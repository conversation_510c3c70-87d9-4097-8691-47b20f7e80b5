<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\ProjectScreen;

use App\Application\Http\Controllers\Api\V2\ProjectScreen\ReorderProjectScreensController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class ProjectScreensReorderRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::put('/projects/{project}/reorder-screens', ReorderProjectScreensController::class)
            ->name('projects.reorderScreens');
    }
}
