<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Admin;

use App\Application\Http\Controllers\Admin\Plan\PlanListingController;
use App\Application\Http\Controllers\Admin\Plan\PlanUpdateController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class PlanRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/plans',
            'as' => 'plans.',
        ], static function (Router $router) {
            $router->get('/', [PlanListingController::class, 'index'])->name('index');
            $router->get('/{plan}', [PlanUpdateController::class, 'show'])->name('show');
            $router->get('/{plan}/edit', [PlanUpdateController::class, 'edit'])->name('edit');
            $router->put('/{plan}', [PlanUpdateController::class, 'update'])->name('update');
        });
    }
}
