<?php

namespace App\Application\Http\Requests\Api\Auth;

use Illuminate\Foundation\Http\FormRequest;

class LoginRequest extends FormRequest
{
    public static function getRules()
    {
        return [
            'email' => ['required', 'email', 'exists:users,email', 'uniqueUserInactive'],
            'password' => ['required'],
            'remember' => ['sometimes', 'boolean'],
        ];
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return static::getRules();
    }
}
