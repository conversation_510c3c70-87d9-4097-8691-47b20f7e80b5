<?php

namespace App\Application\Providers;

use App\Services\Sanitizer\ProjectScreen\FontSanitizer;
use App\Services\Sanitizer\ProjectScreen\Param\ProjectScreenParamSanitizer;
use App\Services\Sanitizer\ProjectScreen\Param\SubtitleSanitizer;
use App\Services\Sanitizer\ProjectScreen\Param\TextareaSanitizer;
use App\Services\Sanitizer\ProjectScreen\TextSizeSanitizer;
use App\Services\Wysiwyg\WysiwygFeature;
use App\Services\Wysiwyg\WysiwygSanitizer;
use Illuminate\Foundation\Application;
use Illuminate\Support\ServiceProvider;

class ParameterSanitizerProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind(TextareaSanitizer::class, function (Application $app) {
            return new TextareaSanitizer(
                $app[WysiwygFeature::class],
                $app[WysiwygSanitizer::class],
                $app[TextSizeSanitizer::class],
                $app[FontSanitizer::class]
            );
        });
        $this->app->bind(SubtitleSanitizer::class, function (Application $app) {
            return new SubtitleSanitizer(
                $app[WysiwygFeature::class],
                $app[WysiwygSanitizer::class],
                $app[TextSizeSanitizer::class],
                $app[FontSanitizer::class]
            );
        });

        $this->app->tag(
            [
                TextareaSanitizer::class,
                SubtitleSanitizer::class,
            ],
            'projectScreenParamSanitizers'
        );

        $this->app->bind(
            ProjectScreenParamSanitizer::class,
            function (Application $app) {
                return new ProjectScreenParamSanitizer($app->tagged('projectScreenParamSanitizers'));
            }
        );
    }
}
