<?php

namespace App\Application\Providers;

use App\Application\Facades\AdminForm as AdminFormFacade;
use App\Application\Facades\AdminShow as AdminShowFacade;
use App\Application\Html\AdminForm;
use App\Application\Html\MenuParser;
use App\Application\Html\Show;
use Collective\Html\FormFacade;
use Collective\Html\HtmlFacade;
use Collective\Html\HtmlServiceProvider;
use Illuminate\Contracts\Auth\Access\Gate;
use Illuminate\Contracts\View\Factory as ViewFactory;
use Illuminate\Contracts\View\View;
use Illuminate\Foundation\AliasLoader;
use Illuminate\Foundation\Application;
use Illuminate\Support\ServiceProvider;

class AdminViewHelpersServiceProvider extends ServiceProvider
{
    public function boot(ViewFactory $viewFactory): void
    {
        $this->mergeConfigFrom(base_path('vendor/watson/bootstrap-form/src/config/config.php'), 'bootstrap_form');
        $this->publishes([
            base_path('vendor/watson/bootstrap-form/src/config/config.php') => config_path('bootstrap_form.php'),
        ], 'template');
        $this->loadViewsFrom(resource_path('views/admin'), 'admin');
        $this->addViewComposers($viewFactory);
    }

    public function register(): void
    {
        $this->registerCollective();
        $this->registerForm();
        $this->registerShow();
    }

    private function addViewComposers(ViewFactory $factory): void
    {
        $factory->composer(['admin::partials.datatables'], function (View $view) {
            $columns = collect($view->getData()['columns'])
                ->mapWithKeys(function ($attribute, $key) {
                    if (is_string($key) && is_array($attribute)) {
                        $parameters = $attribute;
                        $attribute = $key;
                    }

                    return [
                        $attribute => array_merge(
                            ['data' => $attribute, 'name' => $attribute],
                            ($parameters ?? [])
                        ),
                    ];
                });

            $config = $view->getData()['config'];
            if (isset($config['has_actions']) && $config['has_actions']) {
                $columns = $columns->merge([
                    'actions' => [
                        'data' => 'actions',
                        'name' => 'actions',
                        'searchable' => false,
                        'orderable' => false,
                    ],
                ]);
            }

            return $view->with([
                'columnsNames' => $columns->keys(),
                'columnsJson' => $columns->values()->toJson(),
            ]);
        });
    }

    private function registerCollective(): void
    {
        $this->app->register(HtmlServiceProvider::class);
        AliasLoader::getInstance()->alias('Html', HtmlFacade::class);
        AliasLoader::getInstance()->alias('Form', FormFacade::class);
    }

    private function registerForm(): void
    {
        $this->app->singleton(AdminFormFacade::ALIAS, function (Application $app) {
            return new AdminForm(
                $app['html'],
                $app['form'],
                $app['config'],
                $app['translator'],
            );
        });
        AliasLoader::getInstance()->alias('AdminForm', AdminFormFacade::class);
    }

    private function registerShow(): void
    {
        $this->app->singleton(AdminShowFacade::ALIAS, function (Application $app) {
            $gate = $app->get(Gate::class);

            return new Show($app['html'], $app['form'], $gate, new MenuParser($gate));
        });
        AliasLoader::getInstance()->alias('AdminShow', AdminShowFacade::class);
    }
}
