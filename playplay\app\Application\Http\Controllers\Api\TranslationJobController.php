<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api;

use App\Application\Http\Requests\Api\Translation\TranslationJobRequest;
use App\Domain\Translation\WorkflowManager;
use App\Models\TranslationJob;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

final class TranslationJobController extends Controller
{
    private WorkflowManager $workflowManager;

    public function __construct(WorkflowManager $workflowManager)
    {
        $this->workflowManager = $workflowManager;
    }

    public function __invoke(
        TranslationJob $job,
        TranslationJobRequest $request,
    ): Response {
        $this->workflowManager->ackTranslationJob($job, $request->input('translated_text'));

        return new Response('OK', SymfonyResponse::HTTP_OK);
    }
}
