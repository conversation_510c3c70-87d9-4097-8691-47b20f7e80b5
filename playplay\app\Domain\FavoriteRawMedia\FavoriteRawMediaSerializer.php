<?php

declare(strict_types=1);

namespace App\Domain\FavoriteRawMedia;

use App\Models\RawMedia;
use App\Services\CloudStorageService;

/**
 * We duplicated toVue trait logic because when we serialize favorites
 * we want to skip slow query done by RawMedia@getPivotUpdatedAtAttribute
 * and related to timestamp serialization.
 */
final class FavoriteRawMediaSerializer
{
    private CloudStorageService $storageService;

    public function __construct(CloudStorageService $storageService)
    {
        $this->storageService = $storageService;
    }

    public function transform(RawMedia $rawMedia): array
    {
        $toVueAttributes = RawMedia::getToVueAttributes();
        unset($toVueAttributes['timestamp']);

        $values = $this->toVue($toVueAttributes, $rawMedia);
        $values['data'] = $rawMedia->data?->toArray();
        $values['status'] = $rawMedia->lastRender?->status->value;
        $values['timestamp'] = null;
        $values['type'] = $rawMedia->type?->value;
        $values['source'] = $rawMedia->source?->value;

        return $values;
    }

    /**
     * Overrides the BaseModel::toVue() method, since we don't want all field for favorites.
     *
     * @see BaseModel::toVue()
     */
    private function toVue(array $toVueAttributes, RawMedia $rawMedia): array
    {
        $values = [];
        foreach ($toVueAttributes as $key => $attr) {
            $finalKey = is_numeric($key) ? $attr : $key;
            $value = $rawMedia->$attr;

            if (is_array($value) || is_object($value)) {
                array_walk_recursive($value, function (&$attributeValue) {
                    $attributeValue = $this->valueToVue($attributeValue);
                });
            }

            $values[$finalKey] = $this->valueToVue($value);
        }

        return $values;
    }

    private function valueToVue($value)
    {
        if (!is_string($value) || !filter_var($value, FILTER_VALIDATE_URL)) {
            return $value;
        }

        return $this->storageService->getCdnUrl($value);
    }
}
