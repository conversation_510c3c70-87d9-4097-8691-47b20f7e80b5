<?php

namespace App\Application\Events;

use App\Models\Renders\RenderProjectHtml;
use Illuminate\Queue\SerializesModels;

class UserGenerateVideoOrRender
{
    use SerializesModels;

    private RenderProjectHtml $renderProject;

    public function __construct(RenderProjectHtml $render)
    {
        $this->renderProject = $render;
    }

    public function getRenderProject(): RenderProjectHtml
    {
        return $this->renderProject;
    }
}
