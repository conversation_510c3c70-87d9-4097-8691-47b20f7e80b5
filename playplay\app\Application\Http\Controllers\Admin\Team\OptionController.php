<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Team;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Models\Screen\Parameters\Option;
use App\Models\Team;
use Illuminate\Contracts\Cache\Repository as CacheRepository;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

final class OptionController extends BaseController
{
    private CacheRepository $cacheRepository;

    public function __construct(CacheRepository $cacheRepository)
    {
        $this->cacheRepository = $cacheRepository;
    }

    public function disable(Team $team, Option $option): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);

        $team->options()->detach($option->id);

        $this->cacheRepository->tags(['api', 'screens'])->forget(route('api.v2.team.screens.index', $team->id));

        return new JsonResponse(['status' => 'ok'], Response::HTTP_OK);
    }

    public function enable(Team $team, Option $option): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);

        $team->options()->syncWithoutDetaching([$option->id]);

        $this->cacheRepository->tags(['api', 'screens'])->forget(route('api.v2.team.screens.index', $team->id));

        return new JsonResponse(['status' => 'ok'], Response::HTTP_OK);
    }
}
