<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\V2\ShareableLink\ShareableLinkCommentController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class ShareableLinkCommentRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([], function (Router $router) {
            $this->mapperResource($router);
        });
    }

    private function mapperResource(Router $router): void
    {
        $router->resource('shareable-links.comments', ShareableLinkCommentController::class)
            ->only(['store', 'update', 'destroy']);
    }
}
