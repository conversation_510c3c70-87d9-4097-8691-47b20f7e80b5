<?php

declare(strict_types=1);

namespace App\Domain\Processing\Job;

use App\Models\RenderJob;
use App\Services\CloudStorageService;
use Illuminate\Contracts\Routing\UrlGenerator;

class ProcessingJobMessageFactory
{
    private UrlGenerator $urlGenerator;
    private CloudStorageService $gcs;

    public function __construct(UrlGenerator $urlGenerator, CloudStorageService $gcs)
    {
        $this->urlGenerator = $urlGenerator;
        $this->gcs = $gcs;
    }

    public function create(RenderJob $renderJob): ProcessingJobMessage
    {
        return new ProcessingJobMessage(
            $renderJob->getAction(),
            $this->gcs->getFolderUrl($this->getOriginalUrl($renderJob)),
            $this->urlGenerator->route('api.job.callback-media-workflow', ['renderJob' => $renderJob]),
            $renderJob->data
        );
    }

    private function getOriginalUrl(RenderJob $renderJob): string
    {
        return $renderJob->data['objectUrl'] ?? '';
    }
}
