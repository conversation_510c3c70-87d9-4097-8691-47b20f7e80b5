<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Admin;

use App\Application\Http\Controllers\Admin\Option\OptionController;
use App\Application\Http\Controllers\Admin\Option\OptionListCategoryController;
use App\Application\Http\Controllers\Admin\Option\OptionListController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class OptionRouteMapper implements RouteMapper
{
    public function map(): void
    {
        $this->mapOptionListCategories();
        $this->mapOptionLists();
        $this->mapOptions();
    }

    private function mapOptionListCategories(): void
    {
        Route::group([
            'prefix' => '/option-list-categories',
            'as' => 'option-list-categories.',
        ], static function (Router $router) {
            $router->get('/create', [OptionListCategoryController::class, 'create'])->name('create');
            $router->get('/{optionListCategory}/edit', [OptionListCategoryController::class, 'edit'])
                ->name('edit');
            $router->get('/', [OptionListCategoryController::class, 'index'])->name('index');
            $router->post('/store', [OptionListCategoryController::class, 'store'])->name('store');
            $router->put('/{optionListCategory}/update', [OptionListCategoryController::class, 'update'])
                ->name('update');
            $router->put('/reorder', [OptionListCategoryController::class, 'reorder'])->name('reorder');
        });
    }

    private function mapOptionLists(): void
    {
        Route::group([
            'prefix' => '/option-lists',
            'as' => 'option-lists.',
        ], static function (Router $router) {
            $router->get('/create', [OptionListController::class, 'create'])->name('create');
            $router->post('/store', [OptionListController::class, 'store'])->name('store');
            $router->get('/{optionList}/edit', [OptionListController::class, 'edit'])->name('edit');
            $router->delete('/{optionList}', [OptionListController::class, 'destroy'])->name('destroy');
            $router->put('/{optionList}/update', [OptionListController::class, 'update'])->name('update');
            $router->put('/{optionList}/reorder', [OptionListController::class, 'reorder'])->name('reorder');
            $router->get('/{optionList}/danger-zone', [OptionListController::class, 'showDangerZone'])
                ->name('danger-zone');
        });
    }

    private function mapOptions(): void
    {
        Route::group([
            'prefix' => '/options',
            'as' => 'options.',
        ], static function (Router $router) {
            $router->get('/create', [OptionController::class, 'create'])->name('create');
            $router->post('/store', [OptionController::class, 'store'])->name('store');
            $router->get('/{option}/edit', [OptionController::class, 'edit'])->name('edit');
            $router->put('/{option}/update', [OptionController::class, 'update'])->name('update');
            $router->get('/{option}/danger-zone', [OptionController::class, 'showDangerZone'])->name('danger-zone');
            $router->delete('/{option}', [OptionController::class, 'destroy'])->name('destroy');
        });
    }
}
