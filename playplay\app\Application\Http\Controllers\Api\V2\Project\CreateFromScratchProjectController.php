<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Project;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Project\ProjectStoreFromScratchRequest;
use App\Domain\Project\ProjectFactory;
use App\Models\User;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class CreateFromScratchProjectController extends BaseController
{
    use AuthorizesRequests;

    private Guard $guard;
    private ProjectFactory $projectFactory;

    public function __construct(Guard $guard, ProjectFactory $projectFactory)
    {
        $this->guard = $guard;
        $this->projectFactory = $projectFactory;
    }

    public function __invoke(ProjectStoreFromScratchRequest $request): JsonResponse
    {
        /** @var User $user */
        $user = $this->guard->user();

        $project = $this->projectFactory->createFromScratch(
            $user,
            $request->get('format'),
            $request->get('preset_id'),
            $request->get('outro_id')
        );

        return $this->sendJsonResponse(new Collection([$project]), Response::HTTP_CREATED);
    }
}
