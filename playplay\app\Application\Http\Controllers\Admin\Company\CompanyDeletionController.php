<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Company;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Models\Company;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

final class CompanyDeletionController extends BaseController
{
    public function __construct()
    {
        $this->authorizeResource(Company::class);
    }

    public function destroy(Company $company): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $company);

        if ($company->users->isEmpty()) {
            $company->delete();

            return new JsonResponse(['success' => true, 'redirect' => route('admin.companies.index')]);
        }

        return new JsonResponse([
            'title' => trans('message.failed'),
            'message' => trans('message.delete_users'),
            'type' => 'warning',
        ], Response::HTTP_BAD_REQUEST);
    }

    public function showDangerZone(Company $company): View
    {
        $this->authorize('canAccessRestrictedData', $company);

        return view('admin.companies.danger-zone', ['company' => $company]);
    }
}
