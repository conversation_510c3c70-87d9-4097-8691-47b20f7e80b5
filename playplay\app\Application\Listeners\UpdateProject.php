<?php

declare(strict_types=1);

namespace App\Application\Listeners;

use App\Application\Events\ProjectUpdated;

final class UpdateProject
{
    public function handle(ProjectUpdated $event): void
    {
        if ($event->shouldRerenderProject()) {
            $project = $event->getProject();
            $project->render_up_to_date = false;
            $project->snapshot_up_to_date = false;
            $project->save();
        }
    }
}
