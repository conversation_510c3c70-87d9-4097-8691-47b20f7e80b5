<?php

declare(strict_types=1);

namespace App\Application\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Http\Request;
use Illuminate\Routing\ResponseFactory;
use Symfony\Component\HttpFoundation\Response;

final class CheckInactiveCompany
{
    private Guard $guard;
    private ResponseFactory $factory;

    public function __construct(Guard $guard, ResponseFactory $responseFactory)
    {
        $this->guard = $guard;
        $this->factory = $responseFactory;
    }

    public function handle(Request $request, Closure $next)
    {
        /** @var ?User $user */
        $user = $this->guard->user();

        if ($user === null) {
            return $next($request);
        }

        if ($this->userCompanyIsInactive($user)) {
            return $this->factory->make(['company_type' => $user->company->type], Response::HTTP_PAYMENT_REQUIRED);
        }

        return $next($request);
    }

    private function userCompanyIsInactive(User $user): bool
    {
        return !$user->company->isActive();
    }
}
