<?php

declare(strict_types=1);

namespace App\Application\Listeners;

use App\Application\Events\RenderInError;
use App\Application\Mail\RenderCanceled;
use App\Domain\Config\ConfigRepository;
use Illuminate\Support\Facades\Mail;

final class AlertRenderInError
{
    private ConfigRepository $configRepository;

    public function __construct(ConfigRepository $configRepository)
    {
        $this->configRepository = $configRepository;
    }

    public function handle(RenderInError $event): void
    {
        $emails = $this->configRepository->getArray('emails_on_video_error');

        Mail::to($emails)->send(new RenderCanceled($event->getRenderError()));
    }
}
