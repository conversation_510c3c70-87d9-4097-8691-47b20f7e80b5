<?php

declare(strict_types=1);

namespace App\Domain\CutawayShot;

use App\Domain\RawMedia\RawMediaType;
use App\Domain\Render\RenderMedia\Crop\Crop;
use App\Domain\Render\RenderMedia\TargetDimension;
use JsonSerializable;

final class CutawayShotImageRenderMediaData implements JsonSerializable
{
    public readonly Crop $crop;
    public readonly float $duration;
    public readonly bool $keepSize;
    public readonly string $rawMediaType;
    public readonly float $start;
    public readonly TargetDimension $targetDimension;

    public function __construct(
        Crop $crop,
        float $duration,
        bool $keepSize,
        float $start,
        TargetDimension $targetDimension,
    ) {
        $this->crop = $crop;
        $this->duration = $duration;
        $this->keepSize = $keepSize;
        $this->rawMediaType = RawMediaType::IMAGE;
        $this->start = $start;
        $this->targetDimension = $targetDimension;
    }

    public function isEqualTo(
        CutawayShotImageRenderMediaData|CutawayShotVideoRenderMediaData|null $renderMediaData
    ): bool {
        if ($renderMediaData === null) {
            return false;
        }

        return $this->crop->isEqualTo($renderMediaData->crop)
            && $this->duration === $renderMediaData->duration
            && $this->keepSize === $renderMediaData->keepSize
            && $this->start === $renderMediaData->start
            && $this->targetDimension->isEqualTo($renderMediaData->targetDimension);
    }

    public function jsonSerialize(): array
    {
        return [
            'type' => $this->rawMediaType,
            'maxWidth' => $this->targetDimension->width,
            'maxHeight' => $this->targetDimension->height,
            'crop' => $this->crop->toArray(),
            'legacyKeepSize' => $this->keepSize,
            'start' => $this->start,
            'duration' => $this->duration,
        ];
    }
}
