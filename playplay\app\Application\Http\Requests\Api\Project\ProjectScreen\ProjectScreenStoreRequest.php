<?php

namespace App\Application\Http\Requests\Api\Project\ProjectScreen;

use App\Domain\Screen\ScreenCoverType;
use App\Models\Project;
use App\Models\Screen;
use App\Models\ScreenCover;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Factory;

class ProjectScreenStoreRequest extends FormRequest
{
    public function __construct()
    {
        $validationFactory = app(Factory::class);

        $this->checkOrderIsCorrect($validationFactory);
        $this->checkScreenIsCorrect($validationFactory);
        parent::__construct();
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function checkOrderIsCorrect(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'orderIsCorrect',
            function ($attribute, $value, $parameters) {
                /** @var Project $project */
                $project = request()?->route('project');
                $lastOrder = collect($project->projectScreens)->max('order');

                if ($value > $lastOrder + 1) {
                    return false;
                }

                return true;
            }
        );
    }

    public function checkScreenIsCorrect(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'screenIsCorrect',
            function ($attribute, $value, $parameters) {
                $screen = Screen::find($value);

                if ($screen === null) {
                    return false;
                }

                // Screen is public (has a cover image)
                $screenCover = ScreenCover::where('screen_id', $screen->id)
                    ->where('type', ScreenCoverType::IMAGE)
                    ->first();
                if ($screenCover === null) {
                    return false;
                }

                return true;
            }
        );
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'screen_id' => ['required', 'screenIsCorrect'],
            'order' => ['required', 'numeric', 'min:0', 'orderIsCorrect'],
        ];
    }
}
