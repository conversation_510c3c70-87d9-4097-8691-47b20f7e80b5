<?php

namespace App\Application\Http\Requests\Api\Project\ProcessedMedia\Store;

use App\Application\Http\Requests\Api\Project\ProcessedMedia\ProcessedMediaAudioRequest;
use App\Models\ProcessedMedia;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Request;
use Illuminate\Routing\Route;

class ProcessedMediaAudioSplitRequest extends ProcessedMediaAudioRequest
{
    private const MIN_SPLIT_DURATION_IN_SECONDS = 1;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'split_time' => ['required', 'numeric', 'min:1'],
        ];
    }

    public function withValidator(Validator $validator): void
    {
        parent::withValidator($validator);
        $validator->after(function ($validator) {
            if (!$this->isValidSplitTime()) {
                $validator->addFailure('split_time', 'is_valid_split_time');
            }
        });
    }

    private function isValidSplitTime(): bool
    {
        /** @var Request $request */
        $request = request();
        /** @var ProcessedMedia $processedMedia */
        $processedMedia = $request->route('processedMedia');
        $renderMediaData = $processedMedia->lastRender->data;
        $splitTime = $request->get('split_time');

        return (
            ($renderMediaData->getStart() + $renderMediaData->getTrimEnd() -
                $renderMediaData->getTrimStart() - self::MIN_SPLIT_DURATION_IN_SECONDS > $splitTime)
            && ($renderMediaData->getStart() + self::MIN_SPLIT_DURATION_IN_SECONDS < $splitTime)
        );
    }
}
