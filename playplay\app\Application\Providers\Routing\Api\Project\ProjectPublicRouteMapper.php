<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Project;

use App\Application\Http\Controllers\Api\V2\Project\ProjectController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class ProjectPublicRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'middleware' => 'custom-company-auth'
        ], static function (Router $router) {
            $router->get('/projects', [ProjectController::class, 'index'])->name('projects.index');
            /**
             * We temporarily create a second endpoint for Ouest-FR
             * This endpoint is the same as the api.v2.projects.index endpoint we already have
             */
            $router->get('company/projects', [ProjectController::class, 'companyIndex'])
                ->name('company.projects.index');
        });
    }
}
