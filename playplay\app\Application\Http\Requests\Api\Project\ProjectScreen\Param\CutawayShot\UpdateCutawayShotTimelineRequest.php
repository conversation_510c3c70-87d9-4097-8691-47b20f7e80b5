<?php

namespace App\Application\Http\Requests\Api\Project\ProjectScreen\Param\CutawayShot;

use App\Domain\RawMedia\RawMediaType;
use App\Domain\Render\RenderMedia\Transformation\KeepSizeValue;
use Illuminate\Validation\Rule;

class UpdateCutawayShotTimelineRequest extends CutawayShotRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'items' => ['required', 'array', 'areValidCutawayShotTimelineItemsDuration', 'isValidCutawayShotTimeline'],
            'items.*.processed_media_id' => 'required',
            'items.*.start' => ['required', 'numeric', 'min:0'],
            'items.*.type' => [
                'required',
                Rule::in([
                    RawMediaType::IMAGE,
                    RawMediaType::VIDEO,
                ]),
            ],
            'items.*.crop' => ['required', 'crop'],
            'items.*.keep_size' => ['present', 'nullable', Rule::in([true, false, ...KeepSizeValue::getAllValues()])],

            // for images only
            'items.*.duration' => [
                'required_without:items.*.trim_start,items.*.trim_end',
                'numeric',
            ],

            // for videos only
            'items.*.trim_start' => ['required_without:items.*.duration', 'numeric', 'min:0'],
            'items.*.trim_end' => ['required_without:items.*.duration', 'numeric'],

            // projectScreenParam options
            'items.*.options' => ['present', 'array'],
            'items.*.options.loop' => ['sometimes', 'boolean'],
            'items.*.options.zoom' => ['sometimes', 'boolean'],
            'items.*.options.dark_filter' => ['sometimes', 'nullable', 'numeric', 'between:0,1'],
            'items.*.options.credit' => ['sometimes', 'string'],
        ];
    }
}
