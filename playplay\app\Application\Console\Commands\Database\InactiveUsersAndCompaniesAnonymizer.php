<?php

declare(strict_types=1);

namespace App\Application\Console\Commands\Database;

use App\Domain\Anonymizer\AnonymizableInterface;
use App\Domain\Anonymizer\CompanyDataAnonymizer;
use App\Domain\Anonymizer\DataAnonymizerException;
use App\Infrastructure\Company\Repositories\InactiveCompanyRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Throwable;

final class InactiveUsersAndCompaniesAnonymizer extends Command
{
    protected $signature = 'db:inactive-anonymize';

    protected $description = 'Anonymize inactive users and companies data';

    /**
     * @var AnonymizableInterface
     */
    private $userAnonymizer;

    /**
     * @var InactiveCompanyRepository
     */
    private $inactiveCompanyRepository;

    /**
     * @var CompanyDataAnonymizer
     */
    private $companyAnonymizer;

    public function __construct(
        AnonymizableInterface $userAnonymizer,
        CompanyDataAnonymizer $companyAnonymizer,
        InactiveCompanyRepository $inactiveCompanyRepository
    ) {
        parent::__construct();
        $this->userAnonymizer = $userAnonymizer;
        $this->companyAnonymizer = $companyAnonymizer;
        $this->inactiveCompanyRepository = $inactiveCompanyRepository;
    }

    public function handle(): int
    {
        $start = now();
        $this->line('-----');
        $this->line("Start : {$start}");

        $this->anonymizeUsers();
        $this->anonymizeCompanies();

        $end = now();
        $this->line("End : {$end}");
        $this->line("Duration : {$end->diff($start)->format('%hh%im%ss')}");

        return 0;
    }

    private function anonymizeCompanies(): void
    {
        foreach ($this->inactiveCompanyRepository->getAll() as $company) {
            try {
                DB::beginTransaction();
                $this->companyAnonymizer->anonymize($company);
                DB::commit();
            } catch (DataAnonymizerException $exception) {
                DB::rollBack();
                $this->error($exception->getMessage());
            }
        }

        $this->info('Inactive Companies anonymization complete');
    }

    private function anonymizeUsers(): void
    {
        try {
            DB::beginTransaction();
            $this->userAnonymizer->anonymize();
            DB::commit();
            $this->info('Inactive Users anonymization complete');
        } catch (Throwable $exception) {
            DB::rollBack();
            $this->error('Inactive Users anonymization failed');
            $this->error($exception->getMessage());
        }
    }
}
