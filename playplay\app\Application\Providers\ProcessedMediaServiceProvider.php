<?php

namespace App\Application\Providers;

use App\Domain\ProcessedMedia\Repository\ProcessedMediaRepository;
use App\Domain\ProcessedMedia\Transformations\TransformationsFactoryInterface;
use App\Infrastructure\ProcessedMedia\Repository\EloquentProcessedMediaRepository;
use App\Infrastructure\ProcessedMedia\Transformations\TransformationsFactory;
use Illuminate\Support\ServiceProvider;

class ProcessedMediaServiceProvider extends ServiceProvider
{
    /**
     * Register the binding
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind(ProcessedMediaRepository::class, EloquentProcessedMediaRepository::class);
        $this->app->bind(TransformationsFactoryInterface::class, TransformationsFactory::class);
    }
}
