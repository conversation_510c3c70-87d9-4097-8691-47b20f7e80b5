<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Admin;

use App\Application\Http\Controllers\Admin\RenderJobController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class RenderJobRouteMapper implements RouteMapper
{

    public function map(): void
    {
        Route::get('/renderJobs/{renderJob}', [RenderJobController::class, 'show'])->name('renderJobs.show');
    }
}
