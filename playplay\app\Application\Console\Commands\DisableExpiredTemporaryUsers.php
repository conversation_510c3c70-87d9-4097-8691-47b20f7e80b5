<?php

declare(strict_types=1);

namespace App\Application\Console\Commands;

use App\Models\User;
use DateTimeImmutable;
use Illuminate\Console\Command;

final class DisableExpiredTemporaryUsers extends Command
{
    protected $signature = 'check:expired-temp-users';

    protected $description = 'Check temporary users whose validity end date has expired, and disable them';

    public function handle(): int
    {
        User::query()->whereDate('user_until', '<', new DateTimeImmutable())
            ->where('deleted_at', null)
            ->delete();

        return 0;
    }
}
