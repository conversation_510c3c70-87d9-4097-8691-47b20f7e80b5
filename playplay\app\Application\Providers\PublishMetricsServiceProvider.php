<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Metrics\Calculator;
use App\Domain\Metrics\MetricsPublisher;
use App\Domain\Metrics\MetricsRepository;
use App\Domain\Metrics\PublishMetricsService;
use App\Domain\Queueing\QueueMetadataService;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Support\ServiceProvider;

final class PublishMetricsServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(PublishMetricsService::class, function (Application $app) {
            return new PublishMetricsService(
                $app->make(Calculator::class),
                $app->make(MetricsRepository::class),
                $app->make(MetricsPublisher::class),
                array_map(
                    static fn(string $queueName) => $app->makeWith(
                        QueueMetadataService::class,
                        ['queueName' => $queueName]
                    ),
                    ['queue_processing', 'queue_processing_dlq', 'queue_rendering', 'queue_rendering_dlq']
                ),
            );
        });
    }
}
