<?php

namespace App\Application\Http\Controllers\Admin;

use App\Application\Http\Requests\Admin\Config\ConfigStoreRequest;
use App\Application\Http\Requests\Admin\Config\ConfigUpdateRequest;
use App\Models\Config;
use Illuminate\Contracts\View\View;

class ConfigController extends BaseController
{
    public function __construct()
    {
        $this->authorizeResource(Config::class);
    }

    public function create()
    {
        $config = new Config();

        return view('admin.configs.create', compact('config'));
    }

    public function showDangerZone(Config $config): View
    {
        return view('admin.configs.danger-zone', ['config' => $config]);
    }

    public function destroy(Config $config)
    {
        return $this->ajaxDelete($config, route('admin.configs.index'));
    }

    public function edit(Config $config)
    {
        return view('admin.configs.edit', compact('config'));
    }

    public function index()
    {
        $configs = Config::all();

        return view('admin.configs.index', compact('configs'));
    }

    public function store(ConfigStoreRequest $request)
    {
        $config = Config::create($request->all());

        return redirect()->route('admin.configs.edit', $config);
    }

    public function update(Config $config, ConfigUpdateRequest $request)
    {
        $config->update($request->all());

        return redirect()->back();
    }
}
