<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\FreeTrialSignUpController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class FreeTrialSignupRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/free-trial',
            'as' => 'free-trial.',
        ], static function (Router $router) {
            $router->post('/', [FreeTrialSignUpController::class, 'freeTrialSignUp'])
                ->name('sign-up');
        });
    }
}
