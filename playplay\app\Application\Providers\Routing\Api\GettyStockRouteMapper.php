<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\V2\Stocks\GettyStockController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class GettyStockRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/stock/getty',
            'as' => 'stock.getty.',
        ], static function (Router $router) {
            $router->get('/image', [GettyStockController::class, 'images'])->name('image');
            $router->get('/video', [GettyStockController::class, 'videos'])->name('video');
        });
    }
}
