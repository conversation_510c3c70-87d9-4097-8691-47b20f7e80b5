<?php

namespace App\Application\Providers;

use App\Domain\DataSeeding\ModelContainer;
use App\Domain\RawMedia\RawMediaService;
use App\Domain\Template\Repositories\CategoryRepository;
use App\Infrastructure\DataSeeding\Company\ActiveClientCompanySeeder;
use App\Infrastructure\DataSeeding\Company\ActiveFreeTrialCompanySeeder;
use App\Infrastructure\DataSeeding\Company\ExpiredFreeTrialCompanySeeder;
use App\Infrastructure\DataSeeding\Company\InactiveClientCompanySeeder;
use App\Infrastructure\DataSeeding\CompanySeeder;
use App\Infrastructure\DataSeeding\DatabaseSeeder;
use App\Infrastructure\DataSeeding\FeatureSeeder;
use App\Infrastructure\DataSeeding\MusicListSeeder;
use App\Infrastructure\DataSeeding\PlanSeeder;
use App\Infrastructure\DataSeeding\ProjectSeeder;
use App\Infrastructure\DataSeeding\ScreenSeeder;
use App\Infrastructure\DataSeeding\Team\ActiveClientTeamMultiUserSeeder;
use App\Infrastructure\DataSeeding\Team\ActiveClientTeamSeeder;
use App\Infrastructure\DataSeeding\Team\ActiveFreeTrialTeamSeeder;
use App\Infrastructure\DataSeeding\Team\ActiveStandardClientTeamSeeder;
use App\Infrastructure\DataSeeding\Team\ExpiredFreeTrialTeamSeeder;
use App\Infrastructure\DataSeeding\Team\InactiveStandardClientTeamSeeder;
use App\Infrastructure\DataSeeding\TeamFolderSeeder;
use App\Infrastructure\DataSeeding\TeamPresetSeeder;
use App\Infrastructure\DataSeeding\TeamSeeder;
use App\Infrastructure\DataSeeding\TimecodedElement\CompanyFeatureSeeder;
use App\Infrastructure\DataSeeding\TimecodedElement\TimecodedElementsFamilySeeder;
use App\Infrastructure\DataSeeding\User\ActiveClientMultiUserSeeder;
use App\Infrastructure\DataSeeding\User\ActiveClientStandardUserSeeder;
use App\Infrastructure\DataSeeding\User\ActiveClientUserSeeder;
use App\Infrastructure\DataSeeding\User\ActiveFreeTrialUserSeeder;
use App\Infrastructure\DataSeeding\User\ExpiredFreeTrialUserSeeder;
use App\Infrastructure\DataSeeding\User\InactiveClientStandardUserSeeder;
use App\Infrastructure\DataSeeding\User\InactiveFreeTrialUserSeeder;
use App\Infrastructure\DataSeeding\UserSeeder;
use Illuminate\Config\Repository as ConfigRepository;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Support\ServiceProvider;
use Psr\Log\LoggerInterface;

class DatabaseSeederProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(CompanySeeder::class, function () {
            return new CompanySeeder(
                $this->app->get(ModelContainer::class),
                $this->app->get(ActiveFreeTrialCompanySeeder::class),
                $this->app->get(ExpiredFreeTrialCompanySeeder::class),
                $this->app->get(ActiveClientCompanySeeder::class),
                $this->app->get(InactiveClientCompanySeeder::class)
            );
        });
        $this->app->bind(TeamSeeder::class, function () {
            return new TeamSeeder(
                $this->app->get(ModelContainer::class),
                $this->app->get(ActiveFreeTrialTeamSeeder::class),
                $this->app->get(ExpiredFreeTrialTeamSeeder::class),
                $this->app->get(ActiveClientTeamSeeder::class),
                $this->app->get(ActiveClientTeamMultiUserSeeder::class),
                $this->app->get(ActiveStandardClientTeamSeeder::class),
                $this->app->get(InactiveStandardClientTeamSeeder::class),
                $this->app->get(CategoryRepository::class),
            );
        });
        $this->app->bind(MusicListSeeder::class, function () {
            return new MusicListSeeder(
                $this->app->get(ModelContainer::class),
                $this->app->get(RawMediaService::class)
            );
        });
        $this->app->bind(UserSeeder::class, function () {
            return new UserSeeder(
                $this->app->get(ModelContainer::class),
                [
                    $this->app->get(ActiveFreeTrialUserSeeder::class),
                    $this->app->get(InactiveFreeTrialUserSeeder::class),
                    $this->app->get(ExpiredFreeTrialUserSeeder::class),
                    $this->app->get(ActiveClientUserSeeder::class),
                    $this->app->get(ActiveClientMultiUserSeeder::class),
                    $this->app->get(ActiveClientStandardUserSeeder::class),
                    $this->app->get(InactiveClientStandardUserSeeder::class),
                ]
            );
        });
        $this->app->bind(TeamPresetSeeder::class, function () {
            return new TeamPresetSeeder(
                $this->app->get(ModelContainer::class),
                $this->app->get(ConfigRepository::class),
                $this->app->get(RawMediaService::class)
            );
        });
        $this->app->bind(ScreenSeeder::class, function () {
            return new ScreenSeeder($this->app->get(ModelContainer::class));
        });
        $this->app->bind(ProjectSeeder::class, function () {
            return new ProjectSeeder($this->app->get(ModelContainer::class));
        });
        $this->app->bind(PlanSeeder::class, function () {
            return new PlanSeeder($this->app->get(ModelContainer::class));
        });
        $this->app->bind(TeamFolderSeeder::class, function () {
            return new TeamFolderSeeder($this->app->get(ModelContainer::class));
        });

        // Seeders order is important! Make sure to add them in the right order on this list.
        // If you detect a circular dependency, split your seeder in multiple services to avoid it.
        $this->app->tag([
            FeatureSeeder::class,
            PlanSeeder::class,
            CompanySeeder::class,
            MusicListSeeder::class,
            TeamSeeder::class,
            UserSeeder::class,
            TeamFolderSeeder::class,
            ScreenSeeder::class,
            TeamPresetSeeder::class,
            ProjectSeeder::class,
            TimecodedElementsFamilySeeder::class,
            CompanyFeatureSeeder::class,
        ], 'orderedDatabaseSeeders');

        $this->app->bind(DatabaseSeeder::class, function (Application $app) {
            return new DatabaseSeeder(
                $app->get(LoggerInterface::class),
                $app->get('db.connection'),
                $app->tagged('orderedDatabaseSeeders')
            );
        });
    }
}
