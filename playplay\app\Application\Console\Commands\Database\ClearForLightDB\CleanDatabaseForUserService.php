<?php

declare(strict_types=1);

namespace App\Application\Console\Commands\Database\ClearForLightDB;

use App\Models\Company;
use App\Models\User;
use Illuminate\Hashing\HashManager;
use Illuminate\Support\Str;

class CleanDatabaseForUserService
{
    private const PLAYPLAY_COMPANY_NAME = 'PlayPlay';
    private const RANDOM_PASSWORD_LENGTH = 64;

    private HashManager $hashManager;

    public function __construct(HashManager $hashManager)
    {
        $this->hashManager = $hashManager;
    }

    public function deactivateSsoForPlayPlay(): void
    {
        Company::query()
            ->where('name', '=', self::PLAYPLAY_COMPANY_NAME)
            ->update(['sso_enabled' => false]);
    }

    public function changeNullPasswordsToRandomValue(): void
    {
        $randomPassword = $this->hashManager->make(Str::random(self::RANDOM_PASSWORD_LENGTH));

        User::query()
            ->where('password', '=', null)
            ->update([
                'password' => $randomPassword,
            ]);
    }
}
