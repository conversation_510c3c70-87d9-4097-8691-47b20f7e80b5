<?php

namespace App\Application\Http\Requests\Api\Stock;

use Illuminate\Foundation\Http\FormRequest;

class StockSearchRequest extends FormRequest
{
    public static function getRules()
    {
        return [
            'keyword' => ['required'],
            'page' => ['required', 'numeric', 'min:1'],
            'is_edito' => ['sometimes', 'boolean'],
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return static::getRules();
    }
}
