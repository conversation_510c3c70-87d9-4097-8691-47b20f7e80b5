<?php

declare(strict_types=1);

namespace App\Application\Console\Commands;

use App\Application\Mail\FirstRender;
use App\Domain\User\UserRendersRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Throwable;

final class SendFirstVideoMail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mail:first_video';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send mail after first video';

    private UserRendersRepository $repository;

    public function __construct(UserRendersRepository $userRendersRepository)
    {
        parent::__construct();

        $this->repository = $userRendersRepository;
    }

    public function handle(): int
    {
        $usersWithFirstVideo = $this->repository->findUsersThatRenderedTheirFirstProjectYesterday();

        $progressBarSendMail = $this->output->createProgressBar($usersWithFirstVideo->count());
        foreach ($usersWithFirstVideo as $firstRenderUser) {
            try {
                Mail::to($firstRenderUser->getEmail())->send(new FirstRender($firstRenderUser));
            } catch (Throwable $ex) {
                // Don't know what kinds of exceptions could be thrown, so catch everything
                $this->error("There was an unexpected error while sending mails. {$ex->getMessage()}");
            }

            $progressBarSendMail->advance();
        }

        if (count(Mail::failures()) > 0) {
            foreach (Mail::failures() as $failure) {
                $this->error("Failed sending the mail to $failure");
            }
        }

        $progressBarSendMail->finish();

        return 0;
    }
}
