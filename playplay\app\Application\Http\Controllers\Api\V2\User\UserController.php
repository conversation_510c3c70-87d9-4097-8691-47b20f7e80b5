<?php

namespace App\Application\Http\Controllers\Api\V2\User;

use App\Application\Events\AdminCreatedUser;
use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Mappers\UserTeam\TeamAppRoleMapper;
use App\Application\Http\Requests\Api\User\GetRecentlyUsedScreensRequest;
use App\Application\Http\Requests\Api\User\UserStoreRequest;
use App\Application\Http\Requests\Api\User\UserUpdateRequest;
use App\Domain\Localization\CountryCode;
use App\Domain\Localization\CountryCodeRepository;
use App\Domain\Localization\UnableToGetCountryCodeException;
use App\Domain\Permissions\AppRoleRepository;
use App\Domain\Project\ProjectFormat;
use App\Domain\RecentlyUsedScreens\RecentlyUsedScreensService;
use App\Domain\TeamPreset\TeamSerializer;
use App\Domain\User\Action\UserAction;
use App\Domain\User\Action\UserActionService;
use App\Domain\User\Serializer\UserSerializer;
use App\Domain\UserTeam\UserTeamService;
use App\Domain\WaitingGif\WaitingGifRepository;
use App\Models\Project;
use App\Models\Team;
use App\Models\User;
use DateTimeImmutable;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Events\Dispatcher;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

final class UserController extends BaseController
{
    use AuthorizesRequests;

    private Guard $auth;
    private Dispatcher $eventDispatcher;
    private RecentlyUsedScreensService $recentlyUsedScreensService;
    private UserActionService $userActionService;
    private TeamSerializer $teamSerializer;
    private WaitingGifRepository $waitingGifRepository;
    private AppRoleRepository $appRoleRepository;
    private TeamAppRoleMapper $teamAppRoleMapper;
    private UserTeamService $userTeamService;
    private UserSerializer $userSerializer;

    public function __construct(
        Guard                      $auth,
        Dispatcher                 $eventDispatcher,
        UserActionService          $userActionService,
        RecentlyUsedScreensService $recentlyUsedScreensService,
        TeamSerializer             $teamSerializer,
        WaitingGifRepository       $waitingGifRepository,
        AppRoleRepository          $appRoleRepository,
        TeamAppRoleMapper          $userTeamAppRoleMapper,
        UserTeamService            $userTeamService,
        UserSerializer             $userSerializer
    ) {
        $this->auth = $auth;
        $this->eventDispatcher = $eventDispatcher;
        $this->userActionService = $userActionService;
        $this->recentlyUsedScreensService = $recentlyUsedScreensService;
        $this->teamSerializer = $teamSerializer;
        $this->waitingGifRepository = $waitingGifRepository;
        $this->appRoleRepository = $appRoleRepository;
        $this->teamAppRoleMapper = $userTeamAppRoleMapper;
        $this->userTeamService = $userTeamService;
        $this->userSerializer = $userSerializer;
    }

    public function destroy(User $user): JsonResponse
    {
        $this->authorize('destroy', $user);

        $user->delete();

        $this->userActionService->addUserAction(new UserAction('user-deleted'));

        return $this->sendJsonResponse(new Collection([]), JsonResponse::HTTP_NO_CONTENT);
    }

    public function me(): JsonResponse
    {
        return $this->sendJsonResponse(new Collection([$this->userSerializer->serialize($this->auth->user())]), JsonResponse::HTTP_OK);
    }

    public function meCountryCode(Request $request, CountryCodeRepository $countryCodeRepository): JsonResponse
    {
        try {
            $countryCode = $countryCodeRepository->getByIp($request->ip());
        } catch (UnableToGetCountryCodeException $e) {
            $this->eventDispatcher->dispatch($e);
            $countryCode = CountryCode::default();
        }

        return $this->sendJsonResponse($countryCode->toVue(), JsonResponse::HTTP_OK);
    }

    public function meFilters()
    {
        $user = $this->auth->user();
        $users = $user->retrieveTeammate()->sortBy('first_name')->pluck('name', 'id')->toArray();
        $teams = $user->teams->sortBy('name')->pluck('name', 'id')->toArray();
        $formats = ProjectFormat::ALL_FORMATS;
        $status = [Project::STATUS_DRAFT, Project::STATUS_GENERATED];

        return $this->sendJsonResponse(
            new Collection([
                [
                    'users' => $users,
                    'teams' => $teams,
                    'formats' => $formats,
                    'status' => $status,
                ],
            ]),
            JsonResponse::HTTP_OK
        );
    }

    public function mePresets()
    {
        $teams = $this->auth
            ->user()
            ->teams()
            ->with([
                'features',
                'presets',
                'presets.fonts.regular',
                'presets.musicListsMusics.musics.rawMedia.lastRender',
                'presets.musicListsMusics.musics.rawMedia.projects',
                'presets.musicListsMusics.musics.rawMedia.favorites',
                'presets.team',
            ])->get();

        return $this->sendJsonResponse(
            new Collection($this->teamSerializer->serialize($teams->all())),
            JsonResponse::HTTP_OK
        );
    }

    public function recentlyUsedScreensByUserAndTeam(GetRecentlyUsedScreensRequest $request): JsonResponse
    {
        /** @var Team $team */
        $team = Team::query()->find($request->get('team_id'));
        $this->authorize('canAccessRestrictedData', $team->company);
        /** @var User $user */
        $user = $this->auth->user();

        return new JsonResponse(
            $this->recentlyUsedScreensService->findRecentlyUsedScreensByUserAndTeam($user, $team),
            Response::HTTP_OK
        );
    }

    /**
     * @throws AuthorizationException
     * @throws ValidationException
     */
    public function store(UserStoreRequest $request): JsonResponse
    {
        $teamWithAppRoles = $this->teamAppRoleMapper->fromRequest($request);
        $this->authorize('create', [User::class, $teamWithAppRoles]);

        /** @var User $currentUser */
        $currentUser = $this->auth->user();
        $this->checkRolesHierarchy($currentUser, $request->get('teams', []));

        $companyId = Team::query()->find($request->input('teams.0.id'))->company_id;

        $data = $request->only([
            'first_name',
            'last_name',
            'email',
        ]);
        $data['user_from'] = (new DateTimeImmutable())->setTime(0, 0);

        $user = User::whereEmail($data['email'])->withTrashed()->first();
        if ($user) {
            $user->restore();
            $user->update(
                $data + [
                    'company_id' => $companyId,
                    'first_logged_at' => now(),
                ]
            );
            // We sent notification to reset password
            app('auth.password.broker')->sendResetLink(
                $request->only('email')
            );
        } else {
            $user = User::create(
                $data + [
                    'company_id' => $companyId,
                    'language' => $currentUser->language,
                ]
            );
            $this->eventDispatcher->dispatch(new AdminCreatedUser($user));
            $this->userActionService->addUserAction(
                new UserAction('team-user-invited', [], $request->input('teams.0.id'))
            );
        }

        $this->userTeamService->setTeamsWithAppRolesToUser($user, $teamWithAppRoles);

        return $this->sendJsonResponse(
            new Collection([$user->only(['id', 'name', 'email'])]),
            Response::HTTP_CREATED
        );
    }

    public function update(UserUpdateRequest $request)
    {
        $currentUser = $this->auth->user();

        if ($request->has('new_password')) {
            $currentUser->update(['password' => $request->get('new_password')]);
        }

        if ($request->has('language')) {
            $currentUser->update(['language' => $request->get('language')]);
        }

        return $this->sendJsonResponse(new Collection([$currentUser]), JsonResponse::HTTP_OK);
    }

    public function waitingGif(): JsonResponse
    {
        $waitingGif = $this->waitingGifRepository->getRandom();
        if ($waitingGif === null) {
            throw new NotFoundHttpException();
        }

        return $this->sendJsonResponse(new Collection([['url' => $waitingGif->url()]]), Response::HTTP_OK);
    }

    // todo move to a dedicated service
    private function checkRolesHierarchy(User $currentUser, array $destinationTeams): void
    {
        $teamIds = array_column($destinationTeams, 'id');
        $teams = Team::findMany($teamIds);

        foreach ($destinationTeams as $destinationTeam) {
            $teamId = (int)$destinationTeam['id'];
            $team = $teams->filter(function ($team) use ($teamId) {
                return $team->id === $teamId;
            })->first();

            $appRole = $this->appRoleRepository->getById($destinationTeam['app_role_id']);

            if (!$currentUser->hasSufficientRightsToUpdateRole($team, $appRole->name)) {
                throw new AccessDeniedHttpException('Cannot create a higher role user');
            }
        }
    }
}
