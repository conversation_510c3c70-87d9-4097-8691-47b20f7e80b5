<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\MetricsController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class MetricsRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::get('/metrics', [MetricsController::class, 'get'])->name('metrics');
    }
}
