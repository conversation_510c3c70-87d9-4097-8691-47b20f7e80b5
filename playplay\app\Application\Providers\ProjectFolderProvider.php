<?php

namespace App\Application\Providers;

use App\Domain\ProjectFolder\ProjectFolderRepository;
use App\Infrastructure\ProjectFolder\EloquentProjectFolderRepository;
use Illuminate\Support\ServiceProvider;

final class ProjectFolderProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(ProjectFolderRepository::class, EloquentProjectFolderRepository::class);
    }
}
