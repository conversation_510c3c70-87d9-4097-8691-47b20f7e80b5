<?php

namespace App\Application\Http\Controllers\Front;

use App\Application\Http\Controllers\Admin\BaseController;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;

class SsoMobileCallbackController extends BaseController
{
    private Factory $viewFactory;

    public function __construct(Factory $viewFactory)
    {
        $this->viewFactory = $viewFactory;
    }

    public function show(Request $request): View
    {
        return $this->viewFactory->make('sso.show', [
            'code' => $request->get('code'),
        ]);
    }
}
