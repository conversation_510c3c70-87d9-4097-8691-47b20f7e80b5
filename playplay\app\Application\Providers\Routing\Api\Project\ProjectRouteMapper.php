<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Project;

use App\Application\Http\Controllers\Api\V2\Project\GetProjectController;
use App\Application\Http\Controllers\Api\V2\Project\GetProjectTimelineListController;
use App\Application\Http\Controllers\Api\V2\Project\ProjectSettingController;
use App\Application\Http\Controllers\Api\V2\Project\ProjectUploadedFileController;
use App\Application\Http\Controllers\Api\V2\Project\UpdateProjectTransitionsController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class ProjectRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/projects',
            'as' => 'projects.',
        ], static function () {
            Route::group([
                'prefix' => '/{project}',
            ], static function (Router $router) {
                $router->get('/', GetProjectController::class)->name('show');
                $router->get('/uploaded-files/{relationTypeKey?}', ProjectUploadedFileController::class)
                    ->name('uploadedFiles');
                $router->put('/transitions', UpdateProjectTransitionsController::class)->name('update-transitions');
                $router->get('/settings', [ProjectSettingController::class, 'index'])->name('settings.index');
                $router->get('/timelines/{relationTypeKey}', GetProjectTimelineListController::class)
                    ->name('timelines.index');
            });
        });
    }
}
