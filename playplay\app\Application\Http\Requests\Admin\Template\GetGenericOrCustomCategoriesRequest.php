<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\Template;

use Illuminate\Foundation\Http\FormRequest;

class GetGenericOrCustomCategoriesRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'type' => ['sometimes', 'string', 'in:generic,custom'],
        ];
    }
}
