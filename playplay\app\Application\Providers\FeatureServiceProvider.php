<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Feature\Repositories\FeatureGroupRepository;
use App\Domain\Feature\Repositories\FeatureRepository;
use App\Infrastructure\Feature\Repositories\EloquentFeatureGroupRepository;
use App\Infrastructure\Feature\Repositories\EloquentFeatureRepository;
use Illuminate\Support\ServiceProvider;

final class FeatureServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(FeatureRepository::class, EloquentFeatureRepository::class);
        $this->app->bind(FeatureGroupRepository::class, EloquentFeatureGroupRepository::class);
    }
}
