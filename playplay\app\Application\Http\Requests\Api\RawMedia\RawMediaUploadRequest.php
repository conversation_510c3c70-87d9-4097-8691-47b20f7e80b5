<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\RawMedia;

use App\Domain\Project\RawMedia\RelationType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

final class RawMediaUploadRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function messages(): array
    {
        return [
            'size.max' => 'Oops your file is too big (maximum allowed is 500 Mb)',
        ];
    }

    public function rules(): array
    {
        return [
            'size' => ['required', 'integer'],
            'hash' => ['required'],
            'extension' => [],
            'project_id' => ['sometimes', 'required', 'exists:projects,id'],
            'bo_upload', ['sometimes', 'boolean'],
            'relation_type_key' => [
                'nullable',
                Rule::in(RelationType::getAllKeys()),
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'relation_type_key' => $this->route('relationTypeKey'),
        ]);
    }
}
