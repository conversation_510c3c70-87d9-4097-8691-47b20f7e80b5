<?php

namespace App\Application\Providers\Render;

use App\Domain\Render\RenderSubtitle\RenderSubtitleRepository;
use App\Infrastructure\Render\RenderSubtitle\EloquentRenderSubtitleRepository;
use Illuminate\Support\ServiceProvider;

final class RenderSubtitleRepositoryProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->bind(RenderSubtitleRepository::class, EloquentRenderSubtitleRepository::class);
    }
}
