<?php

namespace App\Application\Http\Controllers\Api\V2\Project;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\ProjectFolder\GetProjectFolderListRequest;
use App\Domain\Company\Repositories\CompanyRepository;
use App\Domain\ProjectFolder\Criteria\Order;
use App\Domain\ProjectFolder\Criteria\Pagination;
use App\Domain\ProjectFolder\Criteria\Search;
use App\Domain\ProjectFolder\ProjectFolderRepository;
use App\Domain\Team\TeamRepository;
use Illuminate\Contracts\Auth\Access\Gate;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class GetProjectFolderListController extends BaseController
{
    use AuthorizesRequests;

    private const PER_PAGE = 50;

    private ProjectFolderRepository $repository;

    private Guard $guard;

    private CompanyRepository $companyRepository;

    private TeamRepository $teamRepository;

    private Gate $gate;

    public function __construct(
        ProjectFolderRepository $repository,
        CompanyRepository $companyRepository,
        TeamRepository $teamRepository,
        Guard $guard,
        Gate $gate,
    ) {
        $this->repository = $repository;
        $this->companyRepository = $companyRepository;
        $this->teamRepository = $teamRepository;
        $this->guard = $guard;
        $this->gate = $gate;
    }

    public function __invoke(GetProjectFolderListRequest $request): JsonResponse
    {
        $perPage = (int) $request->input('per_page', self::PER_PAGE);
        $page = (int) $request->input('page', 1);
        $teamId = $request->input('team_id');
        $userIdToSearch = $request->input('user_id');
        $isProjectPrivate = $request->input('is_private', false);
        $terms = $request->input('query');
        $folderId = $request->input('folder_id');
        $orderBy = $request->input('order_by', 'updated_at');
        $orderDirection = $request->input('order_direction', 'desc');

        $team = $this->teamRepository->getById($teamId);
        $company = $this->companyRepository->getByTeamId($teamId);
        $connectedUserId = $this->guard->id();
        $this->gate->authorize('canAccessRestrictedData', $company);
        $this->gate->authorize('view', $team);

        $paginator = $this->repository->getPaginator(
            new Pagination($perPage, $page),
            new Order($orderBy, $orderDirection),
            new Search($teamId, $connectedUserId, $userIdToSearch, $terms, $folderId, $isProjectPrivate)
        );

        return $this->sendJsonResponse(new Collection([$paginator]), Response::HTTP_OK);
    }
}
