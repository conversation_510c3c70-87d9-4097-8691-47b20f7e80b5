<?php

namespace App\Application\Rules;

use App\Models\ProjectScreen;
use Illuminate\Contracts\Validation\Rule;

class CheckParamInScreenRule implements Rule
{
    public function passes($attribute, $value)
    {
        // only for param_id attribute
        /** @var ProjectScreen $projectScreen */
        $projectScreen = request()?->route('projectScreen');
        $screen = $projectScreen->screen;
        $screenParamIds = $screen->params->pluck('id')->toArray();

        // Check that all params belongs to screen
        return \in_array($value, $screenParamIds);
    }

    public function message()
    {
        return 'CheckParamInScreenRule is not valid';
    }
}
