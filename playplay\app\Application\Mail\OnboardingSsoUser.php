<?php

declare(strict_types=1);

namespace App\Application\Mail;

use App\Models\User;
use Illuminate\Mail\Mailable;

final class OnboardingSsoUser extends Mailable
{
    private User $user;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    public function build(): self
    {
        return $this->from('<EMAIL>', __('email.from'))
            ->subject(__('email.onboarding_sso_user.subject'))
            ->markdown('emails.onboarding-sso-user', [
                'name' => $this->user->first_name,
                'loginUrl' => $this->getLoginUrl(),
            ]);
    }

    private function getLoginUrl(): string
    {
        return url('app/login?lang=' . $this->user->preferredLocale()) . "#sso";
    }
}
