<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\Screen;

use Illuminate\Foundation\Http\FormRequest;

class GenericOrCustomScreensRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'type' => ['sometimes', 'string', 'in:generic,custom'],
        ];
    }
}
