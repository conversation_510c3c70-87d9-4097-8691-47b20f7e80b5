<?php

declare(strict_types=1);

namespace App\Domain\Processing\Job;

use App\Domain\Processing\ProcessingJobDispatcher;
use App\Models\RenderJob;

class ProcessingJobTransporter
{
    private ProcessingJobDispatcher $processingJobDispatcher;
    private ProcessingJobMessageFactory $processingJobMessageFactory;

    public function __construct(
        ProcessingJobDispatcher $processingJobDispatcher,
        ProcessingJobMessageFactory $processingJobMessageFactory
    ) {
        $this->processingJobDispatcher = $processingJobDispatcher;
        $this->processingJobMessageFactory = $processingJobMessageFactory;
    }

    public function send(RenderJob $renderJob): void
    {
        $this->processingJobDispatcher->send($this->processingJobMessageFactory->create($renderJob));
    }
}
