<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\Option;

use Illuminate\Foundation\Http\FormRequest;

final class UpdateOptionRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'label' => ['required', 'string', 'max:45'],
            'value' => ['sometimes', 'isValidOptionValue'],
            'thumbnail' => ['sometimes', 'url', 'max:255', 'isValidThumbnailFileExtension'],
        ];
    }
}
