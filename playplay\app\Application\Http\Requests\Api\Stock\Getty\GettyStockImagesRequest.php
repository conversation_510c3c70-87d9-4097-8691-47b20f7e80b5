<?php

namespace App\Application\Http\Requests\Api\Stock\Getty;

use Illuminate\Foundation\Http\FormRequest;

class GettyStockImagesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    private const AGE_OF_PEOPLE = [
        'newborn',
        'baby',
        'child',
        'teenager',
        'young_adult',
        'adult',
        'adults_only',
        'mature_adult',
        'senior_adult',
        '0-1_months',
        '2-5_months',
        '6-11_months',
        '12-17_months',
        '18-23_months',
        '2-3_years',
        '4-5_years',
        '6-7_years',
        '8-9_years',
        '10-11_years',
        '12-13_years',
        '14-15_years',
        '16-17_years',
        '18-19_years',
        '20-24_years',
        '20-29_years',
        '25-29_years',
        '30-34_years',
        '30-39_years',
        '35-39_years',
        '40-44_years',
        '40-49_years',
        '45-49_years',
        '50-54_years',
        '50-59_years',
        '55-59_years',
        '60-64_years',
        '60-69_years',
        '65-69_years',
        '70-79_years',
        '80-89_years',
        '90_plus_years',
        '100_over',
    ];

    private const ETHNICITY = [
        'black',
        'caucasian',
        'east_asian',
        'hispanic_latino',
        'japanese',
        'middle_eastern',
        'mixed_race_person',
        'multiethnic_group',
        'native_american_first_nations',
        'pacific_islander',
        'south_asian',
        'southeast_asian',
    ];

    private const NUMBER_OF_PEOPLE = [
        'none',
        'one',
        'two',
        'group',
    ];

    private const ORIENTATIONS = [
        'horizontal',
        'vertical',
        'square',
        'panoramic_horizontal',
        'panoramic_vertical',
    ];

    private const COMPOSITIONS = [
        'abstract',
        'candid',
        'close_up',
        'copy_space',
        'cut_out',
        'full_frame',
        'full_length',
        'headshot',
        'looking_at_camera',
        'macro',
        'portrait',
        'sparse',
        'still_life',
        'three_quarter_length',
        'waist_up',
    ];

    private const GRAPHICAL_STYLES = [
        'fine_art',
        'illustration',
        'photography',
        'vector',
    ];

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'page' => ['sometimes', 'integer', 'min:1'],
            'per_page' => ['sometimes', 'integer', 'min:1'],
            'is_edito' => ['sometimes', 'boolean'],
            'query' => ['sometimes', 'string'],
            'keyword_ids' => ['sometimes', 'string'],
            'age_of_people' => ['sometimes', 'in_many:' . implode(',', self::AGE_OF_PEOPLE)],
            'ethnicity' => ['sometimes', 'in_many:' . implode(',', self::ETHNICITY)],
            'number_of_people' => ['sometimes', 'in_many:' . implode(',', self::NUMBER_OF_PEOPLE)],
            'orientations' => ['sometimes', 'in_many:' . implode(',', self::ORIENTATIONS)],
            'compositions' => ['sometimes', 'in_many:' . implode(',', self::COMPOSITIONS)],
            'graphical_styles' => ['sometimes', 'in_many:' . implode(',', self::GRAPHICAL_STYLES)],
            'color' => ['sometimes', 'hexColorWithoutSharp'],
        ];
    }
}
