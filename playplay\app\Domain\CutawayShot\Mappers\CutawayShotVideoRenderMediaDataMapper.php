<?php

declare(strict_types=1);

namespace App\Domain\CutawayShot\Mappers;

use App\Domain\CutawayShot\CutawayShotVideoRenderMediaData;
use App\Domain\Render\RenderMedia\LegacyRenderMediaDataInterface;
use App\Domain\Render\RenderMedia\TargetDimension;
use App\Domain\Render\RenderMedia\Trim;
use Exception;
use TypeError;

final class CutawayShotVideoRenderMediaDataMapper
{
    public function fromLegacyRenderMediaData(?LegacyRenderMediaDataInterface $data): CutawayShotVideoRenderMediaData
    {
        if ($data === null) {
            throw new InvalidCutawayShotRenderMediaDataMappingException();
        }

        try {
            $targetDimensionData = $data->getTargetDimension();

            return new CutawayShotVideoRenderMediaData(
                $data->getCrop(),
                $data->getKeepSize(),
                $data->getStart(),
                new TargetDimension(
                    $targetDimensionData['height'],
                    $targetDimensionData['width'],
                ),
                new Trim(
                    $data->getTrimStart(),
                    $data->getTrimEnd(),
                )
            );
        } catch (TypeError | Exception $e) {
            throw new InvalidCutawayShotRenderMediaDataMappingException($e);
        }
    }
}
