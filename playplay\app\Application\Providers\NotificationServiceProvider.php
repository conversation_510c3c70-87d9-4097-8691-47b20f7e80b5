<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Notification\NotificationRepository;
use App\Infrastructure\Notification\EloquentNotificationRepository;
use Illuminate\Support\ServiceProvider;

final class NotificationServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(NotificationRepository::class, EloquentNotificationRepository::class);
    }
}
