<?php

namespace App\Application\Policies;

use App\Models\Renders\RenderProjectHtml;
use App\Models\ShareableLink;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class ShareableLinkPolicy
{
    use HandlesAuthorization;

    public function before($user, $ability)
    {
        return $user->is_admin ?: null;
    }

    public function update(User $user, ShareableLink $shareableLink): bool
    {
        return RenderProjectHtml::query()
            ->join('projects', 'render_projects_html.project_id', 'projects.id')
            ->join('teams', 'teams.id', '=', 'projects.team_id')
            ->where('render_projects_html.id', $shareableLink->render_project_html_id)
            ->whereIn('team_id', $user->teams->pluck('id'))
            ->exists();
    }
}
