<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Admin\TimecodedElement;

use App\Application\Http\Controllers\Admin\TimecodedElement\ReorderTimecodedElementsFamilyPresetsController;
use App\Application\Http\Controllers\Admin\TimecodedElement\TimecodedElementsFamilyController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class TimecodedElementsFamilyRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/timecoded-elements-family',
            'as' => 'timecoded-elements-family.',
        ], static function (Router $router) {
            $router->get('/create', [TimecodedElementsFamilyController::class, 'create'])
                ->name('create');
            $router->get('/', [TimecodedElementsFamilyController::class, 'index'])
                ->name('index');
            $router->post('/', [TimecodedElementsFamilyController::class, 'store'])
                ->name('store');

            Route::group([
                'prefix' => '/{timecodedElementsFamily}',
            ], static function (Router $router) {
                (new TimecodedElementPresetRouteMapper())->map();
                $router->get('/edit', [TimecodedElementsFamilyController::class, 'edit'])
                    ->name('edit');
                $router->put('/', [TimecodedElementsFamilyController::class, 'update'])
                    ->name('update');
                $router->delete('/', [TimecodedElementsFamilyController::class, 'destroy'])
                    ->name('destroy');
                $router->get('/danger-zone', [TimecodedElementsFamilyController::class, 'showDangerZone'])
                    ->name('danger-zone');
                $router->put('/reorder-presets', ReorderTimecodedElementsFamilyPresetsController::class)
                    ->name('reorder-presets');
            });
        });
    }
}
