<?php

declare(strict_types=1);

namespace App\Application\Http\Middleware;

use App\Domain\Logs\ModelHistory\ModelHistoryService;
use App\Domain\Request\RequestService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Session\Store;
use Symfony\Component\HttpFoundation\Response;

final class LogModelOperation
{
    private RequestService $requestService;

    private ModelHistoryService $modelHistoryService;

    private Store $session;

    public function __construct(
        RequestService $requestService,
        ModelHistoryService $modelHistoryService,
        Store $session
    ) {
        $this->requestService = $requestService;
        $this->modelHistoryService = $modelHistoryService;
        $this->session = $session;
    }

    public function handle(Request $request, Closure $next)
    {
        return $next($request);
    }

    public function terminate(Request $request, Response $response): void
    {
        $currentUser = $this->session->get('orig_user') ?? $request->user();

        if ($currentUser === null || !$this->requestService->isBackofficeRoute()) {
            return;
        }

        $this->modelHistoryService->sendLogs();
    }
}
