<?php

namespace App\Domain\Company;

use App\Domain\Company\Repositories\CompanyRepository;
use App\Domain\Plan\MissingPlanException;
use App\Domain\Plan\PlanRepository;
use App\Domain\Plan\PlanType;
use App\Models\Company;
use App\Models\Plan;
use App\Models\Team;
use App\Models\User;
use DateTimeImmutable;
use Exception;
use Illuminate\Contracts\Auth\Guard;

class CompanyService
{
    private const FREE_TRIAL_DURATION_DAYS = 7;

    private PlanRepository $planRepository;
    private CompanyRepository $companyRepository;
    private Guard $auth;
    private CompanyFeatureService $companyFeatureService;

    public function __construct(
        PlanRepository $planRepository,
        CompanyRepository $companyRepository,
        CompanyFeatureService $companyFeatureService,
        Guard $auth
    ) {
        $this->planRepository = $planRepository;
        $this->companyRepository = $companyRepository;
        $this->companyFeatureService = $companyFeatureService;
        $this->auth = $auth;
    }

    public function updateCompany(
        Company $company,
        string $name,
        ?int $csmId,
        ?string $clientFrom,
        ?string $clientUntil,
        ?string $type,
        ?int $planId,
        bool $dataIsRestricted,
        string $status,
    ): void {
        $clientUntil = $clientUntil ?? $this->getClientUntilFromNewStatus($company, $status);

        $clientFrom = $clientFrom ?? $this->getClientFromNewStatus($company, $type);

        if ($company->plan_id !== $planId) {
            $dataIsRestricted = $this->shouldRestrictAccessToCompanyData((int) $planId);
        }

        $this->companyRepository->update(
            $company,
            $name,
            $csmId,
            $clientFrom,
            $clientUntil,
            $type,
            $planId,
            $dataIsRestricted,
            $status,
        );
    }

    /**
     * @throws MissingPlanException
     * @throws Exception
     */
    public function createFreeTrialCompanyFromModelCompany(
        Company $modelCompany,
        User $user,
        ?string $companyName = null
    ): Company {
        $newCompany = $modelCompany->replicate();
        $newCompany->name = $companyName ?? explode('@', $user->email)[1];
        $newCompany->status = Company::STATUS_ACTIVE;
        $newCompany->type = Company::TYPE_FREE_TRIAL;
        $newCompany->client_until = new DateTimeImmutable(self::FREE_TRIAL_DURATION_DAYS . ' days');

        $this->applyPlanToCompany($newCompany, Plan::DEFAULT_PLAN_FOR_FREE_TRIALERS);
        $this->companyFeatureService->updateCompanyFeaturesBasedOnItsPlan($newCompany);

        return $newCompany;
    }

    /**
     * @throws MissingPlanException
     */
    public function applyPlanToCompany(Company $company, string $planName): void
    {
        $defaultPlan = $this->planRepository->getPlanByName($planName);

        if ($defaultPlan === null) {
            throw new MissingPlanException(
                "Tried to associate a plan to a company, but the plan '$planName' was not found."
            );
        }

        $planType = PlanType::from($planName);
        if (in_array($planType, [PlanType::FREE_TRIAL, PlanType::STANDARD], true)) {
            $company->data_is_restricted = false;
        }

        $company->plan()->associate($defaultPlan);

        $company->save();
    }

    /** @todo Move this code to a Repository !!! */
    public function updateCompanyTeamsOrder(Company $company, array $teamsOrder): void
    {
        $teamsToReorder = Team::query()
            ->whereIn('id', array_keys($teamsOrder))
            ->where('company_id', $company->id)
            ->get();

        foreach ($teamsToReorder as $team) {
            $team->order = $teamsOrder[$team->id];
            $team->save();
        }
    }

    /**
     * @note To avoid access to UE companies from US PlayPlayers, we set the flag `is_restricted_data` to true.
     *       We consider that we should restrict access to company's data only when
     *       a company is an Enterprise company AND created by a user which doesn't have US_PlayPlayer ROLE.
     */
    public function shouldRestrictAccessToCompanyData(int $planId): bool
    {
        return !$this->auth->user()->roles->pluck('name')->contains('us_playplayer')
            && $this->planRepository->getOneById($planId)?->name === 'enterprise';
    }

    private function getClientUntilFromNewStatus(Company $company, ?string $status): ?string
    {
        if ($status === Company::STATUS_INACTIVE && $company->status === Company::STATUS_ACTIVE) {
            return (new DateTimeImmutable('today midnight'))->format('Y-m-d H:i:s');
        }

        return null;
    }

    private function getClientFromNewStatus(Company $company, ?string $type): ?string
    {
        if ($type === Company::TYPE_CLIENT && $company->type === Company::TYPE_SALES_LEAD) {
            return (new DateTimeImmutable('today midnight'))->format('Y-m-d H:i:s');
        }

        return null;
    }
}
