<?php

namespace App\Application\Html;

use Collective\Html\FormBuilder;
use Collective\Html\HtmlBuilder;
use Illuminate\Contracts\Auth\Access\Gate;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use InvalidArgumentException;
use ReflectionClass;
use Spatie\Menu\Laravel\Menu;

class Show
{
    private ?Model $model = null;
    private FormBuilder $form;
    private HtmlBuilder $html;
    private Gate $gate;
    private MenuParser $menuParser;

    public function __construct(HtmlBuilder $html, FormBuilder $form, Gate $gate, MenuParser $menuParser)
    {
        $this->html = $html;
        $this->form = $form;
        $this->gate = $gate;
        $this->menuParser = $menuParser;
    }

    public function booleanAttribute(
        string $attribute,
        string $trueTextValue = 'Oui',
        string $falseTextValue = 'Non'
    ): string {
        if ($this->model->$attribute) {
            $class = 'success';
            $value = $trueTextValue;
        } else {
            $class = 'danger';
            $value = $falseTextValue;
        }

        return "<div class=\"label label-$class\">$value</div>";
    }

    public function close(): void
    {
        $this->reset();
    }

    public function colorAttribute(string $attribute): string
    {
        return '<div style="background-color: ' . $attribute . '; width: 20px; height:20px"></div>';
    }

    public function createLinkButton(array $options, string $title): ?HtmlString
    {
        if (!$this->gate->allows('create', [$this->model])) {
            return null;
        }

        return $this->linkButton('@create', $title, $options);
    }

    public function createModalButton(array $options, string $title, string $href = '@create'): ?HtmlString
    {
        if (!$this->gate->allows('create', [$this->model])) {
            return null;
        }

        return $this->modalButton([$href, null], $title, $options);
    }

    public function dateAttribute(string $attribute): string
    {
        return $this->model->$attribute->toFormattedDateString();
    }

    public function deleteButton(array $options, string $title, string $nameAttribute, string $redirect): string
    {
        if (!$this->gate->allows('delete', [$this->model])) {
            return '';
        }

        $output = $this->form->open([
            'method' => 'delete',
            'url' => $this->makeUrl('@destroy'),
            'rel' => 'delete-button',
            'style' => 'display:inline',
            'data-toggle' => 'tooltip',
            'data-placement' => 'top',
            'title' => Lang::get('message.action_tooltips.delete'),
        ]);

        $output .= $this->form->hidden('title', $this->model->$nameAttribute);
        $output .= $this->form->hidden('redirect', $this->makeUrl($redirect));
        $output .= $this->form->button($title, array_merge(['class' => 'btn', 'type' => 'submit'], $options));
        $output .= $this->form->close();

        return $output;
    }

    public function editButton(array $options, string $title): string
    {
        if (!$this->gate->allows('update', [$this->model])) {
            return '';
        }

        return $this->linkButton('@edit', $title, $options);
    }

    public function emailAttribute(string $attribute = 'email'): HtmlString
    {
        return link_to('mailto:' . $this->model->$attribute, $this->model->$attribute);
    }

    public function imageAttribute(string $attribute): HtmlString
    {
        return link_to(
            $this->model->$attribute,
            $this->html->image($this->model->$attribute, $attribute),
            ['target' => '_blank']
        );
    }

    public function indexActions(string $nameAttribute = null): string
    {
        $output = $this->showButton(
            ['class' => 'btn btn-info btn-xs', 'style' => 'margin: 0 2px'],
            'link',
        );
        $output .= $this->editButton(
            ['class' => 'btn btn-warning btn-xs', 'style' => 'margin: 0 2px'],
            'link',
        );
        $output .= $this->deleteButton(
            ['class' => 'btn btn-danger btn-xs', 'style' => 'margin: 0 2px'],
            'delete',
            $nameAttribute,
            '@index'
        );

        return $output;
    }

    public function indexButton(array $options, string $title, string $href = '@index'): ?HtmlString
    {
        if (!$this->gate->allows('list', [$this->model])) {
            return null;
        }

        return $this->linkButton($href, $title, $options);
    }

    public function linkButton(string $hrefWithParameters, string $title, array $options = []): HtmlString
    {
        $href = $this->makeUrl($hrefWithParameters);
        $options = array_merge(['class' => 'btn'], $options);

        return link_to($href, $title, $options, null, false);
    }

    public function displaySidebarMenu(string $class = ''): ?Menu
    {
        return $this->menuParser->convertArrayToMenu(MenuConfiguration::ELEMENTS, $class);
    }

    public function modalButton(array $hrefWithParameters, string $title, array $options): HtmlString
    {
        $href = $this->makeUrl($hrefWithParameters);
        $options = array_merge(['data-toggle' => 'modal', 'data-target' => '#modal', 'href' => $href], $options);
        $options = array_merge(['class' => 'btn'], $options);

        return $this->form->button($title, $options);
    }

    /**
     * @param array|string|Model $model
     */
    public function open($model): self
    {
        return $this->reset()->setModel($model);
    }

    public function relationAttribute(
        string $relation,
        string $relationTitleAttribute,
        ?HtmlString $default = null,
        string $actionOrRoute = null
    ): ?HtmlString {
        $relation = $this->model->$relation;
        if (!$relation) {
            return $default;
        }

        return link_to(
            $this->makeUrlWithModel($actionOrRoute, $relation[$relation->getKeyName()], $relation),
            $relation->$relationTitleAttribute
        );
    }

    public function reset(): self
    {
        $clean = new self($this->html, $this->form, $this->gate, $this->menuParser);
        $attributes = (new ReflectionClass(self::class))->getAttributes();
        foreach ($attributes as $attribute) {
            $key = $attribute->getName();
            if (property_exists($clean, $key)) {
                $this->{$key} = $clean->{$key};
            } else {
                unset($this->{$key});
            }
        }

        return $this;
    }

    public function showButton(array $options, string $title): ?HtmlString
    {
        if (!$this->gate->allows('view', [$this->model])) {
            return null;
        }

        return $this->linkButton('@show', $title, $options);
    }

    public function textAttribute(string $attribute): string
    {
        return $this->model->$attribute;
    }

    public function urlAttribute(string $attribute): HtmlString
    {
        return link_to($this->model->$attribute);
    }

    /**
     * @param array|int|null $parameters
     */
    private function createUrl(string $href, Model $model, $parameters): string
    {
        if (Str::startsWith($href, '@')) {
            $href = '\\App\\Application\\Http\\Controllers\\Admin\\' . Str::studly(class_basename($model)) . 'Controller' . $href;

            return action($href, $parameters);
        }

        if (Str::contains($href, '@')) {
            return action($href, $parameters);
        }

        if (Route::has($href)) {
            return route($href, $parameters);
        }

        return url($href);
    }

    /**
     * @param array|string|Model $model
     */
    private function findModel($model): Model
    {
        if (is_array($model)) {
            [$model, $id] = $model;
        }

        if (is_string($model)) {
            $model = new $model;
        }

        if (!($model instanceof Model)) {
            throw new InvalidArgumentException('Le model doit étendre Eloquent\Model');
        }

        if (isset($id)) {
            // TODO why 1 and not $id???
            $model = $model->find(1);
        }

        return $model;
    }

    /**
     * @param array|string|null $hrefWithParameters
     */
    private function makeUrl($hrefWithParameters = null): string
    {
        if (is_array($hrefWithParameters)) {
            [$href, $parameters] = $hrefWithParameters;
        } else {
            $href = $hrefWithParameters;
            $parameters = $this->model[$this->model->getKeyName()];
        }

        if ($href) {
            return $this->createUrl($href, $this->model, $parameters);
        }

        return action('\\App\\Application\\Http\\Controllers\\Admin\\' . Str::studly(class_basename($this->model)) . 'Controller@show', $parameters);
    }

    /**
     * @param array|string|Model $model
     */
    private function makeUrlWithModel(string $href, array $parameters, $model): string
    {
        return $this->createUrl($href, $this->findModel($model), $parameters);
    }

    /**
     * @param array|string|Model $model
     */
    private function setModel($model): self
    {
        $this->model = $this->findModel($model);

        return $this;
    }
}
