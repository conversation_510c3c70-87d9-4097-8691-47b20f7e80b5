<?php

namespace App\Application\Rules;

use Illuminate\Contracts\Validation\Rule;

class RGBColorRule implements Rule
{
    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('validation.rgb-color');
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed  $value
     *
     * @return bool
     */
    public function passes($attribute, $value)
    {
        return preg_match(
            '/^(([0-9]|[1-9][0-9]|1[0-9][0-9]||2[0-4][0-9]|25[0-5]),){2}([0-9]|[1-9][0-9]|0[1-9][0-9]|1[0-9][0-9]||2[0-4][0-9]|25[0-5])$/m',
            $value
        ) === 1;
    }
}
