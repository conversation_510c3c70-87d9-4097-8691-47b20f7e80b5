<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Video\Format\VideoFormatRepository;
use App\Infrastructure\Video\Format\ConfigVideoFormatRepository;
use Illuminate\Support\ServiceProvider;

final class VideoServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(VideoFormatRepository::class, ConfigVideoFormatRepository::class);
    }
}
