<?php

namespace App\Application\Rules;

use App\Domain\Time\Duration;
use Illuminate\Contracts\Validation\Rule;

class MaxDifferenceRule implements Rule
{
    /**
     * Create a new rule instance.
     */
    public function __construct()
    {
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('validation.max-difference');
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed $value
     *
     * @return bool
     */
    public function passes($attribute, $value, $parameters = [], $validator = null)
    {
        if (count($parameters) !== 2 && !is_numeric($value)) {
            return false;
        }

        $data = $validator->getData();
        $comparedToValue = $data[$parameters[0]] ?? null;
        $maxDifference = $parameters[1];

        if (!is_null($comparedToValue) && (is_numeric($value) && is_numeric($maxDifference))) {
            return $this->getDurationInMilliseconds($comparedToValue, $value) <= $maxDifference;
        }

        return false;
    }

    private function getDurationInMilliseconds(float $start, float $end): int
    {
        return (new Duration($end - $start))->toMilliseconds()->getValue();
    }
}
