<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Screen;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\Screen\GenericOrCustomScreensRequest;
use App\Application\Http\Requests\Admin\Screen\ScreenStoreOrUpdateRequest;
use App\Domain\Screen\DuplicateScreenService;
use App\Domain\Screen\ScreenCategoryRepository;
use App\Domain\Screen\ScreenCoverService;
use App\Domain\Screen\ScreenRepository;
use App\Domain\Screen\ScreenService;
use App\Domain\TeamScreen\CacheTeamScreenService;
use App\Models\Screen;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

final class ScreenController extends BaseController
{
    private const GENERIC = 'generic';
    private const CUSTOM = 'custom';
    private ScreenRepository $screenRepository;
    private ScreenCategoryRepository $screenCategoryRepository;
    private ScreenService $screenService;
    private ScreenCoverService $screenCoverService;
    private CacheTeamScreenService $cacheTeamScreenService;
    private DuplicateScreenService $duplicateScreenService;

    public function __construct(
        ScreenRepository $screenRepository,
        ScreenCategoryRepository $screenCategoryRepository,
        ScreenService $screenService,
        ScreenCoverService $screenCoverService,
        CacheTeamScreenService $cacheTeamScreenService,
        DuplicateScreenService $duplicateScreenService
    ) {
        $this->screenRepository = $screenRepository;
        $this->screenCategoryRepository = $screenCategoryRepository;
        $this->screenService = $screenService;
        $this->screenCoverService = $screenCoverService;
        $this->cacheTeamScreenService = $cacheTeamScreenService;
        $this->duplicateScreenService = $duplicateScreenService;
    }

    public function card(Screen $screen): View
    {
        return view('admin.screens.card', ['screen' => $screen]);
    }

    public function create(GenericOrCustomScreensRequest $request): View
    {
        $screen = $this->getScreenFromRequest($request);
        $categories = $this->screenCategoryRepository->getAllCategoryNamesIndexedById();

        return view('admin.screens.create', compact('screen', 'categories'));
    }

    public function destroy(Screen $screen): JsonResponse
    {
        $screenType = $this->getTypeName((bool) $screen->is_generic);
        $this->screenService->deleteAndUpdateDependenciesOf($screen);
        $this->cacheTeamScreenService->forgetCacheForAllTeamsHavingScreenId($screen->id);

        return new JsonResponse([
            'success' => true,
            'redirect' => route('admin.screens.index', ['type' => $screenType])
        ]);
    }

    public function edit(Screen $screen, Request $request): View
    {
        $categories = $this->screenCategoryRepository->getAllCategoryNamesIndexedById();

        return view('admin.screens.edit', [
            'screen' => $screen,
            'categories' => $categories,
            'referer' => $request->get('referer'),
        ]);
    }

    public function index(GenericOrCustomScreensRequest $request): View
    {
        $isGeneric = $this->getScreenFromRequest($request)->is_generic;
        $screens = $this->screenRepository->getPaginatedScreensOrderedById($isGeneric);
        $tabActive = $this->getTypeName((bool) $isGeneric);
        $screens->appends(['type' => $tabActive]);

        return view('admin.screens.index', [
            'tabActive' => $tabActive,
            'screens' => $screens,
            'referer' => $request->getQueryString(),
        ]);
    }

    public function showDangerZone(Screen $screen): View
    {
        return view('admin.screens.danger-zone', ['screen' => $screen]);
    }

    public function store(ScreenStoreOrUpdateRequest $request): RedirectResponse
    {
        if ($request->input('activated_at') === '') {
            $request->merge(['activated_at' => null]);
        }

        if ($request->input('is_generic') === '') {
            $request->merge(['is_generic' => 0]);
        }

        $createdScreen = Screen::query()->create($request->all());
        $this->screenCoverService->saveAllCoversByTypeAndFormat($createdScreen, $request->input('covers'));
        $this->screenService->updateCategoriesOfAScreen($createdScreen, $request->input('category_ids', []));

        return redirect()->route('admin.screens.index', ['type' => $createdScreen->is_generic ? 'generic' : 'custom']);
    }

    public function update(ScreenStoreOrUpdateRequest $request, Screen $screen): RedirectResponse
    {
        if ($request->input('activated_at') === '') {
            $request->merge(['activated_at' => null]);
        }

        if ($request->input('is_generic') === '') {
            $request->merge(['is_generic' => 0]);
        }

        $screen->update($request->all());
        $updatedScreen = $screen->fresh();
        $screenCoversToDelete = (new Collection($request->input('delete_cover')))->filter()->keys()->toArray();
        $this->screenCoverService->delete($screenCoversToDelete);
        $this->screenCoverService->saveAllCoversByTypeAndFormat($updatedScreen, $request->input('covers'));
        $this->screenService->updateCategoriesOfAScreen($updatedScreen, $request->input('category_ids', []));
        $this->cacheTeamScreenService->forgetCacheForAllTeamsHavingScreenId($screen->id);

        $urlParams = $request->get('referer') ?: ['type' => $screen->is_generic ? 'generic' : 'custom'];

        return redirect()->route('admin.screens.index', $urlParams);
    }

    public function duplicate(Screen $screen): RedirectResponse
    {
        $screenType = $this->getTypeName((bool) $screen->is_generic);

        $this->duplicateScreenService->duplicate($screen);

        return redirect()->route('admin.screens.index', ['type' => $screenType]);
    }

    private function getTypeName(bool $isGeneric): string
    {
        return $isGeneric ? self::GENERIC : self::CUSTOM;
    }

    private function getScreenFromRequest(GenericOrCustomScreensRequest $request): Screen
    {
        return new Screen(['is_generic' => $request->get('type') !== 'custom']);
    }
}
