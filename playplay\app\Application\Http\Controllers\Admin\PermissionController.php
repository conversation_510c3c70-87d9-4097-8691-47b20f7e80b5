<?php

namespace App\Application\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Spatie\Permission\Models\Permission;

class PermissionController extends BaseController
{
    public function __construct()
    {
        $this->authorizeResource(Permission::class);
    }

    public function destroyMany(Request $request)
    {
        Permission::destroy($request->only('permissions'));

        alert()->success(trans('message.permissions_remove'), trans('message.it_good'))
            ->confirmButton()->autoclose(7000);

        return redirect()->back();
    }

    protected function resourceAbilityMap(): array
    {
        return ['destroyMany' => 'delete'];
    }

    protected function resourceMethodsWithoutModels(): array
    {
        return ['destroyMany'];
    }
}
