<?php

namespace App\Domain\Logs\ModelHistory;

use DateTimeImmutable;
use Illuminate\Database\Eloquent\Model;

final class ModelHistoryFactory
{
    private ModelHistorizables $modelHistorizables;

    public function __construct(ModelHistorizables $modelHistorizables)
    {
        $this->modelHistorizables = $modelHistorizables;
    }

    /**
     * @throws UnableToHistorizeModelException
     */
    public function create(string $requestId, string $actionName, Model $model): ModelHistory
    {
        return new ModelHistory(
            $requestId,
            $actionName,
            $model->id,
            $model::class,
            $this->modelHistorizables->historize($model),
            (new DateTimeImmutable())->getTimestamp()
        );
    }
}
