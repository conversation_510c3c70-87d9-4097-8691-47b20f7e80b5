<?php

namespace App\Application\Rules;

use App\Models\RawMedia;
use Illuminate\Contracts\Validation\Rule;

class IsGifRule implements Rule
{
    /**
     * Create a new rule instance.
     */
    public function __construct()
    {
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('validation.is_gif');
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed  $value
     *
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if ($attribute !== 'raw_media_id') {
            return false;
        }

        /** @var RawMedia $rawMedia */
        $rawMedia = RawMedia::find($value);

        return $rawMedia !== null && $rawMedia->isGif();
    }
}
