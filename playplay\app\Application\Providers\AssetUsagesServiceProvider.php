<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\AssetUsages\AssetUsageRepository;
use App\Domain\AssetUsages\AssetUsagesSender;
use App\Infrastructure\AssetUsages\GettyBatchSender;
use App\Infrastructure\AssetUsages\GettyRepository;
use Illuminate\Support\ServiceProvider;

class AssetUsagesServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(AssetUsageRepository::class, GettyRepository::class);
        $this->app->bind(AssetUsagesSender::class, GettyBatchSender::class);
    }
}
