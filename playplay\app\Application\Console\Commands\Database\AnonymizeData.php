<?php

declare(strict_types=1);

namespace App\Application\Console\Commands\Database;

use App\Domain\Anonymizer\DataAnonymizer;
use App\Domain\Anonymizer\DataAnonymizerException;
use Illuminate\Console\Command;
use Illuminate\Contracts\Foundation\Application;
use Psr\Log\LoggerInterface;

final class AnonymizeData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:anonymize';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Anonymize users data';

    private DataAnonymizer $anonymizer;
    private Application $app;
    private LoggerInterface $logger;

    public function __construct(
        DataAnonymizer $anonymizerService,
        Application $app,
        LoggerInterface $logger
    ) {
        parent::__construct();
        $this->anonymizer = $anonymizerService;
        $this->app = $app;
        $this->logger = $logger;
    }

    public function handle(): int
    {
        if ($this->app->environment('production')) {
            $this->error('Anonymize command should never be runned on production.');

            return 1;
        }

        $this->call('down');

        try {
            $this->anonymizer->anonymize();
            $this->logger->info('Anonymization complete');
        } catch (DataAnonymizerException $exception) {
            $this->logger->error($exception->getMessage());

            return 1;
        } finally {
            $this->call('up');
        }

        return 0;
    }
}
