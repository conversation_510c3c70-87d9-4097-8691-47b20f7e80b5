<?php

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\V2\ProjectScreen\Param\Subtitles\SubtitleController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class ProjectScreenParamRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::namespace('ProjectScreen\Param\Subtitles')
            ->as('project-screens.params.')
            ->prefix('/project-screens/{projectScreen}/params')
            ->group(static function (Router $router) {
                $router->get('/{paramSubtitle}/subtitles/export', [SubtitleController::class, 'export'])
                    ->name('subtitles.export');
                $router->post('/subtitles/import', [SubtitleController::class, 'import'])
                    ->name('subtitles.import');
            });
    }
}
