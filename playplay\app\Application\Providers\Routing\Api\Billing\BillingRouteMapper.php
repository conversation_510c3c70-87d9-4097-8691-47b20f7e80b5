<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Billing;

use App\Application\Http\Controllers\Api\V2\Billing\BillingController;
use App\Application\Http\Controllers\Api\V2\Billing\BillingPlanController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class BillingRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'as' => 'billing.',
            'prefix' => 'billing',
        ], static function (Router $router): void {
            $router->get('/', [BillingController::class, 'index'])->name('index');
            $router->put('/', [BillingController::class, 'update'])->name('update');
        });

        Route::group([
            'as' => 'billing-plans.',
            'prefix' => 'billing-plans',
        ], static function (Router $router): void {
            $router->get('/', [BillingPlanController::class, 'index'])->name('index');
        });
    }
}
