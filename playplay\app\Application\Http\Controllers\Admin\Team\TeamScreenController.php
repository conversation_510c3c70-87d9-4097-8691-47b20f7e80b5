<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Team;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Models\Screen;
use App\Models\Team;
use Illuminate\Contracts\Cache\Repository as CacheRepository;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

final class TeamScreenController extends BaseController
{
    private CacheRepository $cacheRepository;

    public function __construct(CacheRepository $cacheRepository)
    {
        $this->cacheRepository = $cacheRepository;
    }

    public function disable(Team $team, Screen $screen): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);

        $team->screens()->detach($screen->id);

        $this->cacheRepository->tags(['api', 'screens'])->forget(route('api.v2.team.screens.index', $team->id));

        return new JsonResponse(['status' => 'ok'], Response::HTTP_OK);
    }

    public function enable(Team $team, Screen $screen): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);

        $team->screens()->syncWithoutDetaching([$screen->id]);

        $this->cacheRepository->tags(['api', 'screens'])->forget(route('api.v2.team.screens.index', $team->id));

        return new JsonResponse(['status' => 'ok'], Response::HTTP_OK);
    }
}
