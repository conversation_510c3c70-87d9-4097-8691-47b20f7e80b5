<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing;

use App\Application\Http\Controllers\Api\V4\Auth\LoginController;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider;
use Illuminate\Support\Facades\Route;

final class ApiV4PublicRoutingProvider extends RouteServiceProvider
{
    public function map(): void
    {
        Route::group([
            'middleware' => ['api', 'set-cookies', 'unregister-device'],
            'prefix' => 'api/v4',
            'as' => 'api.v4.',
        ], function () {
            $this->mapLoginRoutes();
        });
    }

    private function mapLoginRoutes(): void
    {
        Route::post('login', [LoginController::class, 'login'])
            ->middleware('user-from-date-not-reached')
            ->name('login');
    }
}
