<?php

namespace App\Application\Http\Controllers\Api\Media;

use App\Models\Media;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;

/**
 * @deprecated This is v1 legacy, media are replaced by RawMedia and ProcessedMedia.
 */
class MediaController extends Controller
{
    use AuthorizesRequests;

    public function callback(Media $media, Request $request): JsonResponse
    {
        // If the message id is not the one we are waiting for, we do nothing
        if ($request->get('job') && $request->get('job')['messageId'] != $media->message_id) {
            return new JsonResponse();
        }

        if ($request->has('error')) {
            $data = $media->data;
            $errorData = $request->input('error');
            data_set($data, 'error', $errorData);

            $media->data = $data;
            $media->status = Media::STATUS_ERROR;
            $media->save();

            return new JsonResponse();
        }

        if ($media->isAudio() && $request->has('result.renderedUrl') && $request->has('result.thumbnailUrl')) {
            $media->update([
                'status' => Media::STATUS_OK,
                'original_path' => $request->get('result')['thumbnailUrl'],
                'final_path' => $request->get('result')['renderedUrl'],
            ]);
        } elseif ($request->has('result.output')) {
            $media->update([
                'status' => Media::STATUS_OK,
                'final_path' => $request->get('result')['output'],
            ]);
        }

        return new JsonResponse();
    }

    public function callbackVideoThumbnail(Media $media, Request $request): JsonResponse
    {
        if ($request->has('result.output')) {
            $media->update([
                'thumbnail_path' => $request->input('result.output'),
            ]);
        }

        return new JsonResponse();
    }

    public function show(Media $media): JsonResponse
    {
        $this->authorize('view', $media);

        return new JsonResponse($media->toVue());
    }
}
