<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Broadcasting\ProjectChannel;
use App\Domain\Broadcasting\UserChannel;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\ServiceProvider;

final class BroadcastServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        Broadcast::routes(['middleware' => 'api']);

        Broadcast::channel('user.{user}', UserChannel::class);
        Broadcast::channel('project.{project}', ProjectChannel::class);
    }
}
