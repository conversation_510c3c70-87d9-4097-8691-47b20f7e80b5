<?php

declare(strict_types=1);

namespace App\Application\Http\Mappers\UserTeam;

use App\Application\Http\Requests\Admin\Team\TeamRequest;
use App\Domain\Permissions\AppRoleRepository;
use App\Domain\UserTeam\UserAppRoleDTO;
use App\Domain\UserTeam\UserAppRoleDTOCollection;
use Illuminate\Support\Collection;

final class UserAppRoleMapper
{
    private AppRoleRepository $appRoleRepository;

    public function __construct(AppRoleRepository $appRoleRepository)
    {
        $this->appRoleRepository = $appRoleRepository;
    }

    /**
     * @param TeamRequest $formRequest
     *
     * @return UserAppRoleDTOCollection
     */
    public function fromRequest(TeamRequest $formRequest): UserAppRoleDTOCollection
    {
        return new UserAppRoleDTOCollection(
            array_filter(
                array_map(function (array $value) {
                    if (!isset($value['id'], $value['app_role_id'])) {
                        return null;
                    }

                    // TODO: remove when we use the new system of roles
                    $roleName = $this->appRoleRepository->getById((int) $value['app_role_id'])->name;

                    return new UserAppRoleDTO((int) $value['id'], (int) $value['app_role_id'], $roleName);
                }, array_values($formRequest->get('users', [])))
            )
        );
    }
}
