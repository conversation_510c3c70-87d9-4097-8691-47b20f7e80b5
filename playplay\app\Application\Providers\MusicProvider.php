<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Music\MusicListRepository;
use App\Domain\Music\MusicRepository;
use App\Infrastructure\Music\EloquentMusicListRepository;
use App\Infrastructure\Music\EloquentMusicRepository;
use Illuminate\Support\ServiceProvider;

final class MusicProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(MusicListRepository::class, EloquentMusicListRepository::class);
        $this->app->bind(MusicRepository::class, EloquentMusicRepository::class);
    }
}
