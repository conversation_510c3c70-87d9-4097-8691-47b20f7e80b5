<?php

declare(strict_types=1);

namespace App\Application\Rules;

use Illuminate\Contracts\Validation\Rule;

class IsValidThumbnailFileExtensionRule implements Rule
{
    private const ALLOWED_FILE_TYPES = ['jpg', 'jpeg', 'png', 'svg'];

    /**
     * @inheritDoc
     * @note This message is not used. So we don't need to fill it.
     */
    public function message(): string
    {
        return '';
    }

    public function passes($attribute, $value): bool
    {
        if ($value === "") {
            return true;
        }

        $extension = pathinfo($value, PATHINFO_EXTENSION);

        return in_array(strtolower($extension), self::ALLOWED_FILE_TYPES, true);
    }
}
