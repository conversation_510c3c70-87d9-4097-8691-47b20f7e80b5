<?php

declare(strict_types=1);

namespace App\Domain\Company;

use App\Application\Events\CompanyFeatureUpdated;
use App\Domain\Company\Repositories\CompanyRepository;
use App\Domain\Plan\MissingPlanException;
use App\Models\Company;
use App\Models\Feature;
use Illuminate\Events\Dispatcher;

final class CompanyFeatureService
{
    private CompanyRepository $companyRepository;
    private Dispatcher $eventDispatcher;

    public function __construct(
        CompanyRepository $companyRepository,
        Dispatcher $eventDispatcher,
    ) {
        $this->companyRepository = $companyRepository;
        $this->eventDispatcher = $eventDispatcher;
    }

    public function updateCompanyFeatures(Company $company, array $featuresToUpdate, ?string $newCompanyType): void
    {
        /**
         * In case the company was in free trial and became a client then
         * we disable the has_watermark feature for the company in case it has it
         */
        if ($company->type === Company::TYPE_FREE_TRIAL && $newCompanyType === Company::TYPE_CLIENT) {
            $featureHasWatermark = Feature::query()->where('name', 'has_watermark')->first();
            if ($featureHasWatermark !== null) {
                $featuresToUpdate[$featureHasWatermark->id] = 0;
            }
        }

        $initialFeatureValues = $company->features->pluck('pivot.value', 'id')->all();
        $this->companyRepository->updateCompanyFeatures($company, $featuresToUpdate);
        $this->dispatchCompanyFeatureUpdatedIfNeeded($company, $initialFeatureValues, $featuresToUpdate);
    }

    /**
     * @throws MissingPlanException
     */
    public function updateCompanyFeaturesBasedOnItsPlan(Company $company): void
    {
        if ($company->plan === null) {
            throw new MissingPlanException('Tried to associate plan features to a company without a plan.');
        }

        $initialFeatureValues = $company->features->pluck('pivot.value', 'id')->all();
        $company->plan->features()->each(function (Feature $planFeature) use ($company) {
            if ($company->getFeatureFromId($planFeature->id)) {
                $company->features()->updateExistingPivot($planFeature->id, ['value' => $planFeature->pivot->value]);
            } else {
                $company->features()->attach($planFeature->id, ['value' => $planFeature->pivot->value]);
            }
        });

        $this->dispatchCompanyFeatureUpdatedIfNeeded(
            $company,
            $initialFeatureValues,
            $company->plan->features->pluck('pivot.value', 'id')->all()
        );
    }

    private function dispatchCompanyFeatureUpdatedIfNeeded(Company $company, array $initialFeatureValues, array $featuresToUpdate): void
    {
        $updatedFeaturesIds = $this->getUpdatedFeatureIds(
            $initialFeatureValues,
            $featuresToUpdate
        );

        if ($updatedFeaturesIds === []) {
            return;
        }

        $this->eventDispatcher->dispatch(
            new CompanyFeatureUpdated(
                $company->id,
                $updatedFeaturesIds
            ),
        );
    }

    /**
     * @param int[] $initialFeatureValues
     * @param int[] $updatedFeatureValues
     *
     * @return int[]
     */
    private function getUpdatedFeatureIds(array $initialFeatureValues, array $updatedFeatureValues): array
    {
        $updatedFeatureIds = [];

        foreach ($updatedFeatureValues as $updatedFeatureId => $updatedFeatureValue) {
            if (!array_key_exists($updatedFeatureId, $initialFeatureValues)
                || $updatedFeatureValue !== $initialFeatureValues[$updatedFeatureId]
            ) {
                $updatedFeatureIds[] = $updatedFeatureId;
            }
        }

        return $updatedFeatureIds;
    }
}
