<?php

namespace App\Application\Http\Controllers\Api\V2\ProjectScreen\Param\CutawayShot;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Mappers\CutawayShot\CutawayShotTimelineRenderMediaDataMapper;
use App\Application\Http\Requests\Api\Project\ProjectScreen\Param\CutawayShot\UpdateCutawayShotTimelineRequest;
use App\Application\Policies\CutawayShotPolicy;
use App\Domain\ProjectScreen\Param\CutawayShot\CutawayShotParamService;
use App\Domain\ProjectScreen\Param\CutawayShot\Serializer\CutawayShotTimelineSerializer;
use App\Domain\ProjectScreen\Param\Options;
use App\Domain\RawMedia\RawMediaRepository;
use App\Domain\Workflow\Config\MediaWorkflow;
use App\Models\ProjectScreen;
use App\Models\ScreenParams\ParamCutawayShot;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class CutawayShotTimelineController extends BaseController
{
    use AuthorizesRequests;

    private CutawayShotTimelineSerializer $cutawayShotTimelineSerializer;
    private CutawayShotParamService $cutawayShotParamService;
    private CutawayShotTimelineRenderMediaDataMapper $cutawayShotTimelineRenderMediaDataMapper;
    private RawMediaRepository $rawMediaRepository;
    private MediaWorkflow $mediaWorkflow;

    public function __construct(
        CutawayShotParamService $cutawayShotParamService,
        CutawayShotTimelineSerializer $cutawayShotTimelineSerializer,
        CutawayShotTimelineRenderMediaDataMapper $cutawayShotTimelineRenderMediaDataMapper,
        RawMediaRepository $rawMediaRepository,
        MediaWorkflow $mediaWorkflow
    ) {
        $this->cutawayShotParamService = $cutawayShotParamService;
        $this->cutawayShotTimelineSerializer = $cutawayShotTimelineSerializer;
        $this->cutawayShotTimelineRenderMediaDataMapper = $cutawayShotTimelineRenderMediaDataMapper;
        $this->rawMediaRepository = $rawMediaRepository;
        $this->mediaWorkflow = $mediaWorkflow;
    }

    /**
     * @throws AuthorizationException
     */
    public function index(
        ProjectScreen $projectScreen,
        ParamCutawayShot $layoutParam
    ): JsonResponse {
        $this->authorize('view', [CutawayShotPolicy::RESOURCE_NAME, $projectScreen->project]);

        $serializedTimeline = $this->cutawayShotTimelineSerializer->serialize(
            $projectScreen,
            $layoutParam
        );

        return $this->sendJsonResponse(
            new Collection($serializedTimeline),
            Response::HTTP_OK
        );
    }

    /**
     * @throws AuthorizationException
     */
    public function update(
        ProjectScreen $projectScreen,
        ParamCutawayShot $param,
        UpdateCutawayShotTimelineRequest $request
    ): JsonResponse {
        $this->authorize('update', [CutawayShotPolicy::RESOURCE_NAME, $projectScreen->project]);

        $cutawayShotRenderMediaDataList = $this->cutawayShotTimelineRenderMediaDataMapper->fromRequest($request);
        $this->cutawayShotParamService->updateManyCutawayShots($projectScreen, $cutawayShotRenderMediaDataList);

        $updatedOptions = Options::createManyFromArray(
            array_reduce(
                $request->input('items'),
                static fn(array $carry, array $item) => $carry + [$item['processed_media_id'] => $item['options']],
                []
            )
        );

        $this->cutawayShotParamService->updateProjectScreenParamsOptions($param, $projectScreen, $updatedOptions);

        $cutawayShotRawMedias = $this->rawMediaRepository->getByIds(
            $this->rawMediaRepository->getIdsOfCutawayShotsAndMediasUsedInProjectScreen($projectScreen)
        );
        foreach ($cutawayShotRawMedias as $cutawayShotRawMedia) {
            $this->mediaWorkflow->start($cutawayShotRawMedia);
        }

        $serializedTimeline = $this->cutawayShotTimelineSerializer->serialize($projectScreen, $param);

        return $this->sendJsonResponse(new Collection($serializedTimeline), Response::HTTP_OK);
    }
}
