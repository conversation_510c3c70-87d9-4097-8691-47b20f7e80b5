<?php

declare(strict_types=1);

namespace App\Domain\FavoriteRawMedia;

use App\Models\FavoriteRawMedia;
use App\Models\RawMedia;
use Illuminate\Pagination\Paginator;

interface FavoriteRawMediaRepository
{
    public const ITEMS_PER_PAGE = 20;

    public function deleteOneByIdAndUserId(int $rawMediaId, int $userId): bool;

    /**
     * @return string[]
     */
    public function getUsersFavoritesIdsByStockIds(string $stockName, array $stockIds, ?int $userId): array;

    public function getOneByIdAndUserId(int $rawMediaId, int $userId): ?FavoriteRawMedia;

    public function getPaginatedAllByUserId(int $userId, ?int $page): Paginator;

    public function updateOrInsert(int $userId, RawMedia $rawMedia): bool;
}
