<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Captcha\CaptchaValidationInterface;
use App\Infrastructure\Captcha\GoogleCaptchaValidationService;
use Illuminate\Support\ServiceProvider;

class CaptchaServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->bind(CaptchaValidationInterface::class, GoogleCaptchaValidationService::class);
    }
}
