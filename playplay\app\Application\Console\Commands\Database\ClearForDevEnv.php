<?php

namespace App\Application\Console\Commands\Database;

use App\Models\BillingPlan;
use App\Models\Config;
use App\Models\Project;
use App\Models\ProjectScreen;
use App\Models\ProjectScreenParam;
use App\Models\Renders\RenderScreenHtml;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Log\Logger;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use stdClass;

class ClearForDevEnv extends Command
{
    private const EXCLUDED_COMPANY_NAMES = [
        'PlayPlay',
        'test-e2e',
        'test-e2e-2',
        'test-e2e-3',
        'PlayPlay Motion TNR',
    ];

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:clear-for-dev-env';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear data for dev env';
    private Logger $logger;

    public function __construct(Logger $logger)
    {
        parent::__construct();
        $this->logger = $logger;
    }

    public function handle(): int
    {
        if (App::environment() === 'production') {
            $this->logger->warning('This cannot be run in production');
            return 1;
        }

        $this->deleteUselessData();
        $this->anonymizeUsers();
        $this->updateStripePlanId();
        $this->cleanBillingPlansFromExcludedCompanies();

        return 0;
    }

    private function anonymizeUsers(): void
    {
        DB::update(
            "update users u SET
            u.first_name=CONCAT('first_name_', u.id),
            u.last_name=CONCAT('last_name_', u.id),
            u.email=CONCAT('mail', u.id, '@mail.com'),
            u.phone=null
            WHERE u.email NOT LIKE '%@playplay.com'
            AND u.email NOT LIKE '%@playplay.video'
            AND u.email NOT LIKE 'nightwatch.playplay%'
        ;"
        );
    }

    private function deleteAllDataOfProjects(Collection $projects): void
    {
        $projectIds = $projects->pluck('id');
        DB::table('processed_medias')->whereIn('project_id', $projectIds)->delete();
        $projectScreens = ProjectScreen::whereIn('project_id', $projectIds);
        $projectScreenIds = $projectScreens->pluck('id');
        ProjectScreenParam::whereIn('project_screen_id', $projectScreenIds)->forceDelete();
        $renderScreensHtml = RenderScreenHtml::whereIn('project_screen_id', $projectScreenIds);
        DB::table('render_project_html_render_screens_html')
            ->whereIn('render_screen_html_id', $renderScreensHtml->pluck('id'))
            ->delete();
        DB::table('render_medias_render_screens_html')
            ->whereIn('render_screen_html_id', $renderScreensHtml->pluck('id'))
            ->delete();
        DB::table('project_raw_medias')
            ->whereIn('project_id', $projectIds)
            ->delete();
        $renderScreensHtml->forceDelete();
        $projectScreens->forceDelete();
        DB::table('projects')->whereIn('id', $projectIds)->delete();
    }

    private function deleteAllProjectDataFromCompaniesOtherThanPlayPlay(): void
    {
        $companiesToKeepDataFrom = DB::table('companies')
            ->select('id')
            ->whereIn('name', self::EXCLUDED_COMPANY_NAMES)
            ->get()
            ->map(fn(stdClass $company) => $company->id);

        if ($companiesToKeepDataFrom->isEmpty()) {
            return;
        }

        $freeTrialProjectModelConfig = Config::where('key', 'free_trial_project_id')->first();
        $projectsToDelete = Project::leftJoin('template_projects as tp', 'tp.project_id', 'projects.id')
            ->whereNotIn('company_id', $companiesToKeepDataFrom->toArray())
            ->where('id', '!=', $freeTrialProjectModelConfig->value)
            ->whereNull('tp.project_id');
        if ($projectsToDelete->count() === 0) {
            return;
        }

        $projectsToDelete->chunkById(1000, function ($projects) {
            $this->deleteAllDataOfProjects($projects);
        });
    }

    private function deleteUselessData(): void
    {
        DB::statement('SET foreign_key_checks = 0;');
        DB::table('render_errors')->truncate();
        DB::table('render_jobs')->truncate();
        DB::table('snapshots')->truncate();
        DB::table('getty_tokens')->truncate();
        DB::table('password_resets')->truncate();
        DB::table('configs')->where('key', 'emails_on_video_error')->update(['value' => '']);
        $this->deleteAllProjectDataFromCompaniesOtherThanPlayPlay();
        DB::statement('SET foreign_key_checks = 1;');
    }

    private function updateStripePlanId(): void
    {
        BillingPlan::whereRef('Standard Annual (£)')->update(['stripe_plan_id' => 'plan_Gi4QKj5mvbOL0Q']);
        BillingPlan::whereRef('Standard Annual ($)')->update(['stripe_plan_id' => 'plan_GdxwFkUwpCxfxM']);
        BillingPlan::whereRef('Standard Annual (€)')->update(['stripe_plan_id' => 'plan_GduboPZlwIeSvW']);
        BillingPlan::whereRef('Standard Monthly (£)')->update(['stripe_plan_id' => 'plan_Gi4Q8EfckGOZxH']);
        BillingPlan::whereRef('Standard Monthly ($)')->update(['stripe_plan_id' => 'plan_GdxpFf40k8jPbW']);
        BillingPlan::whereRef('Standard Monthly (€)')->update(['stripe_plan_id' => 'plan_FSVImqieTbYiph']);
    }

    private function cleanBillingPlansFromExcludedCompanies(): void
    {
        $companiesIds = DB::table('companies')
            ->select('id')
            ->whereIn('name', self::EXCLUDED_COMPANY_NAMES)
            ->get()
            ->map(fn(stdClass $company) => $company->id);

        if ($companiesIds->isEmpty()) {
            return;
        }

        DB::table('companies')
            ->whereIn('id', $companiesIds)
            ->update(['stripe_customer_id' => null]);

        DB::table('subscriptions')
            ->whereIn('company_id', $companiesIds)
            ->delete();
    }
}
