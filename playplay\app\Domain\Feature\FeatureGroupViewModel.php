<?php

namespace App\Domain\Feature;

class FeatureGroupViewModel
{
    private string $displayName;
    /** @var FeatureViewModel[] */
    private array $features;

    /**
     * @param FeatureViewModel[] $featureViewModels
     */
    public function __construct(string $displayName, array $featureViewModels)
    {
        $this->displayName = $displayName;
        $this->features = $featureViewModels;
    }

    public function getDisplayedName(): string
    {
        return $this->displayName;
    }

    /**
     * @return FeatureViewModel[]
     */
    public function getFeatures(): array
    {
        return $this->features;
    }
}
