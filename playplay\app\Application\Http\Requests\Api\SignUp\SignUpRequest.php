<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\SignUp;

use App\Domain\Localization\SupportedLanguages;
use App\Services\HubspotService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SignUpRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'first_name' => ['required', 'max:50', 'name'],
            'last_name' => ['required', 'max:50', 'name'],
            'password' => ['required', 'check_password_rule'],
            'terms' => ['required', 'boolean', 'accepted'],
            'email' => [
                'bail',
                'required',
                'email',
                'uniqueFreeTrialActive',
            ],
            'language' => ['required', Rule::in(SupportedLanguages::values())],
            'usage_of_playplay' => ['required', 'string', Rule::in(array_keys(HubspotService::PLAYPLAY_USAGE_VALUES))],
            'employees_number' => ['required', 'string', Rule::in(array_keys(HubspotService::EMPLOYEES_NUMBER_VALUES))],
            'option_newsletter' => ['sometimes', 'boolean'],
            'phone' => ['sometimes', 'max:25'],
            'hutk' => ['sometimes', 'nullable', 'string'],
            'origin' => ['sometimes', 'nullable', 'url'],
        ];
    }
}
