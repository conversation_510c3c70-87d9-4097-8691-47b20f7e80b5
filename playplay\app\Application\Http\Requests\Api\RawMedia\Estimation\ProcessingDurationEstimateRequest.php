<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\RawMedia\Estimation;

use App\Domain\RawMedia\RawMediaType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

final class ProcessingDurationEstimateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'type' => ['required', 'string', Rule::in([RawMediaType::AUDIO, RawMediaType::VIDEO])],
            'size' => ['required', 'integer', 'min:0'],
            'duration' => ['required', 'integer', 'min:0'],
        ];
    }
}
