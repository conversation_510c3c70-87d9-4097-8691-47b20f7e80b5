<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\V2\LiraryItem\LibraryItemController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class LibraryItemRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/library-items',
            'as' => 'library-items.',
        ], static function (Router $router) {
            $router->get('', [LibraryItemController::class, 'index'])->name('index');
            $router->put('/{library_item}', [LibraryItemController::class, 'update'])->name('update');
            $router->post('', [LibraryItemController::class, 'store'])->name('store');
            $router->delete('', [LibraryItemController::class, 'destroyMany'])->name('destroyMany');

            $router->put('/bulk/move', [LibraryItemController::class, 'move'])->name('move');
            $router->get('/folders', [LibraryItemController::class, 'folders'])->name('folders');
        });
    }
}
