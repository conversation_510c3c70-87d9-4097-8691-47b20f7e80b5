<?php

declare(strict_types=1);

namespace App\Domain\AssetUsages;

use DateTimeImmutable;
use DateTimeInterface;
use JsonSerializable;

final class AssetUsage implements JsonSerializable
{
    private string $assetId;
    private int $quantity;
    private DateTimeImmutable $usageDate;

    public function __construct(string $assetId, int $quantity, DateTimeImmutable $usageDate)
    {
        $this->assetId = $assetId;
        $this->quantity = $quantity;
        $this->usageDate = $usageDate;
    }

    public function getAssetId(): string
    {
        return $this->assetId;
    }

    public function getQuantity(): int
    {
        return $this->quantity;
    }

    public function getUsageDate(): DateTimeImmutable
    {
        return $this->usageDate;
    }

    public function jsonSerialize(): array
    {
        return [
            'asset_id' => $this->getAssetId(),
            'quantity' => $this->getQuantity(),
            'usage_date' => $this->getUsageDate()->format(DateTimeInterface::ATOM),
        ];
    }
}
