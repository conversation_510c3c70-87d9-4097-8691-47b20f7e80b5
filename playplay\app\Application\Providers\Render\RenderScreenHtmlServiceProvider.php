<?php

namespace App\Application\Providers\Render;

use App\Domain\Render\RenderScreen\RenderScreenHtmlRepository;
use App\Infrastructure\Render\RenderScreen\EloquentRenderScreenHtmlRepository;
use Illuminate\Support\ServiceProvider;

class RenderScreenHtmlServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->bind(RenderScreenHtmlRepository::class, EloquentRenderScreenHtmlRepository::class);
    }
}
