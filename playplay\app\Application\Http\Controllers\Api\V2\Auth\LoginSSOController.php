<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Auth;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Auth\LoginSSORequest;
use App\Domain\User\UserRepository;
use Illuminate\Config\Repository;
use Illuminate\Contracts\Routing\UrlGenerator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;
use Symfony\Component\HttpKernel\Exception\NotAcceptableHttpException;
use Throwable;
use WorkOS\Resource\ConnectionType;
use WorkOS\SSO;

final class LoginSSOController extends BaseController
{
    private SSO $ssoClient;

    private UrlGenerator $urlGenerator;

    private UserRepository $userRepository;

    private Repository $configRepository;

    public function __construct(
        SSO $ssoClient,
        UrlGenerator $urlGenerator,
        UserRepository $userRepository,
        Repository $configRepository
    ) {
        $this->ssoClient = $ssoClient;
        $this->urlGenerator = $urlGenerator;
        $this->userRepository = $userRepository;
        $this->configRepository = $configRepository;
    }

    public function login(LoginSSORequest $request): JsonResponse
    {
        return $this->getAuthorizationUrl(
            $request->get('email'),
            $this->urlGenerator->route('api.v2.login-sso-callback')
        );
    }

    public function loginMobile(LoginSSORequest $request): JsonResponse
    {
        return $this->getAuthorizationUrl(
            $request->get('email'),
            // Important to add trailing slash to target website page
            $this->urlGenerator->route('app.sso.mobile.callback')
        );
    }

    private function getAuthorizationUrl(string $email, string $redirectUri): JsonResponse
    {
        if (!$this->userRepository->userMustLoginWithSSO($email)) {
            throw new NotAcceptableHttpException('email.account_has_no_sso');
        }

        $domain = $this->getDomainFromEmail($email);

        if (in_array($domain, $this->configRepository->get('sso.orange.domains'))) {
            return $this->sendJsonResponse(
                new Collection([
                    'url' => $this->configRepository->get('sso.orange.redirect_url'),
                ]),
                Response::HTTP_OK
            );
        }

        try {
            $workOSOrganizationId = $this->userRepository->getWorkOSOrganizationIdFromEmail($email);
            /** @var ?ConnectionType $provider */
            $provider = $workOSOrganizationId ? null : ConnectionType::GoogleOAuth;
            $authorizationUrl = $this->ssoClient->getAuthorizationUrl(
                null,
                $redirectUri,
                [],
                $provider,
                null,
                $workOSOrganizationId
            );

            return $this->sendJsonResponse(new Collection(['url' => $authorizationUrl]), Response::HTTP_OK);
        } catch (Throwable $e) {
            throw new NotAcceptableHttpException('email.sso_technical_error');
        }
    }

    private function getDomainFromEmail(string $email): string
    {
        return substr($email, strpos($email, '@') + 1);
    }
}
