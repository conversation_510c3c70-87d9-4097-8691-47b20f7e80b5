<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Admin;

use App\Application\Http\Controllers\Admin\TeamPreset\FontController;
use App\Application\Http\Controllers\Admin\TeamPreset\TeamPresetController;
use App\Application\Http\Controllers\Admin\TeamPreset\TransitionController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class TeamPresetRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/teams/{team}/presets/{teamPreset}',
            'as' => 'teams.presets.',
        ], static function (Router $router) {
            $router->put('/colors', [TeamPresetController::class, 'updateColors'])->name('colors.update');
            $router->get('/branding', [TeamPresetController::class, 'editBranding'])->name('branding.edit');
            $router->put('/logos', [TeamPresetController::class, 'updateLogos'])->name('logos.update');
            $router->put('/transitions', [TransitionController::class, 'update'])->name('transitions.update');
            $router->put('/outros', [TeamPresetController::class, 'updateOutros'])->name('outros.update');
            $router->delete('/outros/{format}', [TeamPresetController::class, 'deleteForcedOutroInFormat'])
                ->name('outros.forced.delete');
            $router->get('/musics', [TeamPresetController::class, 'editMusics'])->name('musics.edit');
            $router->put('/musics', [TeamPresetController::class, 'updateMusics'])->name('musics.update');
            $router->put('/fonts', [FontController::class, 'update'])->name('fonts.update');
        });
    }
}
