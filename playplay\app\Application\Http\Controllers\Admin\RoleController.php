<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin;

use App\Models\User;
use Illuminate\Contracts\View\Factory as ViewFactory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Redirector;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

final class RoleController extends BaseController
{
    private ViewFactory $viewFactory;
    private Redirector $redirectService;

    public function __construct(ViewFactory $viewFactory, Redirector $redirectService)
    {
        $this->viewFactory = $viewFactory;
        $this->redirectService = $redirectService;
        $this->authorizeResource(Role::class);
    }

    public function create(): RedirectResponse
    {
        return $this->redirectService->route(
            'admin.roles.edit',
            [
                'role' => Role::query()->create(['name' => trans('message.new_role')])
            ]
        );
    }

    public function destroy(Role $role)
    {
        return $this->ajaxDelete($role, $this->redirectService->getUrlGenerator()->route('admin.roles.index'));
    }

    public function edit(Role $role)
    {
        return $this->returnView($role);
    }

    public function index()
    {
        return $this->returnView();
    }

    public function show(Role $role)
    {
        return $this->returnView($role);
    }

    public function update(Request $request, Role $role)
    {
        $role->update(array_filter($request->only(['name'])));

        $users = (array) $request->input('users');
        $permissions = (array) $request->input('permissions');

        $role->users()->sync($users);

        $existingPermissions = Permission::query()->whereIn('id', $permissions)->pluck('id');
        $role->permissions()->sync($existingPermissions);

        $role->forgetCachedPermissions();

        return $this->redirectService->back();
    }

    private function returnView(Role $role = null): View
    {
        $roles = Role::with('permissions')->get();
        $allPermissions = Permission::query()->pluck('name', 'id')->toArray();
        $allUsers = User::query()->pluck('first_name', 'id')->toArray();

        return $this->viewFactory->make(
            'admin.roles.main',
            [
                'roles' => $roles,
                'allPermissions' => $allPermissions,
                'allUsers' => $allUsers,
                'actualRole' => $role ?? null,
                'actualPermissions' => $role?->permissions()->pluck('id')->toArray(),
                'actualUsers' => $role?->users()->pluck('id')->toArray(),
            ]
        );
    }
}
