<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\RawMedia\HeavyMediaErrorRepository;
use App\Domain\RawMedia\RawMediaRepository;
use App\Infrastructure\RawMedia\EloquentHeavyMediaErrorRepository;
use App\Infrastructure\RawMedia\EloquentRawMediaRepository;
use Illuminate\Support\ServiceProvider;

final class RawMediaServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(RawMediaRepository::class, EloquentRawMediaRepository::class);
        $this->app->bind(HeavyMediaErrorRepository::class, EloquentHeavyMediaErrorRepository::class);
    }
}
