<?php

namespace App\Application\Http\Controllers\Api\V2\Stocks;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Stock\StockLoadRequest;
use App\Application\Http\Requests\Api\Stock\StockSearchRequest;
use App\Services\Stock\AStock;
use App\Services\Stock\StockService;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;

class StockController extends BaseController
{
    private StockService $stockService;

    public function __construct(StockService $stockService)
    {
        $this->stockService = $stockService;
    }

    public function loadGifs(StockLoadRequest $request)
    {
        $results = $this->stockService->callStocks(AStock::ACTION_POPULAR, AStock::TYPE_GIF, [
            'page' => $request->get('page', 1),
            'is_edito' => false,
        ]);

        return $this->sendJsonResponse(new Collection([$results]), JsonResponse::HTTP_OK);
    }

    public function loadImages(StockLoadRequest $request, Guard $guard)
    {
        $isEdito = (bool) $request->get('is_edito', false);
        if ($isEdito && !$guard->user()->company->hasGettyEditoFeature()) {
            throw new UnauthorizedHttpException(
                'Unauthorized',
                'You don\'t have the rights for searching into editorial getty images'
            );
        }

        $results = $this->stockService->callStocks(AStock::ACTION_POPULAR, AStock::TYPE_IMAGE, [
            'page' => $request->get('page', 1),
            'is_edito' => $isEdito,
        ]);

        return $this->sendJsonResponse(new Collection([$results]), JsonResponse::HTTP_OK);
    }

    public function loadVideos(StockLoadRequest $request, Guard $guard)
    {
        $isEdito = (bool) $request->get('is_edito', false);
        if ($isEdito && !$guard->user()->company->hasGettyEditoFeature()) {
            throw new UnauthorizedHttpException(
                'Unauthorized',
                'You don\'t have the rights for searching into editorial getty videos'
            );
        }

        $results = $this->stockService->callStocks(AStock::ACTION_POPULAR, AStock::TYPE_VIDEO, [
            'page' => $request->get('page', 1),
            'is_edito' => $isEdito,
        ]);

        return $this->sendJsonResponse(new Collection([$results]), JsonResponse::HTTP_OK);
    }

    public function searchGifs(StockSearchRequest $request)
    {
        $results = $this->stockService->callStocks(AStock::ACTION_SEARCH, AStock::TYPE_GIF, [
            'page' => $request->get('page'),
            'keyword' => $request->get('keyword'),
            'is_edito' => false,
        ]);

        return $this->sendJsonResponse(new Collection([$results]), JsonResponse::HTTP_OK);
    }

    public function searchImages(StockSearchRequest $request, Guard $guard)
    {
        $isEdito = (bool) $request->get('is_edito', false);
        if ($isEdito && !$guard->user()->company->hasGettyEditoFeature()) {
            throw new UnauthorizedHttpException(
                'Unauthorized',
                'You don\'t have the rights for searching into editorial getty pictures'
            );
        }

        $results = $this->stockService->callStocks(AStock::ACTION_SEARCH, AStock::TYPE_IMAGE, [
            'page' => $request->get('page'),
            'keyword' => $request->get('keyword'),
            'is_edito' => $isEdito,
        ]);

        return $this->sendJsonResponse(new Collection([$results]), JsonResponse::HTTP_OK);
    }

    public function searchVideos(StockSearchRequest $request, Guard $guard)
    {
        $isEdito = (bool) $request->get('is_edito', false);
        if ($isEdito && !$guard->user()->company->hasGettyEditoFeature()) {
            throw new UnauthorizedHttpException(
                'Unauthorized',
                'You don\'t have the rights for searching into editorial getty videos'
            );
        }

        $results = $this->stockService->callStocks(AStock::ACTION_SEARCH, AStock::TYPE_VIDEO, [
            'page' => $request->get('page'),
            'keyword' => $request->get('keyword'),
            'is_edito' => $isEdito,
        ]);

        return $this->sendJsonResponse(new Collection([$results]), JsonResponse::HTTP_OK);
    }
}
