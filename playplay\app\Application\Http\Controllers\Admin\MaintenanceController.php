<?php

namespace App\Application\Http\Controllers\Admin;

use App\Application\Http\Requests\Admin\Maintenance\MaintenanceStoreRequest;
use App\Application\Http\Requests\Admin\Maintenance\MaintenanceUpdateRequest;
use App\Domain\Maintenance\MaintenanceRepository;
use App\Models\Maintenance;
use DateTimeImmutable;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;

class MaintenanceController extends BaseController
{
    private MaintenanceRepository $maintenanceRepository;

    public function __construct(MaintenanceRepository $maintenanceRepository)
    {
        $this->authorizeResource(Maintenance::class);
        $this->maintenanceRepository = $maintenanceRepository;
    }

    public function create(): View
    {
        $maintenance = new Maintenance();

        return view('admin.maintenances.create', ['maintenance' => $maintenance]);
    }

    public function destroy(Maintenance $maintenance): JsonResponse
    {
        if ($maintenance->delete()) {
            return new JsonResponse(['success' => true, 'redirect' => route('admin.maintenances.index')]);
        }

        throw new HttpException(Response::HTTP_INTERNAL_SERVER_ERROR, 'Not deleted!');
    }

    public function edit(Maintenance $maintenance): View
    {
        return view('admin.maintenances.edit', ['maintenance' => $maintenance]);
    }

    public function index(Request $request): View
    {
        $maintenances = Maintenance::orderByDesc('id')
            ->simplePaginate($request->get('per_page', 10))
            ->appends($request->all());

        return view('admin.maintenances.index', ['maintenances' => $maintenances]);
    }

    public function showDangerZone(Maintenance $maintenance): View
    {
        return view('admin.maintenances.danger-zone', ['maintenance' => $maintenance]);
    }

    public function store(MaintenanceStoreRequest $request): RedirectResponse
    {
        $maintenance = $this->maintenanceRepository->create(
            true,
            $request->get('message'),
            $this->getStartedAt($request->get('started_at')),
            $this->getEndedAtFromRequest($request->get('ended_at'))
        );

        return redirect()->route('admin.maintenances.edit', $maintenance);
    }

    public function update(Maintenance $maintenance, MaintenanceUpdateRequest $request): RedirectResponse
    {
        $this->maintenanceRepository->update(
            $maintenance,
            $request->get('message'),
            $this->getStartedAt($request->get('started_at')),
            $this->getEndedAtFromRequest($request->get('ended_at'))
        );

        return redirect()->back();
    }

    private function getEndedAtFromRequest(?string $endedAt): ?DateTimeImmutable
    {
        if ($endedAt === null) {
            return null;
        }

        $endedAt = DateTimeImmutable::createFromFormat('Y-m-d\TH:i', $endedAt);

        return $endedAt === false ? null : $endedAt;
    }

    private function getStartedAt(string $startedAt): DateTimeImmutable
    {
        return DateTimeImmutable::createFromFormat('Y-m-d\TH:i', $startedAt);
    }
}
