<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\TimecodedElement;

use App\Models\TimecodedElement\TimecodedElementPresetParam;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

final class TimecodedElementPresetParamStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'type' => ['required', 'string', Rule::in(TimecodedElementPresetParam::ALL_TYPES)],
            'name' => ['required', 'string'],
            'backoffice_name' => ['required', 'string'],
            'animaniac_ref' => ['required', 'string'],
            'default_value' => [
                Rule::requiredIf(fn() => $this->get('type') === TimecodedElementPresetParam::TYPE_TEXTAREA),
                'string',
            ],
        ];
    }
}
