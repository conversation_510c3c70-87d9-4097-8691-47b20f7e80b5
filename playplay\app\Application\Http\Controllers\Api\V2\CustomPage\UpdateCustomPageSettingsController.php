<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\CustomPage;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\CustomPage\UpdateCustomPageSettingsRequest;
use App\Domain\CustomPage\CustomPageSettingsUpdater;
use App\Domain\CustomPage\CustomPageSettingsUpdaterDTO;
use App\Domain\CustomPage\Exception\UnauthorizedTeamPresetException;
use App\Infrastructure\CustomPage\Serializers\CustomPageSettingsSerializer;
use App\Models\CustomPage;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

final class UpdateCustomPageSettingsController extends BaseController
{
    use AuthorizesRequests;

    private CustomPageSettingsUpdater $customPageSettingsUpdater;

    private CustomPageSettingsSerializer $settingsSerializer;

    public function __construct(
        CustomPageSettingsUpdater $customPageSettingsUpdater,
        CustomPageSettingsSerializer $settingsSerializer,
    ) {
        $this->customPageSettingsUpdater = $customPageSettingsUpdater;
        $this->settingsSerializer = $settingsSerializer;
    }

    public function __invoke(
        CustomPage $customPage,
        UpdateCustomPageSettingsRequest $request
    ): JsonResponse {
        $this->authorize('update', [CustomPage::class, $customPage]);
        if ($customPage->project->trashed() === true) {
            throw new NotFoundHttpException('Project not found for this custom page.');
        }

        try {
            $customPageSettingsUpdaterDTO = new CustomPageSettingsUpdaterDTO(
                $request->input('title'),
                $request->input('description'),
                $request->input('primary_color'),
                $request->get('font_id'),
                $request->get('main_logo_processed_media_id')
            );

            $this->customPageSettingsUpdater->update($customPage, $customPageSettingsUpdaterDTO);

            return $this->sendJsonResponse(
                new Collection([$this->settingsSerializer->serialize($customPage)]),
                Response::HTTP_OK
            );
        } catch (UnauthorizedTeamPresetException $e) {
            // TODO change this when validation guideline is available
            return $this->sendJsonResponseErrorFromArray(
                new Collection($e->getErrors()),
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }
    }
}
