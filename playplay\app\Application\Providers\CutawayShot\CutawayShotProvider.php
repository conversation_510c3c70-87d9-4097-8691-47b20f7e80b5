<?php

namespace App\Application\Providers\CutawayShot;

use App\Domain\ProjectScreen\Param\CutawayShot\CutawayShotProcessedMediaRepository;
use App\Domain\ProjectScreen\Param\CutawayShot\CutawayShotRenderMediaRepository;
use App\Domain\ProjectScreen\Param\CutawayShot\RenderScreenCutawayShotRenderMediaRepository;
use App\Infrastructure\ProjectScreen\Param\CutawayShot\EloquentCutawayShotProcessedMediaRepository;
use App\Infrastructure\ProjectScreen\Param\CutawayShot\EloquentCutawayShotRenderMediaRepository;
use App\Infrastructure\ProjectScreen\Param\CutawayShot\EloquentRenderScreenCutawayShotRenderMediaRepository;
use Illuminate\Support\ServiceProvider;

class CutawayShotProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(
            CutawayShotRenderMediaRepository::class,
            EloquentCutawayShotRenderMediaRepository::class
        );

        $this->app->bind(
            CutawayShotProcessedMediaRepository::class,
            EloquentCutawayShotProcessedMediaRepository::class
        );

        $this->app->bind(
            RenderScreenCutawayShotRenderMediaRepository::class,
            EloquentRenderScreenCutawayShotRenderMediaRepository::class
        );
    }
}
