<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\TimecodedElement;

use Illuminate\Foundation\Http\FormRequest;

final class ReorderTimecodedElementsFamilyPresetsRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'preset_orders' => ['array', 'required'],
            'preset_orders.*' => ['integer', 'min:0'],
        ];
    }
}
