<?php

namespace App\Application\Http\Requests\Admin\Screen\Layout;

use App\Models\ScreenLayout;
use Illuminate\Foundation\Http\FormRequest;

class StoreScreenLayoutRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $layoutPossible = [
            ScreenLayout::LAYOUT_CLASSIC,
            ScreenLayout::LAYOUT_SPLIT_3_COLUMNS,
            ScreenLayout::LAYOUT_SPLIT_4_SECTIONS,
        ];

        return [
            'type' => ['required', 'string', 'in:' . implode(',', $layoutPossible)],
            'default' => ['integer', 'between:1,255', 'lte:max'],
            'max' => ['integer', 'between:1,255'],
        ];
    }
}
