<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\TimecodedElement;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\TimecodedElement\TimecodedElementPresetStoreRequest;
use App\Application\Http\Requests\Admin\TimecodedElement\TimecodedElementPresetUpdateRequest;
use App\Domain\TimecodedElement\Repositories\TimecodedElementPresetParamRepository;
use App\Domain\TimecodedElement\Repositories\TimecodedElementPresetRepository;
use App\Domain\TimecodedElement\TimecodedElementPresetService;
use App\Models\TimecodedElement\TimecodedElementPreset;
use App\Models\TimecodedElement\TimecodedElementsFamily;
use DateTimeImmutable;
use DateTimeInterface;
use Illuminate\Contracts\Routing\UrlGenerator;
use Illuminate\Contracts\View\Factory as ViewFactory;
use Illuminate\Contracts\View\View;
use Illuminate\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Redirector;

final class TimecodedElementPresetController extends BaseController
{
    private Redirector $redirector;
    private TimecodedElementPresetParamRepository $timecodedElementPresetParamRepository;
    private TimecodedElementPresetRepository $timecodedElementPresetRepository;
    private TimecodedElementPresetService $timecodedElementPresetService;
    private ViewFactory $viewFactory;
    private UrlGenerator $urlGenerator;
    private Application $app;

    public function __construct(
        Redirector $redirector,
        TimecodedElementPresetParamRepository $timecodedElementPresetParamRepository,
        TimecodedElementPresetRepository $timecodedElementPresetRepository,
        TimecodedElementPresetService $timecodedElementPresetService,
        ViewFactory $viewFactory,
        UrlGenerator $urlGenerator,
        Application $app,
    ) {
        $this->redirector = $redirector;
        $this->timecodedElementPresetParamRepository = $timecodedElementPresetParamRepository;
        $this->timecodedElementPresetRepository = $timecodedElementPresetRepository;
        $this->timecodedElementPresetService = $timecodedElementPresetService;
        $this->viewFactory = $viewFactory;
        $this->urlGenerator = $urlGenerator;
        $this->app = $app;
    }

    public function create(TimecodedElementsFamily $timecodedElementsFamily): View
    {
        $timecodedElementPreset = new TimecodedElementPreset();

        return $this->viewFactory->make(
            'admin.timecoded-elements-family.timecoded-element-presets.create',
            [
                'timecodedElementsFamily' => $timecodedElementsFamily,
                'timecodedElementPreset' => $timecodedElementPreset,
                'minimumTimecodedElementPresetActivatedAt' => $this->getMinimumTimecodedElementPresetActivatedAt(),
            ]
        );
    }

    public function store(
        TimecodedElementPresetStoreRequest $request,
        TimecodedElementsFamily $timecodedElementsFamily,
    ): RedirectResponse {

        $timecodedElementPreset = $this->timecodedElementPresetService->create(
            $timecodedElementsFamily->id,
            $request->get('name'),
            $request->get('backoffice_name'),
            $request->get('animaniac_ref'),
            $request->get('thumbnail_url'),
            $request->get('preview_url'),
            (int) $request->get('default_duration'),
            $this->getActivatedAtFromRequest($request->get('activated_at')),
        );

        // TODO: redirect to the family edition page when it will exist
        return $this->redirector->route(
            'admin.timecoded-elements-family.timecoded-element-presets.edit',
            [
                'timecodedElementsFamily' => $timecodedElementsFamily,
                'timecodedElementPreset' => $timecodedElementPreset,
            ]
        );
    }

    public function edit(
        TimecodedElementsFamily $timecodedElementsFamily,
        TimecodedElementPreset $timecodedElementPreset,
    ): View {
        $timecodedElementPresetParams = $this
            ->timecodedElementPresetParamRepository
            ->getByTimecodedElementPresetId($timecodedElementPreset->id);

        return $this->viewFactory->make('admin.timecoded-elements-family.timecoded-element-presets.edit', [
            'timecodedElementsFamily' => $timecodedElementsFamily,
            'timecodedElementPreset' => $timecodedElementPreset,
            'timecodedElementPresetParams' => $timecodedElementPresetParams,
            'isTimecodedElementPresetActivatedAtDisabled' => $this->isTimecodedElementPresetActivatedAtDisabled(
                $timecodedElementPreset
            ),
            'minimumTimecodedElementPresetActivatedAt' => $this->getMinimumTimecodedElementPresetActivatedAt(),
        ]);
    }

    public function update(
        TimecodedElementPresetUpdateRequest $request,
        TimecodedElementsFamily $timecodedElementsFamily,
        TimecodedElementPreset $timecodedElementPreset,
    ): RedirectResponse {
        $this->timecodedElementPresetRepository->update(
            $timecodedElementPreset,
            $request->get('name'),
            $request->get('backoffice_name'),
            $request->get('animaniac_ref'),
            $request->get('thumbnail_url'),
            $request->get('preview_url'),
            (int) $request->get('default_duration'),
            $this->getActivatedAtFromRequest($request->get('activated_at'), $timecodedElementPreset->activated_at),
        );

        // TODO: redirect to the family edition page when it will exist
        return $this->redirector->route(
            'admin.timecoded-elements-family.timecoded-element-presets.edit',
            [
                'timecodedElementsFamily' => $timecodedElementsFamily,
                'timecodedElementPreset' => $timecodedElementPreset,
            ]
        );
    }

    public function destroy(
        TimecodedElementsFamily $timecodedElementsFamily,
        TimecodedElementPreset $timecodedElementPreset
    ): JsonResponse {
        $this->timecodedElementPresetService->delete($timecodedElementPreset);

        return new JsonResponse([
            'success' => true,
            'redirect' => $this->urlGenerator->route('admin.timecoded-elements-family.edit', [
                'timecodedElementsFamily' => $timecodedElementsFamily
            ])
        ]);
    }

    public function showDangerZone(
        TimecodedElementsFamily $timecodedElementsFamily,
        TimecodedElementPreset $timecodedElementPreset
    ): View {
        return $this->viewFactory->make('admin.timecoded-elements-family.timecoded-element-presets.danger-zone', [
            'timecodedElementsFamily' => $timecodedElementsFamily,
            'timecodedElementPreset' => $timecodedElementPreset,
        ]);
    }

    private function isTimecodedElementPresetActivatedAtDisabled(TimecodedElementPreset $timecodedElementPreset): bool
    {
        if ($timecodedElementPreset->activated_at === null) {
            return false;
        }

        return $timecodedElementPreset->activated_at <= new DateTimeImmutable();
    }

    private function getMinimumTimecodedElementPresetActivatedAt(): DateTimeInterface
    {
        if ($this->app->environment() === 'production') {
            return new DateTimeImmutable('+1 day midnight');
        }

        return new DateTimeImmutable('midnight');
    }

    private function getActivatedAtFromRequest(
        ?string $activatedAt,
        ?DateTimeInterface $defaultValue = null
    ): ?DateTimeInterface {
        if ($activatedAt === null) {
            return $defaultValue;
        }

        if ($activatedAt === '') {
            return null;
        }

        return DateTimeImmutable::createFromFormat(
            'Y-m-d',
            $activatedAt
        )->setTime(0, 0, 0);
    }
}
