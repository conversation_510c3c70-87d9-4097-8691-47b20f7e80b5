<?php

declare(strict_types=1);

namespace App\Domain\Billing;

use App\Domain\Billing\SubscriptionRepository as BillingSubscriptionRepository;
use App\Domain\Churn\SubscriptionEndDateCalculator;
use App\Domain\Company\Repositories\CompanyRepository;
use App\Domain\Subscription\SubscriptionRepository;
use App\Models\Subscription;
use DateInterval;
use DateTimeImmutable;
use DateTimeInterface;

class SubscriptionService
{
    private BillingSubscriptionRepository $billingSubscriptionRepository;

    private SubscriptionRepository $subscriptionRepository;

    private CompanyRepository $companyRepository;

    private CompanyChurnRepository $companyChurnRepository;

    private SubscriptionEndDateCalculator $endDateCalculator;

    public function __construct(
        BillingSubscriptionRepository $billingSubscriptionRepository,
        SubscriptionRepository $subscriptionRepository,
        CompanyRepository $companyRepository,
        CompanyChurnRepository $companyChurnRepository,
        SubscriptionEndDateCalculator $endDateCalculator,
    ) {
        $this->subscriptionRepository = $subscriptionRepository;
        $this->companyRepository = $companyRepository;
        $this->billingSubscriptionRepository = $billingSubscriptionRepository;
        $this->companyChurnRepository = $companyChurnRepository;
        $this->endDateCalculator = $endDateCalculator;
    }

    public function getAnnualPlanCommitmentEndDate(int $startDateTimestamp): DateTimeImmutable
    {
        return (new DateTimeImmutable())
            ->setTimestamp($startDateTimestamp)
            ->add(new DateInterval('P1Y'));
    }

    /**
     * @throws BillingException
     */
    public function churn(Subscription $subscription, int $userId): void
    {
        $this->logCompanyChurn($subscription, $userId);

        if ($subscription->isAnnualBillingPlan()) {
            $this->endYearlySubscription($subscription);

            return;
        }

        $this->endMonthlySubscription($subscription);
    }

    private function endYearlySubscription(Subscription $subscription): void
    {
        $endDate = $subscription->commitment_end;
        if ($subscription->commitment_end < new DateTimeImmutable()) {
            $endDate = $this->endDateCalculator->calculateFromDate($subscription->started_at);
        }

        $this->billingSubscriptionRepository->cancelSubscription($subscription, $endDate);

        $this->churnSubscription($subscription, $subscription->commitment_end);
    }

    private function endMonthlySubscription(Subscription $subscription): void
    {
        $endDate = $this->billingSubscriptionRepository
            ->getSubscription($subscription->stripe_subscription_id)
            ->getCurrentPeriodEndDate();

        if ($endDate <= new DateTimeImmutable()) {
            $endDate = $this->endDateCalculator->calculateFromDate($subscription->started_at);
        }

        $this->billingSubscriptionRepository->cancelSubscription($subscription, $endDate);
        $this->churnSubscription($subscription, $endDate);
    }

    private function churnSubscription(Subscription $subscription, DateTimeInterface $subscriptionEnd): void
    {
        $this->subscriptionRepository->markSubscriptionAsTerminated($subscription, $subscriptionEnd);
        $this->companyRepository->updateClientUntilDate($subscription->company, $subscriptionEnd);
    }

    private function logCompanyChurn(Subscription $subscription, int $userId): void
    {
        $this->companyChurnRepository->create(
            $subscription->company->id,
            $subscription->id,
            $userId,
            new DateTimeImmutable()
        );
    }
}
