<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\TimecodedElement;

use Illuminate\Foundation\Http\FormRequest;

final class TimecodedElementUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'params.*.timecoded_element_preset_param_id' => ['required', 'integer'],
            'params.*.value' => ['required'],
            'timecodes.start' => ['required', 'integer', 'min:0'],
            'timecodes.end' => ['required', 'integer'],
            'transformations.angle' => ['required', 'numeric', 'min:0', 'max:360'],
            'transformations.scale.x' => ['required', 'numeric'],
            'transformations.scale.y' => ['required', 'numeric'],
            'transformations.position.x' => ['required', 'numeric'],
            'transformations.position.y' => ['required', 'numeric'],
        ];
    }
}
