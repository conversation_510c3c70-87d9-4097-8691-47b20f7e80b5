<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\User;

use App\Application\Http\Controllers\Api\V2\User\UserController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class UserRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'as' => 'users',
            'prefix' => '/users',
        ], static function (Router $router) {
            $router->get('/me', [UserController::class, 'me'])->name('.me');
            $router->get('/me/presets', [UserController::class, 'mePresets'])->name('.me.presets');
            $router->get('/me/waiting-gif', [UserController::class, 'waitingGif'])->name('.me.waiting-gif');
            $router->get('/me/filters', [UserController::class, 'meFilters'])->name('.me.filters');
            $router->put('/me', [UserController::class, 'update'])->name('.me.update');
            $router->get('/me/recently-used-screens', [UserController::class, 'recentlyUsedScreensByUserAndTeam'])
                ->name('.recently-used-screens');

            $router->post('/', [UserController::class, 'store'])->name('.store');
            $router->delete('/{user}', [UserController::class, 'destroy'])->name('.destroy');
        });
    }
}
