<?php

declare(strict_types=1);

namespace App\Domain\Billing;

enum CurrencySign: string
{
    case EURO = '€';
    case DOLLAR = '$';
    case POUND = '£';

    public static function fromISOCode(string $isoCode): CurrencySign
    {
        $isoCode = mb_strtoupper($isoCode);

        return match ($isoCode) {
            'USD' => self::DOLLAR,
            'GBP' => self::POUND,
            'EUR' => self::EURO,
        };
    }

    public static function toISOCode(CurrencySign $currencySign): string
    {
        return match ($currencySign) {
            self::DOLLAR => 'USD',
            self::POUND => 'GBP',
            self::EURO => 'EUR',
        };
    }
}
