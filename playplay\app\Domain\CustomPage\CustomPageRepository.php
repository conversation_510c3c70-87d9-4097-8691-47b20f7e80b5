<?php

declare(strict_types=1);

namespace App\Domain\CustomPage;

use App\Domain\Common\Exceptions\EntityNotFoundException;
use App\Models\CustomPage;

interface CustomPageRepository
{
    public function create(CreateCustomPageDTO $createCustomPageDTO): CustomPage;

    /** @throws EntityNotFoundException */
    public function getByProjectId(int $projectId, $withTrashedProject = false): CustomPage;

    /** @throws EntityNotFoundException */
    public function getByToken(string $token): CustomPage;

    public function publish(CustomPage $customPage): void;

    public function unpublish(CustomPage $customPage): void;

    public function updateSettings(
        CustomPage $customPage,
        CustomPageSettingsUpdaterDTO $updateDTO
    ): void;
}
