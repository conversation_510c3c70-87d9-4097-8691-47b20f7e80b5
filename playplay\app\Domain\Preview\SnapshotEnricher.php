<?php

declare(strict_types=1);

namespace App\Domain\Preview;

use App\Domain\Render\RenderMedia\RenderMediaRepository;
use App\Models\Renders\RenderMedia;
use App\Models\Snapshot;
use Illuminate\Support\Collection;

final class SnapshotEnricher
{
    private RenderMediaRepository $renderMediaRepository;
    private RenderMediaEnricher $renderMediaEnricher;

    public function __construct(
        RenderMediaRepository $renderMediaRepository,
        RenderMediaEnricher $renderMediaEnricher
    ) {
        $this->renderMediaRepository = $renderMediaRepository;
        $this->renderMediaEnricher = $renderMediaEnricher;
    }

    public function enrichWithRenderMedias(Snapshot $snapshot): array
    {
        $data = $snapshot->data;
        $renderMedias = $this->renderMediaRepository->getByIdsAndParentTypeProcessedMedia(
            $this->getRenderMediaIds($data)
        );

        if ($renderMedias->isEmpty()) {
            return $data;
        }

        $data = $this->renderMediaEnricher->enrichLogo($data, $renderMedias);

        foreach ($data['screens'] as $index => $screen) {
            $data['screens'][$index]['params'] = $this->fillMissingDataRecursive($renderMedias, $screen['params']);
        }

        $data = $this->renderMediaEnricher->enrichVoiceover($data, $renderMedias);

        return $this->renderMediaEnricher->enrichMusic($data, $renderMedias);
    }

    private function fillMissingData(Collection $renderMedias, array $media): array
    {
        $renderMediaId = $media['render_media_id'] ?? null;

        if ($renderMediaId === null) {
            return $media;
        }

        /** @var RenderMedia $renderMedia */
        $renderMedia = $renderMedias->get($renderMediaId);
        $media = $this->renderMediaEnricher->enrichMediaData($media, $renderMedia);

        if (array_key_exists('cutaway_shots', $media) && $media['cutaway_shots'] !== null) {
            $media['cutaway_shots'] = $this->fillMissingDataForCutawayShots($renderMedias, $media['cutaway_shots']);
        }

        return $media;
    }

    private function fillMissingDataForCutawayShots(Collection $renderMedias, array $cutawayShots): array
    {
        return array_map(function (array $cutawayShot) use ($renderMedias) {
            if ($cutawayShot['url'] === null) {
                $renderMedia = $renderMedias->get($cutawayShot['render_media_id']);

                if ($renderMedia !== null) {
                    $cutawayShot['url'] = $renderMedia->parent->rawMedia->lastRender->rendered_url;
                }
            }

            return $cutawayShot;
        }, $cutawayShots);
    }

    private function fillMissingDataRecursive(Collection $renderMedias, array $mediasOrLayouts): array
    {
        foreach ($mediasOrLayouts as $nth => $media) {
            /**
             * we want to ignore cutawayShots because it needs a specific logic
             *
             * @see fillMissingDataForCutawayShots
             */
            if (!is_array($media) || $nth === 'cutaway_shots') {
                continue;
            }

            $mediasOrLayouts[$nth] = $this->fillMissingDataRecursive(
                $renderMedias,
                $this->fillMissingData($renderMedias, $media)
            );
        }

        return $mediasOrLayouts;
    }

    private function getRenderMediaIds(array $data): array
    {
        $renderMediaIds = [];
        array_walk_recursive($data, static function ($value, string $key) use (&$renderMediaIds) {
            if ($key === 'render_media_id' && is_int($value)) {
                $renderMediaIds[] = $value;
            }
        });

        return $renderMediaIds;
    }
}
