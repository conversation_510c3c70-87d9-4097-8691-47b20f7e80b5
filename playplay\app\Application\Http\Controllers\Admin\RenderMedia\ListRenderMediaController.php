<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\RenderMedia;

use App\Application\Http\Controllers\Admin\AbstractRenderController;
use App\Models\ProcessedMedia;
use App\Models\RawMedia;
use App\Models\Renders\RenderMedia;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;

final class ListRenderMediaController extends AbstractRenderController
{
    private Guard $guard;

    public function __construct(Guard $guard)
    {
        $this->authorizeResource(RenderMedia::class, RenderMedia::class);
        $this->guard = $guard;
    }

    public function __invoke(Request $request): View
    {
        $query = RenderMedia::query()->select('render_medias.*', 'raw_medias.source', 'raw_medias.type')
            ->whereNull('render_duplicated_id')
            ->orderBy('id', 'desc');

        if ($request->get('parent', 'RawMediaV2') === 'RawMediaV2') {
            $query->where('render_medias.parent_type', RawMedia::DEPRECATED_CLASS)
                ->leftJoin('raw_medias', 'raw_medias.id', '=', 'render_medias.parent_id');
        } else {
            $query->where('render_medias.parent_type', ProcessedMedia::DEPRECATED_CLASS)
                ->leftJoin('processed_medias', 'processed_medias.id', '=', 'render_medias.parent_id')
                ->leftJoin('raw_medias', 'raw_medias.id', '=', 'processed_medias.raw_media_id');

            if ($this->guard->user()->isUsAdminPlayPlayer()) {
                $query->join('projects', 'projects.id', '=', 'processed_medias.project_id')
                    ->join('companies', 'projects.company_id', '=', 'companies.id')
                    ->where('companies.data_is_restricted', false);
            }
        }

        if ($request->get('status')) {
            $query->where('render_medias.status', $request->get('status'));
        }

        $renders = $query->simplePaginate($request->get('per_page', 50))
            ->appends($request->all());

        return view('admin.render-medias.index', compact('renders'));
    }
}
