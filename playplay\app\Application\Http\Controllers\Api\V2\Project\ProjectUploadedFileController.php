<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Project;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Project\ProjectUploadedFilesIndexRequest;
use App\Domain\Project\RawMedia\RelationType;
use App\Domain\RawMedia\RawMediaRepository;
use App\Models\Project;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

final class ProjectUploadedFileController extends BaseController
{
    use AuthorizesRequests;

    private RawMediaRepository $rawMediaRepository;

    public function __construct(RawMediaRepository $rawMediaRepository)
    {
        $this->rawMediaRepository = $rawMediaRepository;
    }

    public function __invoke(Project $project, ProjectUploadedFilesIndexRequest $request): JsonResponse
    {
        $this->authorize('view', $project);
        $relationType = RelationType::fromKey($request->input('relation_type_key'));
        return $this->sendJsonResponse(
            $this->rawMediaRepository->getAllByProject($project, $relationType),
            Response::HTTP_OK
        );
    }
}
