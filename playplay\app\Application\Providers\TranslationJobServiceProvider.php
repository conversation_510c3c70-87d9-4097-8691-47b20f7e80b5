<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Translation\TranslationJobRepository;
use App\Infrastructure\Translation\EloquentTranslationJobRepository;
use Illuminate\Support\ServiceProvider;

class TranslationJobServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->bind(TranslationJobRepository::class, EloquentTranslationJobRepository::class);
    }
}
