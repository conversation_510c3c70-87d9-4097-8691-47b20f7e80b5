<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\TeamPreset\ColorSerializer;
use Illuminate\Contracts\Config\Repository;
use Illuminate\Foundation\Application;
use Illuminate\Support\ServiceProvider;

class ColorsSerializerProvider extends ServiceProvider
{
    private Repository $configRepository;

    public function __construct(Application $app)
    {
        parent::__construct($app);
        $this->configRepository = $app->get(Repository::class);
    }

    public function register()
    {
        $this->app->bind(ColorSerializer::class, function () {
            return new ColorSerializer($this->configRepository->get('team-preset-colors.colors'));
        });
    }
}
