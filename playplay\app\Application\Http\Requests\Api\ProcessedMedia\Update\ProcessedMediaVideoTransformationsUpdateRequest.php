<?php

namespace App\Application\Http\Requests\Api\ProcessedMedia\Update;

use App\Domain\Render\RenderMedia\Transformation\KeepSizeValue;
use App\Models\Renders\RenderMedia;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProcessedMediaVideoTransformationsUpdateRequest extends FormRequest
{
    public function messages(): array
    {
        return [
            'crop.required_without' => 'The crop field is required when keepSize is not present',
            'keepSize.required_without' => 'The keepSize field is required when crop is not present',
            'keepSize.in' => 'The selected keepSize is invalid: '.implode('|', KeepSizeValue::getAllValues()),
        ];
    }

    public static function getRules()
    {
        return [
            'param_id' => ['required', 'numeric', 'exists:layout_params,id', 'paramIsInProject'],
            'trimStart' => ['required', 'numeric'],
            'trimEnd' => [
                'required',
                'numeric',
                'greater_than:trimStart',
                'maxDifference:trimStart,' . RenderMedia::MAX_VIDEO_TRIM_DURATION_IN_MILLISECONDS,
            ],
            'crop' => ['nullable', 'required_without:keepSize', 'crop'],
            'keepSize' => ['nullable', 'required_without:crop', Rule::in(KeepSizeValue::getAllValues())],
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return static::getRules();
    }
}
