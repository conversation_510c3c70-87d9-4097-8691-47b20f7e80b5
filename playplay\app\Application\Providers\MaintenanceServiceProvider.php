<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Maintenance\MaintenanceRepository;
use App\Infrastructure\Maintenance\CacheMaintenanceRepository;
use App\Infrastructure\Maintenance\EloquentMaintenanceRepository;
use Illuminate\Contracts\Cache\Repository as CacheRepository;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Support\ServiceProvider;

final class MaintenanceServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(CacheMaintenanceRepository::class, function (Application $app) {
            return new CacheMaintenanceRepository(
                $app->get(EloquentMaintenanceRepository::class),
                $app->get(CacheRepository::class)
            );
        });
        $this->app->bind(MaintenanceRepository::class, CacheMaintenanceRepository::class);
    }
}
