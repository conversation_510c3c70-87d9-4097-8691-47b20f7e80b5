<?php

declare(strict_types=1);

namespace App\Application\Policies;

use App\Domain\TeamPreset\TeamPresetPolicy as DomainTeamPresetPolicy;
use App\Domain\UserTeam\UserTeamAppPermissionService;
use App\Domain\UserTeam\UserTeamService;
use App\Models\Permissions\AppPermission;
use App\Models\Permissions\AppRole;
use App\Models\ProcessedMedia;
use App\Models\Team;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

final class TeamPolicy extends DefaultPolicy
{
    use HandlesAuthorization;

    private DomainTeamPresetPolicy $teamPresetPolicy;
    private UserTeamAppPermissionService $userTeamAppPermissionService;
    private UserTeamService $userTeamService;

    public function __construct(
        DomainTeamPresetPolicy $teamPresetPolicy,
        UserTeamAppPermissionService $userTeamAppPermissionService,
        UserTeamService $userTeamService
    ) {
        $this->teamPresetPolicy = $teamPresetPolicy;
        $this->userTeamAppPermissionService = $userTeamAppPermissionService;
        $this->userTeamService = $userTeamService;
    }

    public function before(User $authUser): ?bool
    {
        if ($authUser->can('manage-companies')) {
            return true;
        }

        return null;
    }

    /** @todo add missing tests */
    public function addUser(User $authUser, Team $team, AppRole $appRole, User $user): bool
    {
        return $this->view($authUser, $team)
            && !$user->hasTeam($team)
            && $this->userTeamService->canAddUserWithAppRoleToTeam($team, $appRole)
            && $this->userTeamAppPermissionService
                ->userHasAppPermissionInTeam($authUser, $team, AppPermission::CAN_MANAGE_USERS);
    }

    /** @note unit tested. see \*\Unit\**\TeamPolicyTest */
    public function updateUser(User $authUser, Team $team, User $userToUpdate, AppRole $newAppRole): bool
    {
        return $this->view($authUser, $team)
            && $userToUpdate->hasTeam($team)
            && $this->userTeamService->canUpdateUserWithAppRoleInTeam($userToUpdate, $team, $newAppRole)
            && $this->userTeamAppPermissionService
                ->userHasAppPermissionInTeam($authUser, $team, AppPermission::CAN_MANAGE_USERS);
    }

    /** @todo Remove it : dead code */
    public function createUser(User $user, Team $team): bool
    {
        $maxUsers = $team->company->getFeature('max_users');

        return $user->teams->pluck('id')->contains($team->id)
            && ($maxUsers === null || $team->users->count() < $maxUsers);
    }

    public function detachUser(User $authUser, Team $team, User $user): bool
    {
        return $this->view($authUser, $team)
            && $authUser->hasMoreRightsThan($user, $team);
    }

    public function update(User $user, Team $team): bool
    {
        return $this->view($user, $team);
    }

    public function updatePreset(User $user, Team $team, array $processedMediaIds = []): bool
    {
        return $this->view($user, $team)
            /** @todo Replace this check by permission. @see UserTeamAppPermissionService */
            && $user->hasRoleInTeam($team->id, AppRole::ROLE_OWNER)
            && $user->can('viewMany', [ProcessedMedia::class, $processedMediaIds]);
    }

    public function view(User $user, Team $team): bool
    {
        return $user->hasTeam($team);
    }

    public function updateFonts(User $user, Team $team, array $fontIds): bool
    {
        return $this->view($user, $team)
            /** @todo Replace this check by permission. @see UserTeamAppPermissionService */
            && $user->hasRoleInTeam($team->id, AppRole::ROLE_OWNER)
            && $this->teamPresetPolicy->allowsFontsUpdate($team->presets->first(), $fontIds);
    }
}
