<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\User;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Models\User;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Http\RedirectResponse;
use Illuminate\Session\Store;

final class UserLogAsController extends BaseController
{
    public function __construct()
    {
        $this->authorizeResource(User::class);
    }

    public function logAs(Store $session, Guard $auth, User $user = null): RedirectResponse
    {
        if ($user !== null && $user->exists) {
            $this->authorize('canAccessRestrictedData', $user->company);
            $this->authorize('log-as');
            if ($user->id == $auth->id()) {
                alert()->error('Vous ne pouvez pas vous connectez en tant que... vous !', 'Et non !')
                    ->html()->confirmButton()->autoclose(7000);

                return redirect()->back();
            }

            if (!$session->has('orig_user')) {
                $session->put('orig_user', $auth->user());
            }

            auth()->login($user);
        } elseif ($session->has('orig_user')) {
            auth()->login($session->pull('orig_user'));
        } else {
            throw new \InvalidArgumentException('No origin user in session.');
        }

        return redirect()->back();
    }
}
