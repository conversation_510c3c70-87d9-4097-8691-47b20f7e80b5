<?php

declare(strict_types=1);

namespace App\Domain\Outro;

use App\Domain\RawMedia\RawMediaType;
use App\Models\Project;
use App\Models\RawMedia;

class ForcedOutroSerializer
{
    private RawMediaRepository $rawMediaRepository;

    private OutroService $outroService;

    public function __construct(
        RawMediaRepository $rawMediaRepository,
        OutroService $outroService
    ) {
        $this->rawMediaRepository = $rawMediaRepository;
        $this->outroService = $outroService;
    }

    public function serialize(Project $project): ?array
    {
        $forcedOutro = $this->getForcedOutroRawMedia($project);
        if ($forcedOutro === null) {
            return null;
        }

        $hasMusicOnOutro = $project->hasMusicOnOutro();
        $musicLevel = $hasMusicOnOutro ? $project->getMusicLevel() : 0;
        $voiceoverLevel = $hasMusicOnOutro ? $project->getVoiceoverLevel() : 0;

        return $this->convertOutroToAScreen($forcedOutro, $musicLevel, $voiceoverLevel);
    }

    private function convertOutroToAScreen(RawMedia $outro, int $musicLevel, int $voiceoverLevel): array
    {
        return [
            'type' => 'ProjectOutro',
            'params' => [
                'media' => [
                    'type' => RawMediaType::VIDEO,
                    'url' => $outro->url,
                    'duration' => $outro->duration,
                ],
            ],
            'options' => [
                'colors' => [
                    "main" => "rgb(0, 0, 0)",
                    "text" => "rgb(0, 0, 0)",
                    "word" => "rgb(0, 0, 0)",
                ],
                'logos' => [],
                'settings' => [
                    'show_logos' => false,
                    'music_level' => $musicLevel,
                    'media_level' => Project::DEFAULT_AUDIO_LEVEL,
                    'voiceover_level' => $voiceoverLevel,
                    'screen_duration' => 'regular',
                ],
            ],
        ];
    }

    private function getForcedOutroRawMedia(Project $project): ?RawMedia
    {
        $outroId = $this->outroService->getForcedOutroRawMediaIdFor($project);
        if ($outroId === null) {
            return null;
        }

        return $this->rawMediaRepository->getById($outroId);
    }
}
