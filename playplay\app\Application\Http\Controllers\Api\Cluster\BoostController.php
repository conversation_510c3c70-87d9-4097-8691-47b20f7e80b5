<?php

namespace App\Application\Http\Controllers\Api\Cluster;

use App\Application\Http\Requests\Api\Boost\BoostRequest;
use App\Domain\Metrics\PublishMetricsService;
use Illuminate\Contracts\Cache\Repository as CacheRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Symfony\Component\HttpFoundation\Response;

class BoostController extends Controller
{
    private const DEFAULT_SECONDS = 600;
    private const DEFAULT_CLUSTER_SIZE = 3;

    private CacheRepository $cacheRepository;

    private PublishMetricsService $metricsPublisher;

    public function __construct(CacheRepository $cacheRepository, PublishMetricsService $metricsPublisher)
    {
        $this->cacheRepository = $cacheRepository;
        $this->metricsPublisher = $metricsPublisher;
    }

    public function store(BoostRequest $request): JsonResponse
    {
        $seconds = $request->input('seconds', self::DEFAULT_SECONDS);
        $clusterSize = $request->input('cluster_size', self::DEFAULT_CLUSTER_SIZE);
        $this->cacheRepository->put('boost', $clusterSize, $seconds);

        $this->metricsPublisher->publish();

        return new JsonResponse([], Response::HTTP_OK);
    }
}
