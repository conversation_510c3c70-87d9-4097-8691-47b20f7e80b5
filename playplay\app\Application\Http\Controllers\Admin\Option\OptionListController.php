<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Option;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\Option\CreateOptionListRequest;
use App\Application\Http\Requests\Admin\Option\ReorderOptionListRequest;
use App\Application\Http\Requests\Admin\Option\StoreOptionListRequest;
use App\Application\Http\Requests\Admin\Option\UpdateOptionListRequest;
use App\Domain\Screen\Parameters\OptionListRepository;
use App\Models\Screen\Parameters\OptionList;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;

final class OptionListController extends BaseController
{
    private OptionListRepository $optionListRepository;

    public function __construct(OptionListRepository $optionListRepository)
    {
        $this->authorizeResource(OptionList::class, OptionList::class);
        $this->optionListRepository = $optionListRepository;
    }

    public function create(CreateOptionListRequest $createOptionListRequest): View
    {
        $optionList = (new OptionList())->optionListCategory()
            ->associate($createOptionListRequest->get('option_list_category_id'));

        return view('admin.options.option-lists.create', [
            'optionList' => $optionList,
        ]);
    }

    public function destroy(OptionList $optionList): JsonResponse
    {
        $this->optionListRepository->delete($optionList);

        return new JsonResponse([
            'success' => true,
            'redirect' => route('admin.option-list-categories.edit', $optionList->optionListCategory->id),
        ]);
    }

    public function edit(OptionList $optionList): View
    {
        return view('admin.options.option-lists.edit', ['optionList' => $optionList]);
    }

    public function reorder(OptionList $optionList, ReorderOptionListRequest $request): JsonResponse
    {
        $this->optionListRepository->reorderOptionsOf($optionList, $request->get('option_ids'));

        return new JsonResponse(['status' => 'ok'], Response::HTTP_OK);
    }

    public function showDangerZone(OptionList $optionList): View
    {
        return view('admin.options.option-lists.danger-zone', ['optionList' => $optionList]);
    }

    public function store(StoreOptionListRequest $request): RedirectResponse
    {
        $optionList = OptionList::create([
            'name' => $request->get('name'),
            'backoffice_name' => $request->get('backoffice_name'),
            'value_type' => $request->get('value_type'),
            'is_generic' => (bool) $request->get('is_generic'),
            'option_list_category_id' => $request->get('option_list_category_id'),
        ]);

        return redirect()->route('admin.option-lists.edit', $optionList);
    }

    public function update(OptionList $optionList, UpdateOptionListRequest $request): RedirectResponse
    {
        $optionList->update($request->only(['name', 'backoffice_name']));

        return redirect()->route('admin.option-lists.edit', $optionList);
    }
}
