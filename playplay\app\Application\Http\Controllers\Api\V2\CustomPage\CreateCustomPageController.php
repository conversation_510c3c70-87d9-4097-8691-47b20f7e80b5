<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\CustomPage;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\CustomPage\CreateCustomPageRequest;
use App\Domain\Common\Exceptions\EntityNotFoundException;
use App\Domain\CustomPage\CustomPageFactory;
use App\Domain\CustomPage\CustomPageRepository;
use App\Domain\CustomPage\Exception\UnableToGetDefaultFontException;
use App\Domain\CustomPage\ProjectDoesNotHaveRenderHtmlUrlException;
use App\Domain\Project\ProjectRepository;
use App\Infrastructure\CustomPage\Serializers\CustomPageSettingsSerializer;
use App\Models\CustomPage;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

final class CreateCustomPageController extends BaseController
{
    use AuthorizesRequests;

    private CustomPageRepository $customPageRepository;
    private CustomPageFactory $customPageFactory;
    private ProjectRepository $projectRepository;
    private CustomPageSettingsSerializer $customPageSettingsSerializer;

    public function __construct(
        CustomPageRepository $customPageRepository,
        CustomPageFactory $customPageFactory,
        ProjectRepository $projectRepository,
        CustomPageSettingsSerializer $customPageSettingsSerializer
    ) {
        $this->customPageRepository = $customPageRepository;
        $this->customPageFactory = $customPageFactory;
        $this->projectRepository = $projectRepository;
        $this->customPageSettingsSerializer = $customPageSettingsSerializer;
    }

    public function __invoke(CreateCustomPageRequest $request): JsonResponse
    {
        $projectId = (int) $request->get('projectId');
        $project = $this->projectRepository->findOneById($projectId);
        if ($project === null) {
            throw ValidationException::withMessages(["This projectId is invalid."]);
        }

        $this->authorize('create', [CustomPage::class, $project]);

        try {
            $customPage = $this->customPageRepository->getByProjectId($projectId);

            return $this->sendJsonResponse(
                new Collection([$this->customPageSettingsSerializer->serialize($customPage)]),
                Response::HTTP_ALREADY_REPORTED
            );
        } catch (EntityNotFoundException) {
            // no custom page exists for this project, let's create a new one
        }

        try {
            $customPage = $this->customPageFactory->createFromProject($project);

            return $this->sendJsonResponse(
                new Collection([$this->customPageSettingsSerializer->serialize($customPage)]),
                Response::HTTP_CREATED
            );
        } catch (ProjectDoesNotHaveRenderHtmlUrlException) {
            throw ValidationException::withMessages(["This project does not have a rendered url."]);
        } catch (UnableToGetDefaultFontException) {
            throw ValidationException::withMessages(["Unable to set the default font."]);
        }
    }
}
