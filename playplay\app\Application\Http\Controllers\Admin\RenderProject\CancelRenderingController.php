<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\RenderProject;

use App\Application\Http\Controllers\Admin\AbstractRenderController;
use App\Domain\Render\RenderProject\RenderProjectHtmlRepository;
use App\Models\Renders\RenderProjectHtml;
use Illuminate\Routing\Redirector;

final class CancelRenderingController extends AbstractRenderController
{
    private Redirector $redirector;
    private RenderProjectHtmlRepository $renderProjectRepository;

    public function __construct(Redirector $redirector, RenderProjectHtmlRepository $renderProjectRepository)
    {
        $this->redirector = $redirector;
        $this->renderProjectRepository = $renderProjectRepository;
    }

    public function __invoke(RenderProjectHtml $render)
    {
        $this->authorize('canAccessRestrictedData', $render->project->company);

        $this->renderProjectRepository->cancel($render);

        return $this->redirector->route('admin.renderProjectsHtml.show', $render->id);
    }
}
