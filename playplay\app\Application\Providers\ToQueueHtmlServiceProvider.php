<?php

namespace App\Application\Providers;

use App\Domain\Audio\MusicSerializer;
use App\Domain\Audio\VoiceoverSerializer;
use App\Domain\Outro\OutroSerializer;
use App\Domain\Project\Serializer\OptionsSerializer as ProjectOptionsSerializer;
use App\Domain\ProjectScreen\Serializer\ProjectScreenSerializer;
use App\Domain\Transition\Serializer\ProjectTransitionSerializer;
use App\Services\ToQueueHtmlService;
use Illuminate\Foundation\Application;
use Illuminate\Support\ServiceProvider;

class ToQueueHtmlServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(
            ToQueueHtmlService::class,
            function (Application $app) {
                return new ToQueueHtmlService(
                    $app[OutroSerializer::class],
                    $app[ProjectTransitionSerializer::class],
                    $app[VoiceoverSerializer::class],
                    $app[MusicSerializer::class],
                    $app[ProjectOptionsSerializer::class],
                    $app[ProjectScreenSerializer::class],
                );
            }
        );
    }
}
