<?php

declare(strict_types=1);

namespace App\Application\Listeners\UserAction;

use App\Domain\User\Action\UserAction;
use App\Domain\User\Action\UserActionService;

final class SendUserActionLogOnSignUp
{
    private UserActionService $userActionService;

    public function __construct(UserActionService $userActionService)
    {
        $this->userActionService = $userActionService;
    }

    public function handle(): void
    {
        $this->userActionService->addUserAction(new UserAction('user-signed-up'));
    }
}
