<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\ProjectScreen;

use App\Application\Events\ProjectScreenUpdated;
use App\Application\Events\ProjectUpdated;
use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Project\ProjectScreen\ProjectScreenUpdateRequest;
use App\Domain\ProjectScreen\DefaultProjectScreenService;
use App\Domain\ProjectScreen\ProjectScreenService;
use App\Models\Project;
use App\Models\ProjectScreen;
use App\Services\ProjectScreen\SwitchScreen;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use InvalidArgumentException;
use RuntimeException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Throwable;

final class UpdateProjectScreenController extends BaseController
{
    use AuthorizesRequests;

    private SwitchScreen $switchScreenService;
    private Dispatcher $eventDispatcher;
    private ProjectScreenService $projectScreenService;

    public function __construct(
        SwitchScreen $switchScreen,
        Dispatcher $eventDispatcher,
        ProjectScreenService $projectScreenService,
    ) {
        $this->middleware('can:view,project');
        $this->switchScreenService = $switchScreen;
        $this->eventDispatcher = $eventDispatcher;
        $this->projectScreenService = $projectScreenService;
    }

    /**
     * @throws Throwable
     */
    public function __invoke(
        Project $project,
        ProjectScreen $projectScreen,
        ProjectScreenUpdateRequest $request
    ): JsonResponse {
        if ($projectScreen->project_id !== $project->id) {
            throw new AccessDeniedHttpException('User can\'t access this project');
        }

        $isSwitchScreen = $request->has('screen_id');
        if ($isSwitchScreen) {
            try {
                $this->switchScreenService->switch($projectScreen, (int) $request->get('screen_id'));
                $updatedFields = ['screen_id'];
            } catch (RuntimeException | InvalidArgumentException) {
                return $this->sendJsonResponse(new Collection([]), Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } else {
            $updatedFields = $this->updateProjectScreen($projectScreen, $request);
        }

        if ($request->has('has_transition_after')) {
            $this->projectScreenService->toggleTransition(
                $project,
                $projectScreen,
                $request->boolean('has_transition_after')
            );
            $updatedFields[] = 'has_transition_after';
        }

        if (count($updatedFields) > 0) {
            $this->eventDispatcher->dispatch(
                new ProjectScreenUpdated(
                    $projectScreen,
                    DefaultProjectScreenService::shouldUpdatePreview($updatedFields)
                )
            );

            $this->eventDispatcher->dispatch(new ProjectUpdated($project));
        }

        $projectScreen->load([
            'screen.layouts',
            'screen.layouts.params',
            'params',
            'params.layoutParam',
            'params.renderSubtitles',
        ]);

        return $this->sendJsonResponse(new Collection([$projectScreen]), JsonResponse::HTTP_OK);
    }

    private function updateProjectScreen(ProjectScreen $projectScreen, ProjectScreenUpdateRequest $request): array
    {
        // Check if project screen will be changed
        $updatedFields = (new Collection([
            'settings.music_level',
            'settings.media_level',
            'settings.voiceover_level',
            'settings.screen_duration',
            'settings.show_logos',
            'colors.text',
            'colors.main',
            'colors.word',
        ]))->filter(static function (string $key) use ($projectScreen, $request): bool {
            return $request->has($key) && $request->input($key) !== data_get($projectScreen, $key);
        });

        $params = $request->get('params', []);
        if ($params !== []) {
            $this->projectScreenService->upsertParams($projectScreen, $params);
            $updatedFields->add('params');
        }

        if ($request->has('settings')) {
            $projectScreen->settings = $request->get('settings');
        }

        if ($request->has('colors')) {
            $projectScreen->colors = $request->get('colors');
        }

        if ($request->has('order')) {
            $this->projectScreenService->updateOrder($projectScreen, $request->get('order'));
        }

        $projectScreen->save();

        return $updatedFields->values()->all();
    }
}
