<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\RenderProject;

use App\Application\Http\Controllers\Api\BaseController;
use App\Models\Project;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class CreateRenderProjectController extends BaseController
{
    public function __invoke(Project $project): JsonResponse
    {
        return $this->sendJsonResponse(new Collection([$project]), Response::HTTP_OK);
    }
}
