<?php

declare(strict_types=1);

namespace App\Domain\ProcessedMedia\Repository;

use App\Domain\Project\ProcessedMedia\RelationType;
use App\Models\ProcessedMedia;
use App\Models\Project;
use App\Models\ProjectScreen;
use Illuminate\Database\Eloquent\Collection;

interface ProcessedMediaRepository
{
    public function delete(int $processedMediaId): void;

    public function deleteByIds(array $processedMediaIdsToDelete): void;

    public function getAllByIdsWithRendersAndRawMedia(array $processedMediaIds): Collection;

    public function getById(int $processedMediaId): ?ProcessedMedia;

    public function getByIdsAndTypes(array $ids, array $types): Collection;

    /**
     * @param int            $projectId
     * @param RelationType[] $relationTypes
     * @param array          $relationsToLoad
     *
     * @return Collection
     */
    public function getByProjectIdAndRelationTypes(
        int $projectId,
        array $relationTypes,
        array $relationsToLoad = []
    ): Collection;

    /**
     * @param string[] $rawMediaTypes
     *
     * @return Collection|ProcessedMedia[]
     */
    public function getByProjectIdAndRawMediaTypes(int $projectId, array $rawMediaTypes): Collection;

    /**
     * @return int[]
     */
    public function getAllIdsByProjectExceptProcessedMediaUsedInMediaParamsAndCutawayShots(
        Project $project
    ): array;

    /**
     * @return int[]
     */
    public function getIdsOfLogosOfAProject(Project $project): array;

    /**
     * @return int[]
     */
    public function getAllIdsExceptProcessedMediaUsedInMediaAndCutawayShotsParamsOfProjectScreen(
        ProjectScreen $projectScreen
    ): array;

    /**
     * Needed to fill render_media <> render_screen tables for normalized medias
     * Normalized medias will have a proper link table so as we iterate on image > gifs > videos
     * we must filter medias of the same type from initial table
     *
     * Step 1 : filter images (except logos) <== Current step
     * Step 2 : filter gifs
     * Step 3 : filter videos
     *
     * @return int[]
     */
    public function getIdsOfNotNormalizedProcessedMediasByProjectScreen(ProjectScreen $projectScreen): array;

    public function updateRawMediaName(int $processedMediaId, string $name): ProcessedMedia;

    public function getAllByIdsWithLastRenderAndRawMedia(array $processedMediaIds): Collection;

    /**
     * @param int[] $processedMediaIds
     * @param int[] $teamIds
     */
    public function getNbVisibleProcessedMedias(array $processedMediaIds, array $teamIds): int;
}
