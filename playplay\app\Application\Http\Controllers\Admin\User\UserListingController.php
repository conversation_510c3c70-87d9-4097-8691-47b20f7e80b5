<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\User;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Models\Company;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

final class UserListingController extends BaseController
{
    public function __construct()
    {
        $this->authorizeResource(User::class);
    }

    public function filterCompany(Request $request): JsonResponse
    {
        $user = User::find($request->get('user_id'));
        $actualTeams = [];
        $allTeams = [];
        if ($user !== null && $user->company_id === $request->get('company_id')) {
            $allTeams = $user->company->teams->pluck('name', 'id')->toArray();
            $actualTeams = $user->teams->pluck('name', 'id')->toArray();
        }

        if ($allTeams === []) {
            $company = Company::find($request->get('company_id'));
            $allTeams = $company != null ? $company->teams->pluck('name', 'id')->toArray() : [];
        }

        return new JsonResponse(
            ['success' => true, 'allTeams' => $allTeams, 'actualTeams' => $actualTeams],
            JsonResponse::HTTP_CREATED
        );
    }

    public function filters(Request $request): JsonResponse
    {
        $term = $request->get('term');
        $companyId = $request->get('company_id');
        $teamId = $request->get('team_id');

        /** TODO Move this request to a repository */
        $users = User::select('id', 'email', 'company_id', DB::raw("CONCAT(first_name, ' ', last_name) as text"))
            ->when($companyId, function (Builder $query) use ($companyId) {
                return $query->where('company_id', $companyId);
            })
            ->when($teamId, function (Builder $query) use ($teamId) {
                return $query->whereHas('teams', function (Builder $query) use ($teamId) {
                    return $query->where('teams.id', $teamId);
                });
            })
            ->when($term, function (Builder $query) use ($term) {
                return $query->searchByName($term);
            })
            ->simplePaginate();

        return new JsonResponse(['success' => true, 'results' => $users], JsonResponse::HTTP_OK);
    }
}
