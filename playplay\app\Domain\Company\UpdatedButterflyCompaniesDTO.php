<?php

declare(strict_types=1);

namespace App\Domain\Company;

use App\Models\Company;

final class UpdatedButterflyCompaniesDTO
{
    /** @var Company[] */
    public readonly array $payingCompanies;

    /** @var Company[] */
    public readonly array $nonPayingCompanies;

    public function __construct(array $payingCompanies, array $nonPayingCompanies)
    {
        $this->payingCompanies = $payingCompanies;
        $this->nonPayingCompanies = $nonPayingCompanies;
    }
}
