<?php

declare(strict_types=1);

namespace App\Domain\ProcessedMedia\Transformations;

use App\Models\ProcessedMedia;

/**
 * @deprecated this serializer will be removed once every media used normalize files
 */
class LegacyRenderingTransformationsSerializer implements TransformationsSerializer
{
    public function serialize(ProcessedMedia $processedMedia): array
    {
        return [
            'keep_size' => $processedMedia->lastRender?->data?->getKeepSize(),
        ];
    }
}
