<?php

declare(strict_types=1);

namespace App\Application\Events;

use App\Models\User;
use Illuminate\Queue\SerializesModels;

class SignedUpUser
{
    use SerializesModels;

    private User $user;
    private array $requestData;

    public function __construct(User $user, array $requestData)
    {
        $this->user = $user;
        $this->requestData = $requestData;
    }

    public function requestData(): array
    {
        return $this->requestData;
    }

    public function user(): User
    {
        return $this->user;
    }
}
