<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\V2\Stocks\StockController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class StockRouteMapper implements RouteMapper
{
    public function map(): void
    {
        $this->mapStockSearchRoutes();
        $this->mapStockLoadRoutes();
    }

    private function mapStockSearchRoutes(): void
    {
        Route::group([
            'prefix' => '/search',
            'as' => 'stock.search.',
        ], static function (Router $router) {
            $router->get('/image', [StockController::class, 'searchImages'])->name('image');
            $router->get('/video', [StockController::class, 'searchVideos'])->name('video');
            $router->get('/gif', [StockController::class, 'searchGifs'])->name('gif');
        });
    }

    private function mapStockLoadRoutes(): void
    {
        Route::group([
            'prefix' => '/stock',
            'as' => 'stock.',
        ], static function (Router $router) {
            $router->get('/image', [StockController::class, 'loadImages'])->name('image');
            $router->get('/video', [StockController::class, 'loadVideos'])->name('video');
            $router->get('/gif', [StockController::class, 'loadGifs'])->name('gif');
        });
    }
}
