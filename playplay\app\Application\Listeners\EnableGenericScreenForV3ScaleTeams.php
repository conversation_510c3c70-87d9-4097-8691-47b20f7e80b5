<?php

declare(strict_types=1);

namespace App\Application\Listeners;

use App\Application\Events\NewGenericScreenCreated;
use App\Domain\Screen\ScreenService;

final class EnableGenericScreenForV3ScaleTeams
{
    private ScreenService $screenService;

    public function __construct(ScreenService $screenService)
    {
        $this->screenService = $screenService;
    }

    public function handle(NewGenericScreenCreated $event): void
    {
        $this->screenService->enableScreenForAllTeams($event->getScreen());
    }
}
