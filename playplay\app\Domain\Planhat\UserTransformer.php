<?php

declare(strict_types=1);

namespace App\Domain\Planhat;

use App\Domain\Permissions\AppRoleRepository;
use App\Models\User;

final class UserTransformer
{
    private AppRoleRepository $appRoleRepository;

    public function __construct(AppRoleRepository $appRoleRepository)
    {
        $this->appRoleRepository = $appRoleRepository;
    }

    public function transform(User $user): array
    {
        $userTopAppRoleId = $user->top_app_role_id;
        $userTopAppRoleName = !$userTopAppRoleId ? null : $this->appRoleRepository->getById($userTopAppRoleId)->name;

        return [
            'email' => strtolower($user->email),
            'firstName' => $user->first_name,
            'lastName' => $user->last_name,
            'companyExternalId' => $user->company_id,
            'custom' => [
                'Higher role' => $userTopAppRoleName,
                'Active' => (bool) $user->active,
                'Language' => $user->language,
                'First login' => (bool) $user->first_logged_at,
                'User activity status' => $user->activity_status,
            ],
        ];
    }
}
