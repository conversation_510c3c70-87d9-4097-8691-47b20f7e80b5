<?php

namespace App\Application\Providers;

use App\Infrastructure\DataSeeding\DatabaseSeedsCleaner;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Support\ServiceProvider;
use Psr\Log\LoggerInterface;

class DatabaseSeedsCleanerProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(DatabaseSeedsCleaner::class, function (Application $app) {
            return new DatabaseSeedsCleaner(
                $app->get(LoggerInterface::class),
                $app->get('db.connection'),
                $this->app->tagged('orderedDatabaseSeeders')
            );
        });
    }
}
