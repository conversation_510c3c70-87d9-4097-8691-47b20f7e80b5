<?php

declare(strict_types=1);

namespace App\Application\Http\Middleware;

use App\Domain\User\UserRepository;
use Closure;
use Illuminate\Routing\ResponseFactory;
use Symfony\Component\HttpFoundation\Response;

final class UnauthorizeIfUserFromDateIsNotReached
{
    private UserRepository $userRepository;
    private ResponseFactory $factory;

    public function __construct(UserRepository $userRepository, ResponseFactory $responseFactory)
    {
        $this->userRepository = $userRepository;
        $this->factory = $responseFactory;
    }

    public function handle($request, Closure $next)
    {
        $user = $request->email !== null ? $this->userRepository->getUserFromEmail($request->email) : null;
        if ($user !== null && !$user->hasAccessPeriodStarted()) {
            return $this->factory->make(
                [
                    'type' => 'userAccessPeriodNotStartedYet',
                    'errors' => ['email.user_access_period_not_started_yet'],
                    'user_from' => $user->user_from,
                ],
                Response::HTTP_FORBIDDEN
            );
        }

        return $next($request);
    }
}
