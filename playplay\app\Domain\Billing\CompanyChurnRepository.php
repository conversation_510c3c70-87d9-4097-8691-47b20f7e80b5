<?php

declare(strict_types=1);

namespace App\Domain\Billing;

use App\Models\CompanyChurn;
use DateTimeInterface;

interface CompanyChurnRepository
{
    public function create(int $companyId, int $subscriptionId, int $userId, DateTimeInterface $churnedAt): void;

    public function explainChurn(CompanyChurn $companyChurn, string $reason, ?string $comment): void;

    public function updatePlanhatChurnId(CompanyChurn $companyChurn, string $planhatChurnId): void;
}
