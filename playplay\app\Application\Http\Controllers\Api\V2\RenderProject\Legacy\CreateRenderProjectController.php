<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\RenderProject\Legacy;

use App\Application\Events\UserGenerateVideoOrRender;
use App\Application\Http\Controllers\Api\BaseController;
use App\Domain\Render\RenderProject\RenderProjectFactory;
use App\Models\Project;
use App\Services\Project\Validator;
use App\Services\Rendering\WorkflowManager;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

/**
 * @deprecated This code will only be used for Stories Rendering while a new way is provided by the Motion Team.
 *             Other formats will be rendered following another way (Project Mode instead of Screen Mode)
 * @see        \App\Application\Http\Controllers\Api\V2\RenderProject\CreateRenderProjectController
 */
final class CreateRenderProjectController extends BaseController
{
    private RenderProjectFactory $renderProjectFactory;
    private Dispatcher $dispatcher;
    private Validator $projectValidator;
    private WorkflowManager $workflowManager;

    public function __construct(
        RenderProjectFactory $renderProjectFactory,
        Dispatcher $dispatcher,
        Validator $projectValidator,
        WorkflowManager $workflowManager
    ) {
        $this->middleware('can:view,project');
        $this->renderProjectFactory = $renderProjectFactory;
        $this->dispatcher = $dispatcher;
        $this->projectValidator = $projectValidator;
        $this->workflowManager = $workflowManager;
    }

    public function __invoke(Project $project): JsonResponse
    {
        $errors = $this->projectValidator->getProjectErrors($project);
        if ($errors !== []) {
            return $this->sendJsonResponseErrorFromArray(
                new Collection($errors),
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }

        $renderProject = $this->renderProjectFactory->create($project);

        // In case all render projects are processed, dispatch final jobs and transitions
        $this->workflowManager->dispatchJobsAndTransitions($renderProject);

        // Call generate render event
        $this->dispatcher->dispatch(new UserGenerateVideoOrRender($renderProject));

        return $this->sendJsonResponse(new Collection([$renderProject]), Response::HTTP_CREATED);
    }
}
