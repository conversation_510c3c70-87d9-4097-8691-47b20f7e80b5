<?php

namespace App\Application\Http\Controllers\Admin;

use App\Models\Project;
use App\Models\Renders\ARender;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;

class AbstractRenderController extends BaseController
{
    protected function applyFilters(EloquentBuilder $query, array $filters = []): EloquentBuilder
    {
        $filters = (new Collection($filters))->filter(function ($value) {
            return strlen($value) > 0;
        });

        if ($this->shouldJoinCompaniesTable($filters)) {
            $query->join('companies as c', 'c.id', '=', 'projects.company_id');
        }

        foreach ($filters as $key => $filter) {
            if ($key === 'projects.status') {
                if ($filter) {
                    $query->where($key, $filter);
                } else {
                    $query->where($key, '<>', Project::STATUS_DOWNLOADED);
                }
            } elseif ($key === 'with_error') {
                if ($filter === '1') {
                    $query->join('render_projects as r', 'r.project_id', '=', 'projects.id')
                        ->where('r.status', '=', ARender::STATUS_CANCELED)
                        ->where('projects.status', '<>', Project::STATUS_DRAFT);
                }
            } elseif ($key === 'screen_id') {
                $query->join('project_screens', function ($join) use ($filter) {
                    $join->on('project_screens.project_id', '=', 'projects.id')
                        ->where('project_screens.screen_id', $filter)
                        ->whereNull('project_screens.deleted_at');
                });
            } elseif ($key === 'company_type') {
                $query->where('c.type', $filter);
            } elseif ($key === 'company_csm') {
                $query->where('c.csm_id', $filter);
            } else {
                $query->where($key, $filter);
            }
        }

        return $query;
    }

    protected function export(EloquentBuilder $query): Response
    {
        $query->where('companies.name', '<>', 'PlayPlay');

        $renders = $query->limit(10000)->get();

        return response()->csv($renders);
    }

    private function shouldJoinCompaniesTable(Collection $filters): bool
    {
        $keys = $filters->keys();

        return $keys->contains('company_type') || $keys->contains('company_csm');
    }
}
