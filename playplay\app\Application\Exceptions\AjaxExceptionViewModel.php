<?php

declare(strict_types=1);

namespace App\Application\Exceptions;

use App\Domain\Billing\BillingException;
use App\Domain\Stock\StockException;
use App\Domain\UserTeam\UnableToAddUserToTeamException;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Throwable;

final class AjaxExceptionViewModel
{
    public static function fromException(Throwable $exception): self
    {
        return new self(
            self::getErrorCodeFromException($exception),
            self::getMessagesFromException($exception)
        );
    }

    private static function getErrorCodeFromException(Throwable $exception): int
    {
        switch (true) {
            case $exception instanceof HttpException:
                return $exception->getStatusCode();
            case $exception instanceof AuthenticationException:
                return 401;
            case $exception instanceof BillingException:
                return $exception->getCode() ?: 500;
            case $exception instanceof AuthorizationException:
                return 403;
            case $exception instanceof ModelNotFoundException:
                return 404;
            case $exception instanceof UnableToAddUserToTeamException:
                return 409;
            case $exception instanceof ValidationException:
                return 422;
            case $exception instanceof StockException:
                return 523;
            default:
                return 500;
        }
    }

    /**
     * @return string[]
     */
    private static function getMessagesFromException(Throwable $exception): array
    {
        if ($exception instanceof ValidationException) {
            $messages = [];
            $errorFields = $exception->validator->failed();
            foreach ($errorFields as $key => $listOfValidationError) {
                foreach (array_keys($listOfValidationError) as $error) {
                    $messages[] = $key . '.' . strtolower($error);
                }
            }

            return $messages;
        }

        // Obfuscate AStockException messages
        if ($exception instanceof StockException) {
            return ['Error querying the stock provider'];
        }

        if (is_array(json_decode($exception->getMessage(), false))) {
            return json_decode($exception->getMessage(), true);
        }

        return [$exception->getMessage()];
    }

    /** @var int */
    private $errorCode;
    /** @var string[] */
    private $messages;

    /**
     * @param string[] $messages
     */
    private function __construct(int $errorCode, array $messages)
    {
        $this->errorCode = $errorCode;
        $this->messages = $messages;
    }

    public function getResponse(): JsonResponse
    {
        return new JsonResponse([
            'errors' => $this->messages,
            'nb_errors' => count($this->messages),
        ], $this->errorCode);
    }
}
