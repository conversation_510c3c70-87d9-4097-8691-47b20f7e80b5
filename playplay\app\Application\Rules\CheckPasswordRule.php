<?php

namespace App\Application\Rules;

use Illuminate\Contracts\Validation\Rule;

class CheckPasswordRule implements Rule
{
    /**
     * @inheritDoc
     */
    public function message(): string
    {
        return 'This password is not valid';
    }

    /**
     * @inheritDoc
     */
    public function passes($attribute, $value): bool
    {
        return preg_match('/^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z]).{8,}$/', $value) === 1;
    }
}
