<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\Auth;

use Illuminate\Foundation\Http\FormRequest;

class LoginSSORequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'email' => ['required', 'email'],
            'redirectUri' => ['sometimes', 'url'],
        ];
    }
}
