<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\TimecodedElement;

use App\Application\Http\Controllers\Api\BaseController;
use App\Domain\TimecodedElement\TimecodedElementService;
use App\Models\ProjectScreen;
use App\Models\TimecodedElement\TimecodedElement;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

final class DeleteAllTimecodedElementsByProjectScreenController extends BaseController
{
    use AuthorizesRequests;

    private TimecodedElementService $timecodedElementService;

    public function __construct(
        TimecodedElementService $timecodedElementService,
    ) {
        $this->timecodedElementService = $timecodedElementService;
    }

    /**
     * @throws AuthorizationException|ValidationException|ModelNotFoundException
     */
    public function __invoke(ProjectScreen $projectScreen): JsonResponse
    {
        $this->authorize('deleteAll', [TimecodedElement::class, $projectScreen]);

        $this->timecodedElementService->deleteAllByProjectScreen($projectScreen);

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }
}
