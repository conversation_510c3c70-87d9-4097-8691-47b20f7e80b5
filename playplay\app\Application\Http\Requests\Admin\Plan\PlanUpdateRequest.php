<?php

namespace App\Application\Http\Requests\Admin\Plan;

use Illuminate\Foundation\Http\FormRequest;

class PlanUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'displayed_name' => ['required', 'string'],
            'feature_items' => ['required', 'array'],
            'feature_items.*' => ['nullable', 'integer'],
        ];
    }
}
