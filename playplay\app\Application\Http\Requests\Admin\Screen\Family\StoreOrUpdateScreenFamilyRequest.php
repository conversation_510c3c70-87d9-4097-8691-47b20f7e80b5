<?php
declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\Screen\Family;

use Illuminate\Foundation\Http\FormRequest;

class StoreOrUpdateScreenFamilyRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:100'],
            'screen_ids' => ['exists:screens,id'],
        ];
    }
}
