<?php

declare(strict_types=1);

namespace App\Application\Listeners;

use App\Application\Events\SignedUpUser;
use App\Models\User;
use App\Services\HubspotService;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Str;

final class CreateOrUpdateHubspotContact
{
    private const EMAILS_BLACKLIST_KEYWORDS = [
        'playplay.com',
        'playplay.video',
        'nightwatch',
    ];

    private HubspotService $hubspotService;

    public function __construct(HubspotService $hubspotService)
    {
        $this->hubspotService = $hubspotService;
    }

    public function handle(SignedUpUser $event): void
    {
        if ($this->shouldSendDataToHubspot($event->user())) {
            $this->hubspotService->createOrUpdateContact($event->user(), $event->requestData());
        }
    }

    private function shouldSendDataToHubspot(User $user): bool
    {
        $envIsProduction = App::environment() === 'production';
        $userIsNotFromPlayPlay = !Str::contains($user->email, self::EMAILS_BLACKLIST_KEYWORDS);

        return $envIsProduction && $userIsNotFromPlayPlay;
    }
}
