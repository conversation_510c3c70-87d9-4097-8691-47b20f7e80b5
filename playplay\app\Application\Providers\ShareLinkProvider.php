<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\ShareableLink\ShareableLinkRepository;
use App\Domain\ShareableLink\ShareableLinkSerializerInterface;
use App\Domain\ShareableLink\ShareableLinkTokenGeneratorInterface;
use App\Infrastructure\ShareableLink\EloquentShareableLinkRepository;
use App\Infrastructure\ShareableLink\ShareableLinkSerializer;
use App\Infrastructure\ShareableLink\UuidShareableLinkTokenGenerator;
use Illuminate\Support\ServiceProvider;

final class ShareLinkProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(ShareableLinkRepository::class, EloquentShareableLinkRepository::class);
        $this->app->bind(ShareableLinkSerializerInterface::class, ShareableLinkSerializer::class);
        $this->app->bind(ShareableLinkTokenGeneratorInterface::class, UuidShareableLinkTokenGenerator::class);
    }
}
