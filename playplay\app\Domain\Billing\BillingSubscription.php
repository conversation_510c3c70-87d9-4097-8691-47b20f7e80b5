<?php

declare(strict_types=1);

namespace App\Domain\Billing;

use DateTimeImmutable;

final class BillingSubscription
{
    public readonly string $status;

    public readonly int $currentPeriodEndTimestamp;

    public function __construct(string $status, int $currentPeriodEnd)
    {
        $this->status = $status;
        $this->currentPeriodEndTimestamp = $currentPeriodEnd;
    }

    public function getCurrentPeriodEndDate(): DateTimeImmutable
    {
        return (new DateTimeImmutable())->setTimestamp($this->currentPeriodEndTimestamp);
    }
}
