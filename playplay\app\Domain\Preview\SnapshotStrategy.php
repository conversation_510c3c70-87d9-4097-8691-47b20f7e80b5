<?php

declare(strict_types=1);

namespace App\Domain\Preview;

use App\Domain\Project\ProjectRepository;
use App\Domain\ProjectScreen\ProjectScreenRepository;
use App\Domain\User\Action\UserAction;
use App\Domain\User\Action\UserActionService;
use App\Models\Snapshot;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use LogicException;

final class SnapshotStrategy
{
    private UserActionService $userActionService;
    private ProjectSnapshotFactory $projectSnapshotFactory;
    private ProjectScreenSnapshotFactory $projectScreenSnapshotFactory;
    private ProjectScreenRepository $projectScreenRepository;
    private ProjectRepository $projectRepository;

    public function __construct(
        ProjectSnapshotFactory $projectSnapshotFactory,
        ProjectScreenSnapshotFactory $projectScreenSnapshotFactory,
        UserActionService $userActionService,
        ProjectRepository $projectRepository,
        ProjectScreenRepository $projectScreenRepository
    ) {
        $this->projectSnapshotFactory = $projectSnapshotFactory;
        $this->projectScreenSnapshotFactory = $projectScreenSnapshotFactory;
        $this->userActionService = $userActionService;
        $this->projectRepository = $projectRepository;
        $this->projectScreenRepository = $projectScreenRepository;
    }

    /**
     * @throws AuthorizationException
     * @throws ModelNotFoundException
     * @throws LogicException
     */
    public function execute(string $type, int $id): Snapshot
    {
        if ($type === Snapshot::TYPE_PROJECT) {
            $project = $this->projectRepository->findOneById($id);
            if ($project === null) {
                throw new ModelNotFoundException();
            }

            $this->userActionService->addUserAction(
                new UserAction(
                    'project-previewed',
                    [],
                    $project->team_id,
                    $project->id,
                )
            );

            return $this->projectSnapshotFactory->generate($project);
        }

        if ($type === Snapshot::TYPE_PROJECT_SCREEN) {
            $projectScreen = $this->projectScreenRepository->getById($id);
            if ($projectScreen === null) {
                throw new ModelNotFoundException();
            }

            return $this->projectScreenSnapshotFactory->generate($projectScreen);
        }

        throw new LogicException('Snapshot type not found');
    }
}
