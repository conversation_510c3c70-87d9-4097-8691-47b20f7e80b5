<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\TeamUser;

use App\Application\Http\Controllers\Api\TeamUser\UpdateUserTeamAppPermissionController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class TeamUserRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::put('teams/{team}/users/{user}/app-permissions', UpdateUserTeamAppPermissionController::class)
            ->name('teams.users.app-permissions');
    }
}
