<?php

declare(strict_types=1);

namespace App\Domain\CustomPage;

final class CreateCustomPageDTO
{
    public readonly string $token;
    public readonly int $projectId;
    public readonly string $title;
    public readonly string $primaryColor;
    public readonly int $fontId;
    public readonly ?int $mainLogoProcessedMediaId;

    public function __construct(
        string $token,
        int $projectId,
        string $title,
        string $primaryColor,
        int $fontId,
        ?int $mainLogoProcessedMediaId
    ) {
        $this->token = $token;
        $this->projectId = $projectId;
        $this->title = $title;
        $this->primaryColor = $primaryColor;
        $this->fontId = $fontId;
        $this->mainLogoProcessedMediaId = $mainLogoProcessedMediaId;
    }
}
