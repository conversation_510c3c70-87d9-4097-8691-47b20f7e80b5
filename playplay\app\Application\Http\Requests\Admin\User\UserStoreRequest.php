<?php

namespace App\Application\Http\Requests\Admin\User;

use App\Domain\Localization\SupportedLanguages;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UserStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'first_name' => ['required_without:last_name', 'max:50'],
            'last_name' => ['required_without:first_name', 'max:50'],
            'email' => ['required', 'email', ' max:255', 'unique:users'],
            'phone' => ['sometimes', 'max:30'],
            'language' => ['sometimes', Rule::in(SupportedLanguages::values())],
            'company_id' => ['required', 'exists:companies,id'],
            'teams' => ['required', 'array'],
            'teams.*.id' => ['sometimes', 'exists:teams,id'],
            'teams.*.app_role_id' => ['sometimes', 'integer', 'exists:app_roles,id'],
            'is_active' => ['sometimes', 'boolean'],
            'roles' => ['sometimes', 'exists:roles,id'],
            'user_from' => ['required', 'date', 'after_or_equal:' . date('Y-m-d')],
            'user_until' => ['sometimes', 'date', 'after:user_from'],
        ];
    }
}
