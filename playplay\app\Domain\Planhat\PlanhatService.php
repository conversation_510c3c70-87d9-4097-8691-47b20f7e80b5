<?php

declare(strict_types=1);

namespace App\Domain\Planhat;

interface PlanhatService
{
    /**
     * @throws PlanhatAPIException
     */
    public function insertCompanyUsage(string $dateStart): void;

    /**
     * @throws PlanhatAPIException
     */
    public function insertUserUsage(string $dateStart): void;

    /**
     * @throws PlanhatAPIException
     */
    public function upsertCompanies(): void;

    /**
     * @throws PlanhatAPIException
     */
    public function upsertUsers(): void;
}
