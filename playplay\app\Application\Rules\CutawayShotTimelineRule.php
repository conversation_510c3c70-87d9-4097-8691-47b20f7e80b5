<?php

namespace App\Application\Rules;

use App\Domain\CutawayShot\Validators\CutawayShotTimelineRuleValidator;
use Illuminate\Contracts\Validation\Rule;

class CutawayShotTimelineRule implements Rule
{
    private CutawayShotTimelineRuleValidator $validator;

    public function __construct(CutawayShotTimelineRuleValidator $validator)
    {
        $this->validator = $validator;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message(): string
    {
        return trans('validation.is_valid_timeline');
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed  $value
     *
     * @return bool
     */
    public function passes($attribute, $value): bool
    {
        if (!is_array($value)) {
            return false;
        }

        return $this->validator->isValid($value);
    }
}
