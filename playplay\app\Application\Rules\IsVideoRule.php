<?php

namespace App\Application\Rules;

use App\Models\RawMedia;
use Illuminate\Contracts\Validation\Rule;

class IsVideoRule implements Rule
{
    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('validation.is_video');
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed  $value
     *
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if ($attribute !== 'raw_media_id') {
            return false;
        }

        /** @var RawMedia $rawMedia */
        $rawMedia = RawMedia::find($value);

        return $rawMedia !== null && $rawMedia->isVideo();
    }
}
