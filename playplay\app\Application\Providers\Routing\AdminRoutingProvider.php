<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing;

use App\Application\Providers\Routing\Admin\BillingPlanRouteMapper;
use App\Application\Providers\Routing\Admin\CompanyRouteMapper;
use App\Application\Providers\Routing\Admin\ConfigRouteMapper;
use App\Application\Providers\Routing\Admin\HomeRouteMapper;
use App\Application\Providers\Routing\Admin\MaintenanceRouteMapper;
use App\Application\Providers\Routing\Admin\MusicListRouteMapper;
use App\Application\Providers\Routing\Admin\OptionRouteMapper;
use App\Application\Providers\Routing\Admin\PermissionRouteMapper;
use App\Application\Providers\Routing\Admin\PlanRouteMapper;
use App\Application\Providers\Routing\Admin\ProjectRouteMapper;
use App\Application\Providers\Routing\Admin\RenderJobRouteMapper;
use App\Application\Providers\Routing\Admin\RenderMediaRouteMapper;
use App\Application\Providers\Routing\Admin\RenderProjectHtmlRouteMapper;
use App\Application\Providers\Routing\Admin\RenderProjectMapper;
use App\Application\Providers\Routing\Admin\RoleRouteMapper;
use App\Application\Providers\Routing\Admin\ScreenRouteMapper;
use App\Application\Providers\Routing\Admin\TeamPresetRouteMapper;
use App\Application\Providers\Routing\Admin\TeamRouteMapper;
use App\Application\Providers\Routing\Admin\TemplateRouteMapper;
use App\Application\Providers\Routing\Admin\TimecodedElement\TimecodedElementsFamilyRouteMapper;
use App\Application\Providers\Routing\Admin\UserRouteMapper;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider;
use Illuminate\Support\Facades\Route;

final class AdminRoutingProvider extends RouteServiceProvider
{
    /** @var RouteMapper[] */
    private array $routeMappers;

    public function __construct(Application $app)
    {
        parent::__construct($app);
        // Declare all route mappers
        $this->routeMappers = [
            new BillingPlanRouteMapper(),
            new CompanyRouteMapper(),
            new ConfigRouteMapper(),
            new HomeRouteMapper(),
            new MaintenanceRouteMapper(),
            new MusicListRouteMapper(),
            new OptionRouteMapper(),
            new PermissionRouteMapper(),
            new PlanRouteMapper(),
            new ProjectRouteMapper(),
            new RenderJobRouteMapper(),
            new RenderMediaRouteMapper(),
            new RenderProjectHtmlRouteMapper(),
            new RenderProjectMapper(),
            new RoleRouteMapper(),
            new ScreenRouteMapper(),
            new TeamPresetRouteMapper(),
            new TeamRouteMapper(),
            new TemplateRouteMapper(),
            new TimecodedElementsFamilyRouteMapper(),
            new UserRouteMapper(),
        ];
    }

    public function map(): void
    {
        Route::group([
            'middleware' => ['web', 'auth', 'admin'],
            'prefix' => 'admin',
            'as' => 'admin.',
        ], function () {
            foreach ($this->routeMappers as $routeMapper) {
                $routeMapper->map();
            }
        });
    }
}
