<?php

namespace App\Application\Console\Commands;

use App\Models\User;
use Carbon\Carbon;
use Carbon\CarbonInterface;
use Carbon\CarbonPeriod;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class FillDailyUsageTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bi:daily_usage {date_start? : Date start or specific date} {date_end? : Date end}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Compute and fill daily usage table for zoho';

    public function handle(): int
    {
        $dateStart = $this->argument('date_start');
        $dateEnd = $this->argument('date_end');

        if ($dateStart && $dateEnd) {
            // Period
            $period = CarbonPeriod::create($dateStart, $dateEnd);
        } elseif ($dateStart) {
            // Specific date
            $period[] = Carbon::parse($dateStart);
        } else {
            // Yesterday
            $period[] = Carbon::now()->subDays(1);
        }

        foreach ($period as $date) {
            $this->insertUsers($date);
            $this->insertProjectDownloadDailyUsage($date);
        }

        return 0;
    }

    private function insertProjectDownloadDailyUsage($date): void
    {
        $dateStart = $date->copy()->startOfDay();
        $dateEnd = $date->copy()->endOfDay();
        $today = $date->format('Y-m-d');

        $sqlQuery = <<<SQL
SELECT
    count(vd.user_id) as nb_download,
    vd.user_id,
    vd.company_id,
    vd.company_type,
    projects.team_id
FROM video_downloads vd
INNER JOIN (
    SELECT min(created_at) as min_created_at, project_id, min(id) as min_id
    FROM video_downloads
    GROUP BY project_id
    HAVING min_created_at >= "$dateStart"
       AND min_created_at <= "$dateEnd"

) vd_min ON (vd_min.min_id=vd.id)
INNER JOIN projects ON (vd_min.project_id = projects.id)
INNER JOIN companies ON (vd.company_id = companies.id)
WHERE vd.company_id IS NOT NULL
  AND projects.team_id IS NOT NULL
  AND (companies.client_until IS NULL OR companies.client_until > "$dateStart")
GROUP BY vd.user_id,
         vd.company_id,
         vd.company_type,
         projects.team_id
SQL;

        $results = new Collection(DB::select(DB::raw($sqlQuery)));
        $chunkedResults = $results->chunk(200);

        foreach ($chunkedResults as $chunkedResult) {
            foreach ($chunkedResult as $videoDownloadData) {
                DB::table('daily_usages')->updateOrinsert(
                    [
                        'date' => $today,
                        'user_id' => $videoDownloadData->user_id,
                        'team_id' => $videoDownloadData->team_id,
                        'company_id' => $videoDownloadData->company_id,
                        'company_type' => $videoDownloadData->company_type,
                    ],
                    [
                        'nb_first_download' => $videoDownloadData->nb_download,
                    ]
                );
            }
        }
    }

    private function insertUsers(CarbonInterface $date): void
    {
        $today = $date->format('Y-m-d');
        User::withTrashed()
            ->select('users.*')
            ->join('companies', 'companies.id', 'users.company_id')
            ->whereRaw('DATE(users.created_at) <= "' . $date . '"')
            ->where(function ($query) use ($date) {
                $query->whereRaw('DATE(users.deleted_at) >= "' . $date . '"')
                    ->orWhereNull('users.deleted_at');
            })
            ->where(function ($query) use ($date) {
                $query->whereRaw('DATE(companies.client_until) >= "' . $date . '"')
                    ->orWhereNull('companies.client_until');
            })
            ->where(function ($query) use ($date) {
                $query->whereRaw('DATE(companies.deleted_at) >= "' . $date . '"')
                    ->orWhereNull('companies.deleted_at');
            })
            ->with('teams', 'company')
            ->chunkById(
                500,
                static function (Collection $users) use ($today) {
                    $usersDataToInsert = [];

                    foreach ($users as $user) {
                        foreach ($user->teams as $team) {
                            $usersDataToInsert[] =
                                [
                                    'date' => $today,
                                    'user_id' => $user->id,
                                    'team_id' => $team->id,
                                    'company_id' => $user->company->id,
                                    'company_type' => $user->company->type,
                                    'nb_first_download' => 0,
                                ];
                        }
                    }

                    DB::table('daily_usages')->insertOrIgnore($usersDataToInsert);
                },
                'users.id',
                'id'
            );
    }
}
