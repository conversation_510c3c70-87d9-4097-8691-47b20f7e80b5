<?php

namespace App\Domain\CutawayShot;

use App\Domain\ProjectScreen\Param\ProjectScreenParamRepository;
use App\Domain\Render\RenderMedia\LegacyRenderMediaDataFactory;
use App\Domain\Render\RenderMedia\RenderMediaRepository;
use App\Models\Renders\RenderMedia;

class CutawayShotTimelineService
{
    private ProjectScreenParamRepository $projectScreenParamRepository;
    private RenderMediaRepository $renderMediaRepository;

    public function __construct(
        ProjectScreenParamRepository $projectScreenParamRepository,
        RenderMediaRepository $renderMediaRepository
    ) {
        $this->projectScreenParamRepository = $projectScreenParamRepository;
        $this->renderMediaRepository = $renderMediaRepository;
    }

    public function resolveOverlappingItems(int $cutawayShotParamId, int $projectScreenId): void
    {
        /** @var RenderMedia[] $renderMediasSortedByStart */
        $renderMediasSortedByStart = $this->projectScreenParamRepository
            ->getByParamIdAndProjectScreenId($cutawayShotParamId, $projectScreenId)
            ->load('processedMedia.lastRender')
            ->pluck('processedMedia.lastRender')
            ->sort(static function (RenderMedia $renderMediaA, RenderMedia $renderMediaB) {
                return $renderMediaA->data->getStart() <=> $renderMediaB->data->getStart();
            })
            ->all();

        $minStartAllowed = 0;
        foreach ($renderMediasSortedByStart as $renderMedia) {
            $actualStart = $renderMedia->data->getStart();

            if ($actualStart < $minStartAllowed) {
                $this->renderMediaRepository->updateData(
                    $renderMedia,
                    LegacyRenderMediaDataFactory::createProcessedMediaData(['start' => $minStartAllowed,])
                );
                $actualStart = $minStartAllowed;
            }

            $minStartAllowed = $actualStart + $renderMedia->data->getDuration();
        }
    }
}
