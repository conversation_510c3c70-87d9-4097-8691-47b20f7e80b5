<?php

namespace App\Application\Http\Controllers\Api\V2\User;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\User\Notification\UserNotificationUpdateManyRequest;
use App\Models\Notification;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;

class UserNotificationController extends BaseController
{
    use AuthorizesRequests;

    public function updateMany(UserNotificationUpdateManyRequest $request): JsonResponse
    {
        $notificationIds = $request->get('notifications');
        $this->authorize('update-many', [Notification::class, $notificationIds]);

        Notification::whereIn('id', $notificationIds)->update(['viewed' => true]);

        return $this->sendJsonResponse(new Collection([]), JsonResponse::HTTP_OK);
    }
}
