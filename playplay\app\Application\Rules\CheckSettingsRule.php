<?php

namespace App\Application\Rules;

use Illuminate\Contracts\Validation\Rule;

class CheckSettingsRule implements Rule
{
    private const FIELDS = ['screen_duration', 'show_logos'];

    private static function audioLevelsAreSetAndInRange(array $value): bool
    {
        return array_diff(self::FIELDS, array_keys($value)) === [];
    }

    public function passes($attribute, $value): bool
    {
        return $value === null || (self::audioLevelsAreSetAndInRange($value));
    }

    public function message(): string
    {
        return 'CheckSettingsRule is invalid';
    }
}
