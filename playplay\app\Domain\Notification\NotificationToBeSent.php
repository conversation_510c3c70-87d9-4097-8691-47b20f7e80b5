<?php

declare(strict_types=1);

namespace App\Domain\Notification;

final class NotificationToBeSent
{
    public readonly string $type;
    public readonly string $content;
    public readonly string|null $username;
    public readonly string $url;
    public readonly int $userId;

    public function __construct(
        string $type,
        NotificationContent $notificationContent,
        ?string $username,
        string $url,
        int $userId
    ) {
        $this->type = $type;
        $this->content = $notificationContent->content;
        $this->username = $username;
        $this->url = $url;
        $this->userId = $userId;
    }
}
