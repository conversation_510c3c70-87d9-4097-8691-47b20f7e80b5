<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\TransferMedia;

use App\Application\Http\Requests\Api\TransferMedia\GetUploadedFilesFromTransferMediaForProjectRequest;
use App\Domain\Project\ProjectRepository;
use App\Domain\RawMedia\RawMediaRepository;
use App\Models\RawMedia;
use Illuminate\Routing\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

final class GetUploadedFilesFromTransferMediaForProjectController extends Controller
{
    private RawMediaRepository $rawMediaRepository;
    private ProjectRepository $projectRepository;

    public function __construct(ProjectRepository $projectRepository, RawMediaRepository $rawMediaService)
    {
        $this->rawMediaRepository = $rawMediaService;
        $this->projectRepository = $projectRepository;
    }

    public function __invoke(GetUploadedFilesFromTransferMediaForProjectRequest $request): JsonResponse
    {
        $projectRawMedias = $this->rawMediaRepository->getAllUploadedTransferMediaFor(
            $this->projectRepository->findOneByUuid($request->get('uuid'))
        );

        return new JsonResponse(
            $projectRawMedias->map(fn(RawMedia $rawMedia) => $rawMedia->toVue()),
            Response::HTTP_OK
        );
    }
}
