<?php

namespace App\Application\Policies;

use App\Models\Company;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class CompanyPolicy extends DefaultPolicy
{
    use HandlesAuthorization;

    public function before(User $authUser, $ability, $company = null): ?bool
    {
        if ($ability === 'canAccessRestrictedData') {
            return null;
        }

        if ($authUser->can('manage-companies')) {
            return true;
        }

        return null;
    }

    public function show(User $user, Company $company): bool
    {
        return $user->company_id === $company->id;
    }

    public function canAccessRestrictedData(User $user, Company $company): bool
    {
        return !($company->data_is_restricted && $user->isUsAdminPlayPlayer());
    }
}
