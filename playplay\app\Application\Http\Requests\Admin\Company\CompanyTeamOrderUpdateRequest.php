<?php

namespace App\Application\Http\Requests\Admin\Company;

use Illuminate\Foundation\Http\FormRequest;

class CompanyTeamOrderUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'team_orders' => ['array', 'required'],
            'team_orders.*' => ['integer', 'min:0'],
        ];
    }
}
