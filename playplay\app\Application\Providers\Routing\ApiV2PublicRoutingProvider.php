<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing;

use App\Application\Providers\Routing\Api\Auth\AuthRouteMapper;
use App\Application\Providers\Routing\Api\Auth\TwoFactorRouteMapper;
use App\Application\Providers\Routing\Api\CustomPage\CustomPagePublicRouteMapper;
use App\Application\Providers\Routing\Api\Project\ProjectPublicRouteMapper;
use App\Application\Providers\Routing\Api\ShareableLinkCommentRouteMapper;
use App\Application\Providers\Routing\Api\ShareableLinkPublicRouteMapper;
use App\Application\Providers\Routing\Api\SignupRouteMapper;
use App\Application\Providers\Routing\Api\User\UserPublicRouteMapper;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider;
use Illuminate\Support\Facades\Route;

final class ApiV2PublicRoutingProvider extends RouteServiceProvider
{
    /** @var RouteMapper[] */
    private array $routeMappers;

    public function __construct(Application $app)
    {
        parent::__construct($app);
        // Declare all route mappers
        $this->routeMappers = [
            new AuthRouteMapper(),
            new ProjectPublicRouteMapper(),
            new ShareableLinkCommentRouteMapper(),
            new ShareableLinkPublicRouteMapper(),
            new SignupRouteMapper(),
            new TwoFactorRouteMapper(),
            new UserPublicRouteMapper(),
            new CustomPagePublicRouteMapper(),
        ];
    }

    public function map(): void
    {
        Route::group([
            'middleware' => ['api'],
            'prefix' => 'api/v2',
            'as' => 'api.v2.',
        ], function () {
            foreach ($this->routeMappers as $routeMapper) {
                $routeMapper->map();
            }
        });
    }
}
