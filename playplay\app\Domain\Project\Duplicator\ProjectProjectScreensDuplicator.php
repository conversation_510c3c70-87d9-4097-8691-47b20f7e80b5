<?php

declare(strict_types=1);

namespace App\Domain\Project\Duplicator;

use App\Domain\ProjectScreen\Duplicator\ProjectScreenDuplicator;
use App\Models\Project;
use App\Models\ProjectScreen;

final class ProjectProjectScreensDuplicator
{
    private ProjectScreenDuplicator $projectScreenDuplicator;

    public function __construct(ProjectScreenDuplicator $projectScreenDuplicator)
    {
        $this->projectScreenDuplicator = $projectScreenDuplicator;
    }

    public function duplicate(Project $sourceProject, Project $destinationProject): void
    {
        $duplicatedProjectScreens = $sourceProject
            ->projectScreens
            ->map(
                fn(ProjectScreen $projectScreen) => $this->projectScreenDuplicator->duplicate(
                    $projectScreen,
                    $destinationProject,
                    false
                )
            );

        $destinationProject->setRelation('projectScreens', $duplicatedProjectScreens);
    }
}
