<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\Project\ProjectScreen\Param\CutawayShot;

use App\Domain\RawMedia\RawMediaSource;
use App\Models\ProcessedMedia;
use App\Models\ScreenParams\BaseParam;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Routing\Route;

abstract class CutawayShotRequest extends FormRequest
{
    public function withValidator(Validator $validator): void
    {
        $validator->after(function (Validator $validator) {
            /** @var Route $route */
            $route = $this->route();
            $paramType = $route->parameter('param')?->type ?? null;

            if ($paramType !== BaseParam::TYPE_CUTAWAY_SHOT) {
                $validator->addFailure('param', 'validation.is_cutaway_shot');
            }
        });
    }

    protected function getAllowedSources(): array
    {
        return [
            RawMediaSource::GETTY,
            RawMediaSource::GETTY_EDITO,
            RawMediaSource::UPLOAD,
            RawMediaSource::STORYBLOCKS,
            RawMediaSource::UNSPLASH,
            ProcessedMedia::SOURCE_FAVORITES,
            ProcessedMedia::SOURCE_RECENTLY_USED,
            ProcessedMedia::SOURCE_LIBRARY,
        ];
    }
}
