<?php

namespace App\Application\Providers;

use App\Domain\Project\Serializer\ColorsSerializer;
use App\Domain\Project\Serializer\LogoSerializer;
use App\Domain\ProjectScreen\Serializer\GoogleIndexSerializer;
use App\Domain\ProjectScreen\Serializer\NullIndexSerializer;
use App\Domain\ProjectScreen\Serializer\OptionsSerializer as ProjectScreenOptionsSerializer;
use App\Domain\ProjectScreen\Serializer\ProjectScreenSerializer;
use App\Domain\ProjectScreen\Serializer\ProjectScreenToParameters;
use App\Domain\ProjectScreen\Serializer\SettingsSerializer;
use App\Infrastructure\ProjectScreen\Repositories\EloquentGoogleProjectScreenRepository;
use App\Infrastructure\TimecodedElement\Serializers\Animaniac\AnimaniacTimecodedElementsSerializer;
use Illuminate\Foundation\Application;
use Illuminate\Support\ServiceProvider;

class ProjectScreenSerializerProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(
            ProjectScreenSerializer::class,
            function (Application $app) {
                return new ProjectScreenSerializer(
                    $app[ProjectScreenToParameters::class],
                    $app[ProjectScreenOptionsSerializer::class],
                    $app[ColorsSerializer::class],
                    $app[GoogleIndexSerializer::class],
                    $app[NullIndexSerializer::class],
                    $app[SettingsSerializer::class],
                    $app[LogoSerializer::class],
                    $app[AnimaniacTimecodedElementsSerializer::class],
                    $app[EloquentGoogleProjectScreenRepository::class],
                );
            }
        );
    }
}
