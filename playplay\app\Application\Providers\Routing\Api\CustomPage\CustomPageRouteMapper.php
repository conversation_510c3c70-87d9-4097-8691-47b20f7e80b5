<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\CustomPage;

use App\Application\Http\Controllers\Api\V2\CustomPage\CreateCustomPageController;
use App\Application\Http\Controllers\Api\V2\CustomPage\GetCustomPageSettingsController;
use App\Application\Http\Controllers\Api\V2\CustomPage\SearchCustomPageController;
use App\Application\Http\Controllers\Api\V2\CustomPage\UpdateCustomPagePublicationController;
use App\Application\Http\Controllers\Api\V2\CustomPage\UpdateCustomPageSettingsController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class CustomPageRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/custom-page',
            'as' => 'custom-page.',
        ], static function (Router $router) {
            $router->post('/', CreateCustomPageController::class)->name('create');
            $router->get('/search', SearchCustomPageController::class)->name('search');
            $router->get('/{customPage}', GetCustomPageSettingsController::class)->name('get');
            $router
                ->put('/{customPage}/settings', UpdateCustomPageSettingsController::class)
                ->name('update');
            $router
                ->put('/{customPage}/publish', UpdateCustomPagePublicationController::class)
                ->name('update-publication');
        });
    }
}
