<?php

namespace App\Application\Policies;

use App\Models\Renders\RenderProjectHtml;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class RenderProjectHtmlPolicy extends DefaultPolicy
{
    use HandlesAuthorization;

    public function before(User $authUser, string $ability)
    {
        return $authUser->can('manage-renders') ?: null;
    }

    public function update(User $authUser, RenderProjectHtml $renderProjectHtml)
    {
        return $authUser->can('view', $renderProjectHtml->project);
    }
}
