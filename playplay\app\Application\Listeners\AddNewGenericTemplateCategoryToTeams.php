<?php

declare(strict_types=1);

namespace App\Application\Listeners;

use App\Application\Events\NewGenericTemplateCategoryIsCreated;
use App\Domain\Team\TeamRepository;

final class AddNewGenericTemplateCategoryToTeams
{
    private TeamRepository $teamRepository;

    public function __construct(TeamRepository $teamRepository)
    {
        $this->teamRepository = $teamRepository;
    }

    public function handle(NewGenericTemplateCategoryIsCreated $event): void
    {
        $this->teamRepository->linkGenericTemplateCategoryToTeamsThatHaveAutoAddNewGenericTemplateCategories(
            $event->getGenericTemplateCategory()
        );
    }
}
