<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\ProjectScreen;

use App\Application\Http\Controllers\Api\V2\ProjectScreen\Layout\ProjectScreenLayoutController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class ProjectScreenLayoutRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/layouts/{layout}',
            'as' => 'layouts.',
        ], static function (Router $router) {
            $router->post('', [ProjectScreenLayoutController::class, 'store'])->name('store');
            $router->put('', [ProjectScreenLayoutController::class, 'move'])->name('move');
            $router->delete('', [ProjectScreenLayoutController::class, 'destroy'])->name('destroy');
        });
    }
}
