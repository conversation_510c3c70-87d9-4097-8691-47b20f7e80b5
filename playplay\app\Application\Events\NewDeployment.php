<?php

namespace App\Application\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class NewDeployment implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    private const SOCKET_CHANNEL_NAME = 'deployments';
    private const SOCKET_EVENT_NAME = 'new-deployment';

    public function broadcastAs(): string
    {
        return self::SOCKET_EVENT_NAME;
    }

    public function broadcastOn(): array
    {
        return [self::SOCKET_CHANNEL_NAME];
    }
}
