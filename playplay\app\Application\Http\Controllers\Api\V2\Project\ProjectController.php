<?php

namespace App\Application\Http\Controllers\Api\V2\Project;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Project\ProjectIndexRequest;
use App\Domain\Company\Repositories\CompanyRepository;
use App\Models\Project;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

final class ProjectController extends BaseController
{
    use AuthorizesRequests;

    private Guard $guard;
    private CompanyRepository $companyRepository;

    public function __construct(
        Guard $guard,
        CompanyRepository $companyRepository
    ) {
        $this->guard = $guard;
        $this->companyRepository = $companyRepository;
    }

    public function companyIndex(ProjectIndexRequest $request): JsonResponse
    {
        $request->query->add(['require_last_rendered_url' => true]);

        return $this->index($request);
    }

    public function index(ProjectIndexRequest $request): JsonResponse
    {
        $requestHeader = $request->header('Authorization');
        $token = Str::startsWith($requestHeader, 'Bearer') ? null : $requestHeader;
        $page = $request->get('page', 1);
        $perPage = $request->get('per_page', 10);
        $user = $this->guard->user();
        $userId = $request->get('user_id');
        $teamId = $request->get('team_id');
        $videoId = $request->get('video_id');
        $format = $request->get('format');
        $updatedSince = $request->get('updated_since');
        $status = $request->get('status')
            ? $request->input('status')
            : [Project::STATUS_GENERATED, Project::STATUS_DOWNLOADED];
        $requireLastRenderedUrl = $request->get('require_last_rendered_url', false);

        if ($teamId !== null) {
            $this->authorize('canAccessRestrictedData', $this->companyRepository->getCompanyByTeamId($teamId));
        }

        /** @todo move this code to a repository */
        $paginator = Project::whereIn('projects.status', $status)
            ->where('is_team_template_project', false)
            ->when($requireLastRenderedUrl, function (QueryBuilder $qb) {
                return $qb->whereNotNull('projects.last_rendered_url');
            })
            ->when($userId, function (QueryBuilder $qb) use ($userId) {
                return $qb->where('user_id', $userId);
            })
            ->when($user, function (QueryBuilder $qb) use ($user) {
                return $qb->whereIn('team_id', $user->teams->pluck('id'));
            })
            ->when($format, function (QueryBuilder $qb) use ($format) {
                return $qb->where('format', $format);
            })
            ->when($teamId, function (QueryBuilder $qb) use ($teamId) {
                return $qb->where('team_id', $teamId);
            })
            ->when($videoId, function (QueryBuilder $qb) use ($videoId) {
                return $qb->whereIn('id', explode(',', $videoId));
            })
            ->when($token, function (QueryBuilder $qb) use ($token) {
                return $qb->join('companies', 'companies.id', '=', 'projects.company_id')
                    ->where('token', $token);
            })
            ->when($updatedSince, function (QueryBuilder $qb) use ($updatedSince) {
                return $qb->where('projects.updated_at', '>', $updatedSince);
            })
            ->when(
                $teamId !== null && !$user->company->hasPrivateProjectsFeatureEnabled(),
                function (QueryBuilder $qb) {
                    return $qb->where('projects.is_private', '=', 0);
                }
            )
            ->select('projects.*')
            ->orderBy('projects.updated_at', 'DESC')
            ->with([
                'user:id,email,first_name,last_name',
                'team:id,name',
                'template:id,name',
                'lastRenderProjectHtmlProcessed',
            ])
            ->paginate($perPage)
            ->appends([
                'page' => $page,
                'per_page' => $perPage,
                'user_id' => $userId,
                'team_id' => $teamId,
            ]);

        $paginator->getCollection()->transform(function (Project $project) {
            return [
                'id' => $project->id,
                'title' => $project->title,
                'format' => $project->format,
                'render_id' => $this->getLastRenderIdProcessedFor($project),
                'rendered_url' => $project->last_rendered_url,
                'thumbnail_url' => $project->last_thumbnail_url,
                'status' => $project->status,
                'is_private' => $project->is_private,
                'user_id' => $project->user !== null ? $project->user->id : null,
                'user_email' => $project->user !== null ? $project->user->email : null,
                'username' => $project->user !== null ? $project->user->name : null,
                'team_id' => $project->team !== null ? $project->team->id : null,
                'team_name' => $project->team !== null ? $project->team->name : null,
                'template_id' => $project->template !== null ? $project->template->id : null,
                'template_name' => $project->template !== null ? $project->template->name : null,
                'updated_at' => $project->updated_at->format('Y-m-d H:i:s'),
                'created_at' => $project->created_at->format('Y-m-d H:i:s'),
                'version' => $project->version,
            ];
        });

        return $this->sendJsonResponseFromPaginator($paginator, Response::HTTP_OK);
    }

    private function getLastRenderIdProcessedFor(Project $project): ?int
    {
        if ($project->lastRenderProjectHtmlProcessed === null) {
            return null;
        }

        return $project->lastRenderProjectHtmlProcessed->id;
    }
}
