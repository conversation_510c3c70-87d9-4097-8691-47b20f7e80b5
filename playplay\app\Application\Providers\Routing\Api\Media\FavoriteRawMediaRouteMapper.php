<?php

namespace App\Application\Providers\Routing\Api\Media;

use App\Application\Http\Controllers\Api\V2\Media\FavoriteRawMediaController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class FavoriteRawMediaRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/users/me/favorites',
            'as' => 'favorites.'
        ], static function (Router $router): void {
            $router
               ->get('/', [FavoriteRawMediaController::class, 'index'])
               ->name('index');
            $router
               ->post('/', [FavoriteRawMediaController::class, 'store'])
               ->name('store');
            $router
               ->delete('/{rawMedia}', [FavoriteRawMediaController::class, 'destroy'])
               ->name('destroy');
        });
    }
}
