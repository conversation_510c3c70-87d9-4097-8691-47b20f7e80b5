<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\RenderError\RenderErrorRepository;
use App\Infrastructure\RenderError\EloquentRenderErrorRepository;
use Illuminate\Support\ServiceProvider;

final class RenderErrorServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(RenderErrorRepository::class, EloquentRenderErrorRepository::class);
    }
}
