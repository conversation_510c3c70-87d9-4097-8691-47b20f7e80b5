<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Music;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\Music\MusicStoreRequest;
use App\Application\Http\Requests\Admin\Music\MusicUpdateRequest;
use App\Domain\Music\MusicListRepository;
use App\Domain\RawMedia\RawMediaService;
use App\Models\Music\Music;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

final class MusicController extends BaseController
{
    private RawMediaService $rawMediaService;
    private MusicListRepository $musicListRepository;
    private Factory $viewFactory;

    public function __construct(
        RawMediaService $rawMediaService,
        MusicListRepository $musicListRepository,
        Factory $viewFactory
    ) {
        $this->authorizeResource(Music::class, Music::class);
        $this->rawMediaService = $rawMediaService;
        $this->musicListRepository = $musicListRepository;
        $this->viewFactory = $viewFactory;
    }

    /**
     * @throws ModelNotFoundException
     */
    public function create(Request $request): View
    {
        $musicList = $this->musicListRepository->findOneById((int) $request->get('music_list_id'));
        if ($musicList === null) {
            throw new ModelNotFoundException();
        }

        return $this->viewFactory->make(
            'admin.music-lists.musics.create',
            [
                'musicList' => $musicList,
            ]
        );
    }

    public function destroy(Music $music): RedirectResponse
    {
        $this->musicListRepository->deleteMusic($music->musicList, $music->id);

        return redirect()->back();
    }

    public function edit(Music $music): View
    {
        return $this->viewFactory->make(
            'admin.music-lists.musics.edit',
            [
                'music' => $music,
            ]
        );
    }

    public function store(MusicStoreRequest $request): JsonResponse
    {
        $musicList = $this->musicListRepository->findOneById((int) $request->get('music_list_id'));
        $title = $request->get('title');
        $rawMedia = $this->rawMediaService->findOrCreateMusic(
            $request->get('file_url'),
            $title
        );

        Music::query()->create([
            'title' => $title,
            'order' => $musicList->musics()->count(),
            'music_list_id' => $musicList->id,
            'raw_media_id' => $rawMedia->id,
        ]);

        return new JsonResponse(['success' => true], Response::HTTP_CREATED);
    }

    public function update(Music $music, MusicUpdateRequest $request): JsonResponse
    {
        if ($request->filled('file_url')) {
            $rawMedia = $this->rawMediaService->findOrCreateMusic(
                $request->get('file_url'),
                $request->get('title')
            );
            $music->update([
                'raw_media_id' => $rawMedia->id,
            ]);
        } else {
            $music->rawMedia->update([
                'name' => $request->input('title'),
            ]);
        }

        return new JsonResponse(['success' => true], JsonResponse::HTTP_OK);
    }
}
