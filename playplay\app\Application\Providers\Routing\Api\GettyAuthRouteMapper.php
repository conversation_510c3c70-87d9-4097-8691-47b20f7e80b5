<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\V2\Stocks\GettyAuthController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class GettyAuthRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/getty',
            'as' => 'getty.',
        ], static function (Router $router) {
            $router->get('/authorization-url', [GettyAuthController::class, 'authorizationUrl'])
                ->name('auth.authorization-url');
            $router->get('/auth-callback', [GettyAuthController::class, 'authorizationCallback'])
                ->name('auth.authorization-callback');
        });
    }
}
