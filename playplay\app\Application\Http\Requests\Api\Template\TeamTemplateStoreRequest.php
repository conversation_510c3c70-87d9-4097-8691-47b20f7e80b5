<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\Template;

use Illuminate\Foundation\Http\FormRequest;

final class TeamTemplateStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'project_id' => ['required', 'exists:projects,id'],
            'template_name' => ['required', 'string', 'min:1', 'max:120'],
            'teams' => ['required', 'array'],
            'teams.*' => ['exists:teams,id'],
        ];
    }
}
