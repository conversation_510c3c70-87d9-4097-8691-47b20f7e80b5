<?php

namespace App\Application\Events;

use App\Models\Project;
use Illuminate\Queue\SerializesModels;

class ProjectUpdated
{
    use SerializesModels;

    private Project $project;
    private bool $shouldRerenderProject;

    public function __construct(Project $project, bool $shouldRerenderProject = true)
    {
        $this->project = $project;
        $this->shouldRerenderProject = $shouldRerenderProject;
    }

    public function getProject(): Project
    {
        return $this->project;
    }

    public function shouldRerenderProject(): bool
    {
        return $this->shouldRerenderProject;
    }
}
