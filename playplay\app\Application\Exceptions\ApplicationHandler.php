<?php

namespace App\Application\Exceptions;

use App\Models\User;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Illuminate\Config\Repository as ConfigRepository;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpExceptionInterface;
use Throwable;

class ApplicationHandler extends ExceptionHandler
{
    private bool $doesRequestAcceptsJson;

    public function render($request, Throwable $e)
    {
        if ($request->ajax()) {
            return AjaxExceptionViewModel::fromException($e)->getResponse();
        }

        $this->doesRequestAcceptsJson = $request->wantsJson();

        return parent::render($request, $e);
    }

    public function report(Throwable $e): void
    {
        if ($this->isExceptionHandled($e)) {
            $sentryService = app('sentry');
            $configRepository = app(ConfigRepository::class);

            /** @var User|null $user */
            $user = auth()->user();

            $sentryService->configureScope((new ScopeConfigurationFactory($configRepository))->create($e, $user));
            $sentryService->captureException($e);
        }

        parent::report($e);
    }

    protected function convertExceptionToResponse(Throwable $e)
    {
        if (!$this->doesRequestAcceptsJson) {
            return parent::convertExceptionToResponse($e);
        }

        $data = [
            'code' => Response::HTTP_INTERNAL_SERVER_ERROR,
            'error' => 'Internal Server Error',
        ];

        if (config('app.debug')) {
            $data['error'] = $e->getMessage();
            $data['int_code'] = $e->getCode();
            $data['file'] = $e->getFile();
            $data['line'] = $e->getLine();
            $data['trace'] = $e->getTrace();
        }

        return new JsonResponse($data, $data['code']);
    }

    protected function invalidJson($request, ValidationException $exception): JsonResponse
    {
        return response()->json($exception->errors(), $exception->status);
    }

    protected function renderHttpException(HttpExceptionInterface $e)
    {
        if (!$this->doesRequestAcceptsJson) {
            return parent::renderHttpException($e);
        }

        return $this->convertExceptionToResponse($e);
    }

    protected function unauthenticated($request, AuthenticationException $exception)
    {
        $referral = urlencode($request->url());

        return $request->expectsJson()
            ? response()->json(['message' => 'Unauthenticated.'], 401)
            : redirect()->guest(url("app/login?referral=$referral"));
    }

    private function isExceptionHandled(Throwable $e): bool
    {
        return app()->bound('sentry')
            && !app()->environment('local')
            && !$e instanceof AuthenticationException;
    }
}
