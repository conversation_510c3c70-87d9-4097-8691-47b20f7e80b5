<?php

declare(strict_types=1);

namespace App\Application\Mail;

use App\Models\Renders\RenderProjectHtml;
use App\Models\Renders\RenderStory;
use App\Models\User;
use App\Models\VideoDownload;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

final class SendRenderLink extends Mailable
{
    use Queueable, SerializesModels;

    private string $url;
    private string $fileName;
    private string $countChunks = '';
    private User $user;

    public function __construct(RenderProjectHtml|RenderStory $render, User $user, int $nth)
    {
        $this->user = $user;
        $this->url = route(
            'api.v2.projects.download',
            [$render->project->id, VideoDownload::TYPE_EMAIL, 'nth' => $nth]
        );
        $this->fileName = $render->getDownloadFilename();
        if ($render instanceof RenderStory) {
            $nbChunks = count($render->chunk_urls);
            $nth++;
            $this->countChunks = " - {$nth}/{$nbChunks}";
        }
    }

    public function build(): self
    {
        return $this->from('<EMAIL>')
            ->subject(__('email.send_video.subject', ['countChunks' => $this->countChunks]))
            ->markdown('emails.render-link', [
                'name' => $this->user->first_name,
                'url' => $this->url,
                'fileName' => $this->fileName,
            ]);
    }
}
