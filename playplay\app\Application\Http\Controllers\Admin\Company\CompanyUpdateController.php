<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Company;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\Company\CompanyUnlimitedUsersUpdateRequest;
use App\Application\Http\Requests\Admin\Company\CompanyUpdateRequest;
use App\Domain\Company\CompanyFeatureService;
use App\Domain\Company\CompanyService;
use App\Domain\Feature\FeatureGroupViewModelService;
use App\Domain\Plan\PlanRepository;
use App\Models\Company;
use App\Models\Feature;
use App\Models\User;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Redirector;

final class CompanyUpdateController extends BaseController
{
    private PlanRepository $planRepository;
    private CompanyService $companyService;
    private CompanyFeatureService $companyFeatureService;
    private FeatureGroupViewModelService $featuresGroupViewModelService;
    private Redirector $redirectService;
    private Factory $viewFactory;

    public function __construct(
        PlanRepository $planRepository,
        CompanyService $companyService,
        CompanyFeatureService $companyFeatureService,
        FeatureGroupViewModelService $featuresGroupViewModelService,
        Redirector $redirectService,
        Factory $viewFactory,
    ) {
        $this->authorizeResource(Company::class);
        $this->planRepository = $planRepository;
        $this->companyService = $companyService;
        $this->companyFeatureService = $companyFeatureService;
        $this->featuresGroupViewModelService = $featuresGroupViewModelService;
        $this->redirectService = $redirectService;
        $this->viewFactory = $viewFactory;
    }

    public function show(Company $company, Request $request): View
    {
        $this->authorize('canAccessRestrictedData', $company);
        $listOfMonth = [];
        for ($i = 0; $i <= 12; $i++) {
            $month = date("Y-m", strtotime(date('Y-m-01') . " -$i months"));
            $listOfMonth[$month] = $month;
        }

        // Retrieve admin users that can be associated with company
        $adminUsers = User::retrieveAdminUsers();

        $features = Feature::for('company')->get();
        $currentSubscription = $company->getCurrentSubscription();
        $plans = $this->planRepository->getAll()->reverse();

        $companyFeatureGroups = $this->featuresGroupViewModelService->getFeatureGroupViewModel($company);

        return $this->viewFactory->make(
            'admin.companies.show',
            [
                'company' => $company,
                'teams' => self::getPaginatedTeams($company, $request),
                'listOfMonth' => $listOfMonth,
                'adminUsers' => $adminUsers,
                'features' => $features,
                'currentSubscription' => $currentSubscription,
                'plans' => $plans,
                'companyFeatureGroups' => $companyFeatureGroups,
            ]
        );
    }

    public function update(CompanyUpdateRequest $request, Company $company): RedirectResponse
    {
        $this->authorize('canAccessRestrictedData', $company);
        $name = $request->get('name', '');
        $type = $request->get('type', '') !== '' ? $request->get('type') : null;
        $csmId = $request->get('csm_id', '') !== '' ? (int) $request->get('csm_id') : null;
        $planId = (int) $request->get('plan_id');
        $dataIsRestricted = $request->get('data_is_restricted', '') !== ''
            ? (bool) $request->get('data_is_restricted')
            : false;
        $clientUntil = $request->get('client_until', '') !== '' ? $request->get('client_until') : null;
        $clientFrom = $request->get('client_from', '') !== '' ? $request->get('client_from') : null;
        $status = $request->get('status', '') !== '' ? $request->get('status') : Company::STATUS_ACTIVE;

        $dataIsRestrictedOriginalValue = $company->data_is_restricted;
        $planIdOriginalValue = $company->plan_id;

        $this->companyService->updateCompany(
            $company,
            $name,
            $csmId,
            $clientFrom,
            $clientUntil,
            $type,
            $planId,
            $dataIsRestricted,
            $status,
        );

        $this->companyFeatureService->updateCompanyFeatures(
            $company,
            $request->get('feature_items', []),
            $type
        );

        $company->touch();

        return $this->redirectService->back()->with(
            'data_is_restricted_updated',
            $this->shouldDisplayDataIsRestrictedAlert($company, $dataIsRestrictedOriginalValue, $planIdOriginalValue)
        );
    }

    private static function getPaginatedTeams(Company $company, Request $request): LengthAwarePaginator
    {
        return $company
            ->teams()
            ->with('users', 'categories')
            ->paginate($request->input('per_page', 400))
            ->appends($request->all());
    }

    private function shouldDisplayDataIsRestrictedAlert(
        Company $company,
        ?bool $dataIsRestrictedOriginalValue,
        ?int $planIdOriginalValue
    ): bool {
        if ($dataIsRestrictedOriginalValue !== $company->data_is_restricted) {
            return true;
        }

        if ($planIdOriginalValue !== $company->plan_id) {
            return true;
        }

        return false;
    }

    public function updateUnlimitedUsers(
        CompanyUnlimitedUsersUpdateRequest $request,
        Company $company
    ): RedirectResponse {
        $this->authorize('canAccessRestrictedData', $company);
        $this->authorize('update', $company);

        $updatedCompanyData = $request->only(['unlimited_users']);
        $updatedCompanyData['unlimited_users'] = $request->boolean('unlimited_users');
        $company->update($updatedCompanyData);
        $company->touch();

        return $this->redirectService->back();
    }
}
