<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\Screen\Version;

use Illuminate\Foundation\Http\FormRequest;

final class ScreenVersionBulkUpdateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'screen_ids' => ['required', 'array'],
            'screen_ids.*' => ['required', 'integer', 'exists:screens,id'],
        ];
    }

    public function authorize(): bool
    {
        return true;
    }
}
