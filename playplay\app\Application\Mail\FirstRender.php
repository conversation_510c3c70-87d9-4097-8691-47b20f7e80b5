<?php

namespace App\Application\Mail;

use App\Infrastructure\User\FirstRenderUser;
use Illuminate\Mail\Mailable;

final class FirstRender extends Mailable
{
    private FirstRenderUser $firstRenderUser;

    public function __construct(FirstRenderUser $firstRenderUser)
    {
        $this->firstRenderUser = $firstRenderUser;
    }

    public function build(): FirstRender
    {
        return $this->from('<EMAIL>', __('email.from'))
            ->subject(__('email.first_render.subject'))
            ->markdown('emails.first-render', ['firstRenderUser' => $this->firstRenderUser]);
    }
}
