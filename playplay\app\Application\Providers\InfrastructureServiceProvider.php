<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Application\Console\Commands\GoogleCloudStorage\GoogleCloudStorageCleanerService;
use App\Application\Console\Commands\GoogleCloudStorage\StorageCleanerInterface;
use Illuminate\Support\ServiceProvider;
use Ramsey\Uuid\UuidFactory;
use Ramsey\Uuid\UuidFactoryInterface;

class InfrastructureServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(StorageCleanerInterface::class, GoogleCloudStorageCleanerService::class);
        $this->app->bind(UuidFactoryInterface::class, UuidFactory::class);
    }
}
