<?php

/** @noinspection SuspiciousLoopInspection */
declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Screen;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\Screen\Param\ScreenLayoutParamRequest;
use App\Domain\Screen\Parameters\OptionListCategoryRepository;
use App\Domain\Screen\Parameters\ScreenParameterFactory;
use App\Models\Screen;
use App\Models\ScreenLayout;
use App\Models\ScreenParams\BaseParam;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

final class ScreenLayoutParamController extends BaseController
{
    private ScreenParameterFactory $screenParametersFactory;

    private OptionListCategoryRepository $optionListCategoryRepository;
    private Factory $viewFactory;

    public function __construct(
        ScreenParameterFactory $screenParametersFactory,
        OptionListCategoryRepository $optionListCategoryRepository,
        Factory $viewFactory,
    ) {
        $this->authorizeResource(BaseParam::class, BaseParam::class);
        $this->screenParametersFactory = $screenParametersFactory;
        $this->optionListCategoryRepository = $optionListCategoryRepository;
        $this->viewFactory = $viewFactory;
    }

    public function create(Screen $screen, ScreenLayout $screenLayout, Request $request): View
    {
        $param = new BaseParam();
        $layoutSectionNumber = (int) $request->get('layout_section');

        return $this->viewFactory->make(
            'admin.screens.params.create',
            [
                'screen' => $screen,
                'screenLayout' => $screenLayout,
                'param' => $param,
                'layoutSectionNumber' => $layoutSectionNumber,
                'optionListCategories' => $this->optionListCategoryRepository
                    ->getAll()
                    ->pluck('backoffice_name', 'id')
                    ->toArray(),
            ]
        );
    }

    public function destroy(Screen $screen, ScreenLayout $screenLayout, BaseParam $param): RedirectResponse
    {
        $oldSection = $param->section;
        $param->delete();

        $orderedParams = $screenLayout->params->where('section', $oldSection)->sortBy('section_order');
        foreach ($orderedParams as $key => $param) {
            $param->update(['section_order' => $key]);
        }

        return redirect()->route('admin.screens.edit', ['screen' => $screen]);
    }

    public function edit(Screen $screen, ScreenLayout $screenLayout, BaseParam $param): View
    {
        return $this->viewFactory->make(
            'admin.screens.params.edit',
            [
                'screen' => $screen,
                'screenLayout' => $screenLayout,
                'param' => $param,
                'optionListCategories' => $this->optionListCategoryRepository
                    ->getAll()
                    ->pluck('backoffice_name', 'id')
                    ->toArray(),
            ]
        );
    }

    public function reorder(Screen $screen, ScreenLayout $screenLayout, Request $request): void
    {
        foreach ($request->get('param_ids', []) as $section => $params) {
            $order = 1;
            if ($params) {
                foreach ($params as $paramId) {
                    $param = BaseParam::find($paramId);
                    $param->update(
                        [
                            'section_order' => $order,
                            'section' => $section,
                            'layout_id' => $screenLayout->id,
                        ]
                    );
                    $order++;
                }
            }
        }
    }

    public function store(
        Screen $screen,
        ScreenLayout $screenLayout,
        ScreenLayoutParamRequest $request
    ): RedirectResponse {
        // Retrieve Common param value not depending of param type
        $commonParamValue = new Collection($request->get('param', []));
        $paramType = $commonParamValue->get('type');
        $layoutSection = (int) $commonParamValue->get('layout_section');

        // Create object Based on "type" field
        $param = $this->screenParametersFactory->createFromType($paramType);

        // Retrieve specific field for this type of object
        $specificConfigValue = new Collection($request->get($paramType, []));

        // Set specific field only available on store
        $param->type = $paramType;
        $param->class_model = get_class($param);
        $param->layout_id = $screenLayout->id;
        $param->section = $layoutSection;
        $param->section_order = $screenLayout->countParamsInSection($layoutSection) + 1;
        if ($param->type === BaseParam::TYPE_CUTAWAY_SHOT) {
            $param->parent_layout_param_id = $screenLayout->getFirstParamMedia()->id ?? null;
        }

        $this->upsert($param, $commonParamValue, $specificConfigValue);

        return redirect()->route('admin.screens.edit', ['screen' => $screen]);
    }

    // TODO create a dedicated Request
    public function update(
        Screen $screen,
        ScreenLayout $screenLayout,
        BaseParam $param,
        ScreenLayoutParamRequest $request
    ): RedirectResponse {
        // Retrieve Common param value not depending of param type
        $commonParamValue = new Collection($request->get('param', []));

        // Retrieve specific field for this type of object
        $specificConfigValue = new Collection($request->get($param->type, []));

        $this->upsert($param, $commonParamValue, $specificConfigValue);

        return redirect()->route('admin.screens.edit', ['screen' => $screen]);
    }

    /** @todo Move this code into a service or a repository */
    private function upsert(BaseParam $param, Collection $commonValue, Collection $specificValue): void
    {
        $optionListCategoryId = $specificValue->get('option_list_category_id');
        $param->option_list_category_id = $optionListCategoryId === '' ? null : $optionListCategoryId;

        // Common field for all param types
        $param->fill($commonValue->toArray());

        // Set config
        $param->config = $specificValue->toArray();

        $param->save();
        $param->touch();
    }
}
