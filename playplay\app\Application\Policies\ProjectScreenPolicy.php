<?php

namespace App\Application\Policies;

use App\Models\ProjectScreen;
use App\Models\User;

class ProjectScreenPolicy
{
    public function view(User $user, ProjectScreen $projectScreen): bool
    {
        return $user->can('view', $projectScreen->project);
    }

    public function import(User $user, ProjectScreen $projectScreen): bool
    {
        return $user->company->getValueOfFeature('import_export_subtitles') === 1
            && $this->view($user, $projectScreen);
    }
}
