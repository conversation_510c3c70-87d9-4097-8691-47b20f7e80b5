<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\Template;

use Illuminate\Foundation\Http\FormRequest;

final class TeamTemplateUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'template_name' => ['required', 'string', 'min:1', 'max:120'],
            'teams' => ['required', 'array'],
            'teams.*' => ['exists:teams,id'],
        ];
    }
}
