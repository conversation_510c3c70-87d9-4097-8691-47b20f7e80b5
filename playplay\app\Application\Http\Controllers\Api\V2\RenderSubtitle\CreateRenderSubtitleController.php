<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\RenderSubtitle;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Project\RenderSubtitleStoreRequest;
use App\Domain\Render\RenderSubtitle\RenderSubtitleSerializer;
use App\Domain\Render\RenderSubtitle\RenderSubtitleStatus;
use App\Models\ProjectScreenParam;
use App\Models\Renders\RenderSubtitle;
use App\Services\Processing\WorkflowManager;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;

final class CreateRenderSubtitleController extends BaseController
{
    use AuthorizesRequests;

    private WorkflowManager $workflowManager;
    private RenderSubtitleSerializer $serializer;

    public function __construct(WorkflowManager $workflowManager, RenderSubtitleSerializer $serializer)
    {
        $this->workflowManager = $workflowManager;
        $this->serializer = $serializer;
    }

    public function __invoke(RenderSubtitleStoreRequest $request): JsonResponse
    {
        $projectScreenParam = ProjectScreenParam::find($request->get('project_screen_param_id'));
        $this->authorize('create', [RenderSubtitle::class, $projectScreenParam]);

        $renderMedia = $projectScreenParam->processedMedia->lastRender;

        $renderSubtitle = RenderSubtitle::create([
            'status' => RenderSubtitleStatus::TO_PROCESS,
            'project_screen_param_id' => $projectScreenParam->id,
            'render_media_id' => $renderMedia->id,
            'target_language' => $request->input('target_language'),
            'to_queue_data' => ['lang' => $request->get('lang')],
        ]);

        $this->workflowManager->dispatchSubtitle($renderSubtitle);

        return $this->sendJsonResponse(
            new Collection([$this->serializer->serialize($renderSubtitle)]),
            JsonResponse::HTTP_CREATED
        );
    }
}
