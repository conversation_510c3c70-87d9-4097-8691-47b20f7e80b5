<?php

namespace App\Application\Http\Requests\Api\Billing;

use Illuminate\Foundation\Http\FormRequest;

class SubscriptionStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'stripe_token' => ['required', 'string'],
            'billing_plan_id' => ['required', 'exists:billing_plans,id'],
            'country' => ['required', 'string', 'country_iso3166_alpha2'],
        ];
    }
}
