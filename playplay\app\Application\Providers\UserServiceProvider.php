<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Request\RequestService;
use App\Domain\User\Action\CriticalUserAction\CriticalUserActionHandler;
use App\Domain\User\Action\CriticalUserAction\CriticalUserActionNotifier;
use App\Domain\User\Action\CriticalUserAction\Rules\BillingPlanEditCriticalActionRule;
use App\Domain\User\Action\CriticalUserAction\Rules\ConfigurationDestroyCriticalActionRule;
use App\Domain\User\Action\CriticalUserAction\Rules\MaintenanceListDestroyGenericCriticalActionRule;
use App\Domain\User\Action\CriticalUserAction\Rules\MusicDestroyCriticalActionRule;
use App\Domain\User\Action\CriticalUserAction\Rules\MusicListDestroyCriticalActionRule;
use App\Domain\User\Action\CriticalUserAction\Rules\OptionDestroyCriticalActionRule;
use App\Domain\User\Action\CriticalUserAction\Rules\OptionListDestroyCriticalActionRule;
use App\Domain\User\Action\CriticalUserAction\Rules\PlanEditCriticalActionRule;
use App\Domain\User\Action\CriticalUserAction\Rules\ScreenCategoryCriticalActionRule;
use App\Domain\User\Action\CriticalUserAction\Rules\ScreenDestroyCriticalActionRule;
use App\Domain\User\Action\CriticalUserAction\Rules\ScreenFamilyDestroyCriticalActionRule;
use App\Domain\User\Action\CriticalUserAction\Rules\TemplateCategoryDestroyCriticalActionRule;
use App\Domain\User\Action\CriticalUserAction\Rules\TemplateDestroyCriticalActionRule;
use App\Domain\User\Action\CriticalUserAction\Rules\TemplateSubCategoryDestroyCriticalActionRule;
use App\Domain\User\Action\UserActionLogDispatcher;
use App\Domain\User\Action\UserActionLogFactory;
use App\Domain\User\Action\UserActionService;
use App\Domain\User\UserRendersRepository;
use App\Domain\User\UserRepository;
use App\Infrastructure\User\Action\PubSubUserActionLogDispatcher;
use App\Infrastructure\User\Action\SlackCriticalActionNotifier;
use App\Infrastructure\User\Repositories\EloquentUserRendersRepository;
use App\Infrastructure\User\Repositories\EloquentUserRepository;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Support\ServiceProvider;

final class UserServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(UserRepository::class, EloquentUserRepository::class);
        $this->app->bind(UserRendersRepository::class, EloquentUserRendersRepository::class);
        $this->app->bind(UserActionLogDispatcher::class, PubSubUserActionLogDispatcher::class);
        $this->app->singleton(UserActionService::class, function ($app) {
            return new UserActionService(
                $app->make(UserActionLogDispatcher::class),
                $app->make(UserActionLogFactory::class),
                $app->make(Dispatcher::class),
                $app->make(CriticalUserActionHandler::class),
            );
        });

        $this->registerCriticalAction();
    }

    private function registerCriticalAction(): void
    {
        // Critical Actions
        $this->app->tag([
            BillingPlanEditCriticalActionRule::class,
            ConfigurationDestroyCriticalActionRule::class,
            MaintenanceListDestroyGenericCriticalActionRule::class,
            MusicListDestroyCriticalActionRule::class,
            MusicDestroyCriticalActionRule::class,
            OptionDestroyCriticalActionRule::class,
            OptionListDestroyCriticalActionRule::class,
            PlanEditCriticalActionRule::class,
            TemplateDestroyCriticalActionRule::class,
            TemplateCategoryDestroyCriticalActionRule::class,
            TemplateSubCategoryDestroyCriticalActionRule::class,
            ScreenFamilyDestroyCriticalActionRule::class,
            ScreenCategoryCriticalActionRule::class,
            ScreenDestroyCriticalActionRule::class,
        ], 'criticalActionRules');
        $this->app->bind(CriticalUserActionNotifier::class, SlackCriticalActionNotifier::class);
        $this->app->bind(CriticalUserActionHandler::class, function (Application $app) {
            return new CriticalUserActionHandler(
                $app->tagged('criticalActionRules'),
                $app->make(CriticalUserActionNotifier::class),
                $app->make(RequestService::class),
                $app->environment('production')
            );
        });
    }
}
