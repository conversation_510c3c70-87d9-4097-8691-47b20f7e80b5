<?php

declare(strict_types=1);

namespace App\Application\Mail;

use App\Models\TwoFactorAuthCode as TwoFactorAuthCodeModel;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;

final class TwoFactorAuthCode extends Mailable
{
    use Queueable;

    private User $user;
    private TwoFactorAuthCodeModel $code;

    public function __construct(User $user, TwoFactorAuthCodeModel $code)
    {
        $this->user = $user;
        $this->code = $code;
    }

    public function build(): self
    {
        return $this->from('<EMAIL>', __('email.from'))
            ->subject(__('email.two_factor_auth.subject'))
            ->markdown('emails.two-factor-auth-code', [
                'name' => $this->user->first_name,
                'code' => $this->code->code,
            ]);
    }
}
