<?php

namespace App\Application\Http\Requests\Admin\BillingPlan;

use Illuminate\Foundation\Http\FormRequest;

class BillingPlanStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'ref' => ['required', 'string', 'unique:billing_plans,ref'],
            'stripe_plan_id' => ['required', 'string', 'unique:billing_plans,stripe_plan_id'],
            'available' => ['required', 'boolean'],
        ];
    }
}
