<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\Config;

use App\Models\Config;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Routing\Route;

class ConfigUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        /** @var Route $route */
        $route = $this->route();
        /** @var Config $config */
        $config = $route->parameter('config');

        return [
            'key' => 'required|unique:configs,key,' . $config->id,
            'value' => 'required',
        ];
    }
}
