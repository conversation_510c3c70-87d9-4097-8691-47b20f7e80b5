<?php

declare(strict_types=1);

namespace App\Domain\Notification;

use App\Application\Events\BroadcastNotification;
use Illuminate\Contracts\Events\Dispatcher;

class NotificationService
{
    private NotificationRepository $notificationRepository;
    private Dispatcher $eventDispatcher;

    public function __construct(NotificationRepository $notificationRepository, Dispatcher $eventDispatcher)
    {
        $this->notificationRepository = $notificationRepository;
        $this->eventDispatcher = $eventDispatcher;
    }

    public function createAndSend(NotificationToBeSent $notificationToBeSent): void
    {
        $notification = $this->notificationRepository->createFrom($notificationToBeSent);

        $this->eventDispatcher->dispatch(new BroadcastNotification($notification));
    }
}
