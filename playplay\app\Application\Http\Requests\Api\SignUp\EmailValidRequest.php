<?php

namespace App\Application\Http\Requests\Api\SignUp;

use Illuminate\Foundation\Http\FormRequest;

class EmailValidRequest extends FormRequest
{
    public static function getRules()
    {
        return [
            'email' => [
                'bail',
                'required',
                'email',
                'uniqueUserActive',
                'uniqueFreeTrialActive',
            ],
        ];
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return static::getRules();
    }
}
