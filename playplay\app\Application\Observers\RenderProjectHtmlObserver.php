<?php

namespace App\Application\Observers;

use App\Domain\Render\RenderScreen\RenderScreenStatus;
use App\Models\Renders\ARender;
use App\Models\Renders\RenderProjectHtml;

class RenderProjectHtmlObserver
{
    public function updated(RenderProjectHtml $renderProjectHtml): void
    {
        if ($renderProjectHtml->isDirty('status') && $renderProjectHtml->isAborted()) {
            $renderProjectHtml->renderScreens()
                ->whereNotIn('status', RenderScreenStatus::FINISHED)
                ->update(['status' => ARender::STATUS_ABORTED]);
            $renderProjectHtml->project()->update(['render_up_to_date' => false]);
        }
    }
}
