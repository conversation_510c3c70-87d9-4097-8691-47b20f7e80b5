<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\User;

use App\Application\Http\Controllers\Api\V2\User\UserNotificationController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class UserNotificationRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::put('users/{user}/notifications', [UserNotificationController::class, 'updateMany'])
            ->name('users.notifications.update-many');
    }
}
