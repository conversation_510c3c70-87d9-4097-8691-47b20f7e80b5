<?php

namespace App\Domain\Logs\ModelHistory;

use Illuminate\Database\Eloquent\Model;

final class ModelHistorizables
{
    /** @var ModelHistorizable[] */
    private iterable $historizables;

    public function __construct(iterable $historizables)
    {
        $this->historizables = $historizables;
    }

    public function isHistorizableRelation(Model $model, string $relationName): bool
    {
        foreach ($this->historizables as $historizable) {
            if ($historizable->canHistorize($model)) {
                return $historizable->isHistorizableRelation($relationName);
            }
        }

        return false;
    }

    public function historize(Model $model): array
    {
        foreach ($this->historizables as $historizable) {
            if ($historizable->canHistorize($model)) {
                return $historizable->getHistorySnapshot($model);
            }
        }

        throw new UnableToHistorizeModelException($model::class);
    }
}
