<?php

declare(strict_types=1);

namespace App\Application\Mail\Getty;

use Illuminate\Mail\Mailable;

final class InvalidAssetUsages extends Mailable
{
    private array $invalidAssets;

    public function __construct(array $invalidAssets)
    {
        $this->invalidAssets = $invalidAssets;
    }

    public function build(): self
    {
        return $this->from('<EMAIL>')
            ->subject('invalid assets')
            ->markdown('emails.publish-getty-asset-usage', ['invalidAssets' => $this->invalidAssets]);
    }
}
