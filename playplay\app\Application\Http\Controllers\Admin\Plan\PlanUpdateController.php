<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Plan;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\Plan\PlanUpdateRequest;
use App\Domain\Feature\FeatureGroupViewModelService;
use App\Domain\Plan\PlanRepository;
use App\Models\Plan;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;

final class PlanUpdateController extends BaseController
{
    private FeatureGroupViewModelService $featuresGroupViewModelService;
    private PlanRepository $planRepository;
    private Factory $viewFactory;

    public function __construct(
        PlanRepository $planRepository,
        FeatureGroupViewModelService $featuresGroupViewModelService,
        Factory $viewFactory,
    ) {
        $this->authorizeResource(Plan::class);
        $this->planRepository = $planRepository;
        $this->featuresGroupViewModelService = $featuresGroupViewModelService;
        $this->viewFactory = $viewFactory;
    }

    public function edit(Plan $plan): View
    {
        return $this->viewFactory->make('admin.plans.edit', [
            'plan' => $plan,
            'planFeatureGroups' => $this->featuresGroupViewModelService->getFeatureGroupViewModel($plan),
        ]);
    }

    public function show(Plan $plan): View
    {
        return $this->viewFactory->make('admin.plans.show', [
            'plan' => $plan,
            'planFeatureGroups' => $this->featuresGroupViewModelService->getFeatureGroupViewModel($plan),
        ]);
    }

    public function update(PlanUpdateRequest $request, Plan $plan): RedirectResponse
    {
        $plan->displayed_name = $request->get('displayed_name');
        $plan->save();

        $this->planRepository->updatePlanFeatures($plan, $request->get('feature_items', []));

        return redirect()->back();
    }
}
