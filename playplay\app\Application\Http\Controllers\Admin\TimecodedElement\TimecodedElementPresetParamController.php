<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\TimecodedElement;

use App\Application\Http\Requests\Admin\TimecodedElement\TimecodedElementPresetParamStoreRequest;
use App\Application\Http\Requests\Admin\TimecodedElement\TimecodedElementPresetParamUpdateRequest;
use App\Domain\TimecodedElement\Repositories\TimecodedElementPresetParamRepository;
use App\Domain\TimecodedElement\TimecodedElementPresetParamService;
use App\Models\TimecodedElement\TimecodedElementPreset;
use App\Models\TimecodedElement\TimecodedElementPresetParam;
use App\Models\TimecodedElement\TimecodedElementsFamily;
use Illuminate\Contracts\View\Factory as ViewFactory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Redirector;

final class TimecodedElementPresetParamController
{
    private Redirector $redirector;
    private TimecodedElementPresetParamService $timecodedElementPresetParamService;
    private ViewFactory $viewFactory;
    private TimecodedElementPresetParamRepository $timecodedElementPresetParamRepository;

    public function __construct(
        Redirector $redirector,
        TimecodedElementPresetParamRepository $timecodedElementPresetParamRepository,
        TimecodedElementPresetParamService $timecodedElementPresetParamService,
        ViewFactory $viewFactory,
    ) {
        $this->redirector = $redirector;
        $this->timecodedElementPresetParamRepository = $timecodedElementPresetParamRepository;
        $this->timecodedElementPresetParamService = $timecodedElementPresetParamService;
        $this->viewFactory = $viewFactory;
    }

    public function create(
        TimecodedElementsFamily $timecodedElementsFamily,
        TimecodedElementPreset $timecodedElementPreset
    ): View {
        $timecodedElementPresetParamTypes = TimecodedElementPresetParam::ALL_TYPES;

        return $this->viewFactory->make(
            'admin.timecoded-elements-family.timecoded-element-presets.timecoded-element-preset-params.create',
            [
                'timecodedElementsFamily' => $timecodedElementsFamily,
                'timecodedElementPreset' => $timecodedElementPreset,
                'timecodedElementPresetParam' => new TimecodedElementPresetParam(),
                'timecodedElementPresetParamTypes' => $timecodedElementPresetParamTypes,
            ]
        );
    }

    public function store(
        TimecodedElementsFamily $timecodedElementsFamily,
        TimecodedElementPreset $timecodedElementPreset,
        TimecodedElementPresetParamStoreRequest $request,
    ): RedirectResponse {
        $this->timecodedElementPresetParamService->create(
            $timecodedElementPreset->id,
            $request->get('type'),
            $request->get('name'),
            $request->get('backoffice_name'),
            $request->get('animaniac_ref'),
            $request->get('default_value'),
        );

        return $this->redirector->route(
            'admin.timecoded-elements-family.timecoded-element-presets.edit',
            [
                'timecodedElementsFamily' => $timecodedElementsFamily,
                'timecodedElementPreset' => $timecodedElementPreset,
            ]
        );
    }

    public function edit(
        TimecodedElementsFamily $timecodedElementsFamily,
        TimecodedElementPreset $timecodedElementPreset,
        TimecodedElementPresetParam $timecodedElementPresetParam
    ): View {
        return $this->viewFactory->make(
            'admin.timecoded-elements-family.timecoded-element-presets.timecoded-element-preset-params.edit',
            [
                'timecodedElementsFamily' => $timecodedElementsFamily,
                'timecodedElementPreset' => $timecodedElementPreset,
                'timecodedElementPresetParam' => $timecodedElementPresetParam,
            ]
        );
    }

    public function update(
        TimecodedElementsFamily $timecodedElementsFamily,
        TimecodedElementPreset $timecodedElementPreset,
        TimecodedElementPresetParam $timecodedElementPresetParam,
        TimecodedElementPresetParamUpdateRequest $request,
    ): RedirectResponse {
        $this->timecodedElementPresetParamRepository->update(
            $timecodedElementPresetParam,
            $request->get('name'),
            $request->get('backoffice_name'),
            $request->get('animaniac_ref'),
            $request->get('default_value'),
        );

        return $this->redirector->route(
            'admin.timecoded-elements-family.timecoded-element-presets.edit',
            [
                'timecodedElementsFamily' => $timecodedElementsFamily,
                'timecodedElementPreset' => $timecodedElementPreset,
            ]
        );
    }

    public function destroy(
        TimecodedElementsFamily $timecodedElementsFamily,
        TimecodedElementPreset $timecodedElementPreset,
        TimecodedElementPresetParam $timecodedElementPresetParam
    ): RedirectResponse {
        $timecodedElementPresetParam->delete();

        return $this->redirector->route('admin.timecoded-elements-family.timecoded-element-presets.edit', [
            'timecodedElementsFamily' => $timecodedElementsFamily,
            'timecodedElementPreset' => $timecodedElementPreset
        ]);
    }
}
