<?php

namespace App\Application\Http\Middleware;

use Illuminate\Config\Repository as ConfigRepository;
use Illuminate\Http\Middleware\TrustProxies as Middleware;
use Symfony\Component\HttpFoundation\Request;

class TrustProxies extends Middleware
{
    public function __construct(ConfigRepository $configRepository)
    {
        $this->proxies = $configRepository->get('infrastructure.authorized_proxies', []);
    }

    /**
     * The headers that should be used to detect proxies.
     *
     * @var int
     */
    protected $headers = Request::HEADER_X_FORWARDED_FOR | Request::HEADER_X_FORWARDED_HOST |
    Request::HEADER_X_FORWARDED_PORT | Request::HEADER_X_FORWARDED_PROTO;
}
