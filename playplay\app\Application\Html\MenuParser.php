<?php

namespace App\Application\Html;

use Illuminate\Auth\Access\Gate;
use Illuminate\Support\Collection;
use Spatie\Menu\Laravel\Html;
use Spatie\Menu\Laravel\Link;
use Spatie\Menu\Laravel\Menu;

class MenuParser
{
    private Gate $gate;

    public function __construct(Gate $gate)
    {
        $this->gate = $gate;
    }

    public function convertArrayToMenu(array $menuConfig, string $class = ''): ?Menu
    {
        $menu = $this->initializeMenu($class);

        $this->parseMenuItems($menu, $menuConfig);

        return $menu->count() > 1 ? $menu : null;
    }

    private function initializeMenu(string $class): Menu
    {
        $menu = Menu::new()
            ->addClass($class)
            ->setActiveFromRequest('admin/');
        $menu->add(Html::raw('Administration')->addParentClass('header'));

        return $menu;
    }

    private function parseMenuItems(Menu $menu, array $menuConfig): ?Menu
    {
        $menuConfig = (new Collection($menuConfig))->filter(function (array $item) {
            return !isset($item[2]) || $this->gate->allows($item[2]);
        });

        if ($menuConfig->isEmpty()) {
            return null;
        }

        foreach ($menuConfig as $item) {
            $menu->add($this->parseMenuItem($item));
            if (isset($item[3])) {
                $subMenu = $this->parseMenuItems(Menu::new()->addClass('nav treeview-menu'), $item[3]);
                $menu->submenu($subMenu);
            }
        }

        return $menu;
    }

    private function parseMenuItem(array $item): Link
    {
        [$route, $label] = $item;

        if (is_array($label)) {
            [$label, $fontAwesomeClass] = $label;
            $label = '<i class="fa fa-' . $fontAwesomeClass . '"></i> <span>' . $label . '</span>';
        }

        if (is_array($route)) {
            [$route, $parameters] = $route;
            $parameters = is_callable($parameters) ? $parameters() : $parameters;
        }

        return Link::toRoute($route, $label, $parameters ?? null);
    }
}
