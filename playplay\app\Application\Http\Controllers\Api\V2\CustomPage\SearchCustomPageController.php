<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\CustomPage;

use App\Application\Http\Controllers\Api\BaseController;
use App\Domain\Common\Exceptions\EntityNotFoundException;
use App\Domain\CustomPage\CustomPageRepository;
use App\Infrastructure\CustomPage\Serializers\CustomPageLightSerializer;
use App\Models\CustomPage;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class SearchCustomPageController extends BaseController
{
    use AuthorizesRequests;

    private CustomPageRepository $customPageRepository;
    private CustomPageLightSerializer $customPageLightSerializer;

    public function __construct(
        CustomPageRepository $customPageRepository,
        CustomPageLightSerializer $customPageLightSerializer
    ) {
        $this->customPageRepository = $customPageRepository;
        $this->customPageLightSerializer = $customPageLightSerializer;
    }

    /**
     * @throws AuthorizationException
     */
    public function __invoke(Request $request): JsonResponse
    {
        try {
            $customPage = $this->customPageRepository->getByProjectId(
                (int) $request->query('projectId', '0')
            );
        } catch (EntityNotFoundException) {
            return $this->sendJsonResponse(new Collection([]), Response::HTTP_OK);
        }

        $this->authorize('canAccessRestrictedData', $customPage->project->company);
        $this->authorize('view', [CustomPage::class, $customPage]);

        return $this->sendJsonResponse(
            new Collection([$this->customPageLightSerializer->serialize($customPage)]),
            Response::HTTP_OK
        );
    }
}
