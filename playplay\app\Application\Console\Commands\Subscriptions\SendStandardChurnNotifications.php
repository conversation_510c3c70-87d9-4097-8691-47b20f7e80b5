<?php

namespace App\Application\Console\Commands\Subscriptions;

use App\Domain\Churn\ChurnException;
use App\Domain\Churn\ChurnNotificationService;
use App\Domain\Churn\NotificationException;
use App\Domain\Churn\PlanhatChurnService;
use App\Domain\Subscription\SubscriptionRepository;
use DateTimeImmutable;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Psr\Log\LoggerInterface;

class SendStandardChurnNotifications extends Command
{
    protected $signature = 'subscriptions:standard-churn-notifications
                            {--S|subscriptions= : A comma-separated list of subscription IDs = 1,2,3,...}
                            {--planhat-only : To only update Planhat data}
                            {--slack-only : To only send Slack notifications}';

    protected $description = 'Posts a slack message for every company that have their subscription finish today';

    private ChurnNotificationService $churnNotificationService;

    private PlanhatChurnService $planhatChurnService;

    private SubscriptionRepository $subscriptionRepository;

    private LoggerInterface $logger;

    public function __construct(
        ChurnNotificationService $churnNotificationService,
        PlanhatChurnService $planhatChurnService,
        SubscriptionRepository $subscriptionRepository,
        LoggerInterface $logger
    ) {
        parent::__construct();
        $this->churnNotificationService = $churnNotificationService;
        $this->planhatChurnService = $planhatChurnService;
        $this->subscriptionRepository = $subscriptionRepository;
        $this->logger = $logger;
    }

    public function handle(): int
    {
        $endedSubscriptions = $this->getSubscriptionsToBeNotifiedAbout();
        if ($endedSubscriptions->isEmpty()) {
            $this->logAndDisplayOnConsole('No companies churned today woohoo!', 'info');

            return 0;
        }

        $erroredSlackSubscriptionsCount = 0;
        $erroredPlanhatSubscriptionsCount = 0;
        if (!$this->option('planhat-only')) {
            $erroredSlackSubscriptionsCount = $this->sendSlackNotifications($endedSubscriptions);
        }

        if (!$this->option('slack-only')) {
            $erroredPlanhatSubscriptionsCount = $this->updatePlanhatData($endedSubscriptions);
        }

        if ($erroredSlackSubscriptionsCount !== 0 || $erroredPlanhatSubscriptionsCount !== 0) {
            throw new NotificationException(
                'Some Slack notifications or Planhat churn creations failed. Check the logs for more info.'
            );
        }

        return 0;
    }

    private function getSubscriptionsToBeNotifiedAbout(): Collection
    {
        if ($this->option('subscriptions') === null) {
            return $this->subscriptionRepository->getEndedStandardSubscriptionsOnDate(new DateTimeImmutable());
        }

        $endedSubscriptionsIds = explode(',', $this->option('subscriptions'));

        return $this->subscriptionRepository->getEndedStandardSubscriptionsByIds($endedSubscriptionsIds);
    }

    private function sendSlackNotifications(Collection $endedSubscriptions): int
    {
        $progressBar = $this->output->createProgressBar($endedSubscriptions->count());

        $erroredSubscriptionIds = [];
        foreach ($endedSubscriptions as $subscription) {
            try {
                $this->churnNotificationService->sendChurnNotification($subscription);
            } catch (NotificationException $exception) {
                $erroredSubscriptionIds[] = $subscription->id;
                $this->logAndDisplayOnConsole('Error when trying to send a notification: ' . $exception->getMessage());
            } finally {
                $progressBar->advance();
            }
        }

        $sentNotifications = $endedSubscriptions->count() - count($erroredSubscriptionIds);
        $this->logAndDisplayOnConsole(
            "{$sentNotifications}/{$endedSubscriptions->count()} churned companies logged.",
            'info'
        );

        if ($erroredSubscriptionIds !== []) {
            $formattedSubscriptionIds = implode(', ', $erroredSubscriptionIds);
            $this->logAndDisplayOnConsole(
                "Some Slack notifications couldn't be sent. Subscription IDs to retry: [{$formattedSubscriptionIds}]"
            );
        }

        $progressBar->finish();
        return count($erroredSubscriptionIds);
    }

    private function updatePlanhatData(Collection $endedSubscriptions): int
    {
        if (!App::environment('production')) {
            return 0;
        }

        $progressBar = $this->output->createProgressBar($endedSubscriptions->count());

        $erroredSubscriptionIds = [];
        foreach ($endedSubscriptions as $subscription) {
            try {
                $this->planhatChurnService->createPlanhatChurn($subscription);
            } catch (ChurnException $exception) {
                $erroredSubscriptionIds[] = $subscription->id;
                $this->logAndDisplayOnConsole('Error when trying to create a churn: ' . $exception->getMessage());
            } finally {
                $progressBar->advance();
            }
        }

        $createdPlanhatChurns = $endedSubscriptions->count() - count($erroredSubscriptionIds);
        $this->logAndDisplayOnConsole(
            "{$createdPlanhatChurns}/{$endedSubscriptions->count()} churns created on Planhat.",
            'info'
        );

        if ($erroredSubscriptionIds !== []) {
            $formattedSubscriptionIds = implode(', ', $erroredSubscriptionIds);
            $this->logAndDisplayOnConsole(
                "Some Planhat churns couldn't be created. Subscription IDs to retry: [{$formattedSubscriptionIds}]"
            );
        }

        $progressBar->finish();
        return count($erroredSubscriptionIds);
    }

    private function logAndDisplayOnConsole(string $message, string $style = 'error'): void
    {
        if ($style === 'info') {
            $this->info($message);
            $this->logger->info("[CHURN] {$message}");

            return;
        }

        $this->error($message);
        $this->logger->error("[CHURN] {$message}");
    }
}
