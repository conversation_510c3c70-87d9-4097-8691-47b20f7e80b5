<?php

declare(strict_types=1);

namespace App\Domain\Outro;

use App\Domain\ProcessedMedia\Repository\ProcessedMediaRepository;
use App\Models\ProcessedMedia;
use App\Models\RawMedia;
use App\Models\TeamPreset;
use InvalidArgumentException;

final class OutroFileService
{
    private const FORCED_OUTRO_KEY = 'forced';
    private const OPTIONAL_OUTRO_KEY = 'values';

    private OutroRawMediaFactory $outroRawMediaFactory;
    private OutroRenderMediaFactory $renderMediaFactory;
    private ProcessedMediaRepository $processedMediaRepository;

    public function __construct(
        OutroRawMediaFactory $outroRawMediaFactory,
        OutroRenderMediaFactory $renderMediaFactory,
        ProcessedMediaRepository $processedMediaRepository
    ) {
        $this->outroRawMediaFactory = $outroRawMediaFactory;
        $this->renderMediaFactory = $renderMediaFactory;
        $this->processedMediaRepository = $processedMediaRepository;
    }

    public function deleteForcedOutroInFormat(array $outros, string $forcedOutroFormatToDelete): array
    {
        if (!array_key_exists(self::FORCED_OUTRO_KEY, $outros)) {
            return $outros;
        }

        foreach ($outros[self::FORCED_OUTRO_KEY] as $format => $forcedOutro) {
            if ($format === $forcedOutroFormatToDelete) {
                unset($outros[self::FORCED_OUTRO_KEY][$format]);
            }
        }

        return $outros;
    }

    public function retrieveOutrosWithRawMedia(TeamPreset $teamPreset): array
    {
        $outros = $teamPreset->outros;
        if ($outros === null) {
            return [];
        }

        if (isset($outros[self::OPTIONAL_OUTRO_KEY])) {
            $outros[self::OPTIONAL_OUTRO_KEY] = $this
                ->retrieveOptionalOutrosWithRawMedia($outros[self::OPTIONAL_OUTRO_KEY]);
        }

        if (isset($outros[self::FORCED_OUTRO_KEY])) {
            $outros[self::FORCED_OUTRO_KEY] = $this
                ->retrieveForcedOutrosWithRawMedia($outros[self::FORCED_OUTRO_KEY]);
        }

        return $outros;
    }

    public function updateTeamPresetOutros(
        TeamPreset $teamPreset,
        array $finalOutros,
        array $optionalOutroFilePaths,
        array $forcedOutroFilePaths
    ): array {
        if ($this->hasOptionalOutros($finalOutros)) {
            $finalOutros[self::OPTIONAL_OUTRO_KEY] = $this->updateTeamPresetOptionalOutros(
                $teamPreset,
                $finalOutros[self::OPTIONAL_OUTRO_KEY],
                $optionalOutroFilePaths
            );
        }

        $teamPresetForcedOutros = $this->updateTeamPresetForcedOutros(
            $teamPreset,
            $forcedOutroFilePaths
        );

        if ($teamPresetForcedOutros !== []) {
            $finalOutros[self::FORCED_OUTRO_KEY] = $teamPresetForcedOutros;
        }

        return $finalOutros;
    }

    private function createAProcessedMediaIdIfNeeded(array $optionalOutro, RawMedia $rawMedia): array
    {
        $formats = $optionalOutro['formats'] ?? [];
        foreach ($formats as $format => $isActive) {
            if ($isActive) {
                $processedMedia = $this->renderMediaFactory->createProcessedMediasAndRenders($rawMedia, $format);
                $optionalOutro['processed_medias'][$format] = $processedMedia->id;
            }
        }

        return $optionalOutro;
    }

    private function createOutroFromFile(
        string $filePath,
        string $fileName,
        TeamPreset $teamPreset,
        array $optionalOutro
    ): array {
        $rawMedia = $this->outroRawMediaFactory->createRawMediaForOutroFile(
            $filePath,
            $fileName,
            $teamPreset->id
        );

        return $this->createAProcessedMediaIdIfNeeded($optionalOutro, $rawMedia);
    }

    private function enrichForcedOutrosWithTheirProcessedMedia(array $forcedOutros): array
    {
        $processedMediasWithRendersAndRawMedias = $this->processedMediaRepository
            ->getAllByIdsWithRendersAndRawMedia(array_values($forcedOutros));

        $forcedOutrosWithMedia = [];
        foreach ($forcedOutros as $format => $forcedOutroProcessedMediaId) {
            $forcedOutrosWithMedia[$format] = $processedMediasWithRendersAndRawMedias->find(
                $forcedOutroProcessedMediaId
            );
        }

        return $forcedOutrosWithMedia;
    }

    private function getFirstProcessedMediaIdOfOutro(array $outro): int
    {
        foreach ($outro as $processedMediaIds) {
            if (is_array($processedMediaIds)) {
                return (int) $processedMediaIds['project_media_id'];
            }
        }

        throw new InvalidArgumentException();
    }

    private function getOrCreateProcessedMediaIdForOutroInFormat(
        array $currentOutro,
        string $format,
        RawMedia $rawMedia
    ): int {
        $processedMediaId = $currentOutro[$format]['project_media_id'] ?? null;

        return (int) ($processedMediaId ?? $this->renderMediaFactory
                ->createProcessedMediasAndRenders($rawMedia, $format)
                ->id
        );
    }

    private function getProcessedMediaIdsFromOptionalOutros(array $optionalOutros): array
    {
        $processedMediaIds = [];
        foreach ($optionalOutros as $optionalOutro) {
            foreach ($optionalOutro['processed_medias'] as $processedMediaId) {
                $processedMediaIds[] = $processedMediaId;
            }
        }

        return $processedMediaIds;
    }

    private function hasOptionalOutros(array $finalOutros): bool
    {
        return array_key_exists(self::OPTIONAL_OUTRO_KEY, $finalOutros);
    }

    private function hasUploadedOutroFileForCreation(array $outroFilePaths, int $outroPosition): bool
    {
        $filePath = $outroFilePaths[$outroPosition] ?? null;

        return $filePath !== null;
    }

    private function retrieveForcedOutrosWithRawMedia(array $forcedOutros): array
    {
        $forcedOutros['processed_medias'] = $this->enrichForcedOutrosWithTheirProcessedMedia($forcedOutros);

        return $forcedOutros;
    }

    private function retrieveOptionalOutrosWithRawMedia(array $optionalOutros): array
    {
        $listOfMediaId = $this->getProcessedMediaIdsFromOptionalOutros($optionalOutros);
        $medias = $this->processedMediaRepository->getAllByIdsWithRendersAndRawMedia($listOfMediaId);

        foreach ($optionalOutros as &$optionalOutro) {
            $firstProcessedMedia = $optionalOutro['processed_medias'][0] ?? null;
            if ($firstProcessedMedia !== null) {
                $rawMedia = $medias->firstWhere('id', $firstProcessedMedia)->rawMedia;
                $optionalOutro['raw_media'] = $rawMedia;
            }

            foreach ($optionalOutro['processed_medias'] ?? [] as $format => $processedMediaId) {
                $optionalOutro['processed_medias'][$format] = $medias->firstWhere('id', $processedMediaId);
            }
        }

        return $optionalOutros;
    }

    private function updateFormatsOfOutro(array $currentOutro): array
    {
        $outroRawMedia = $this->updateRawMediaNameOfOutro($currentOutro)->rawMedia;
        foreach ($currentOutro['formats'] as $format => $isActive) {
            if (!$isActive) {
                unset($currentOutro['processed_medias'][$format]);

                continue;
            }

            $currentOutro['processed_medias'][$format] = $this->getOrCreateProcessedMediaIdForOutroInFormat(
                $currentOutro,
                $format,
                $outroRawMedia
            );
        }

        return $currentOutro;
    }

    private function updateRawMediaNameOfOutro(array $outro): ProcessedMedia
    {
        return $this->processedMediaRepository->updateRawMediaName(
            $this->getFirstProcessedMediaIdOfOutro($outro),
            $outro['name']
        );
    }

    private function updateTeamPresetForcedOutros(TeamPreset $teamPreset, array $forcedOutroFilePaths): array
    {
        $finalForcedOutros = $teamPreset->outros[self::FORCED_OUTRO_KEY] ?? [];

        foreach ($forcedOutroFilePaths as $format => $filePath) {
            if ($filePath !== '') {
                $rawMedia = $this->outroRawMediaFactory->createRawMediaForOutroFile(
                    $filePath,
                    'forced-outro',
                    $teamPreset->id
                );

                $finalForcedOutros[$format] = $this->renderMediaFactory
                    ->createProcessedMediasAndRenders($rawMedia, $format)->id;
            }
        }

        return $finalForcedOutros;
    }

    private function updateTeamPresetOptionalOutros(
        TeamPreset $teamPreset,
        array $finalOptionalOutros,
        array $optionalOutroFilePaths
    ): array {
        foreach ($finalOptionalOutros as $outroPosition => $outro) {
            if ($this->hasUploadedOutroFileForCreation($optionalOutroFilePaths, $outroPosition)) {
                $finalOptionalOutros[$outroPosition] = $this->createOutroFromFile(
                    $optionalOutroFilePaths[$outroPosition],
                    $outro['name'],
                    $teamPreset,
                    $outro
                );

                continue;
            }

            // update
            $outroFormats = $outro['formats'] ?? [];
            if ($outroFormats !== []) {
                $finalOptionalOutros[$outroPosition] = $this->updateFormatsOfOutro($outro);

                continue;
            }

            // remove
            // If there is no file neither project_media_id, delete it
            unset($finalOptionalOutros[$outroPosition]);
        }

        return $finalOptionalOutros;
    }
}
