<?php

namespace App\Application\Http\Controllers\Api\V2\Media;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\RawMedia\RawMediaDestroyRequest;
use App\Application\Http\Requests\Api\RawMedia\RawMediaIndexRequest;
use App\Application\Http\Requests\Api\RawMedia\RawMediaStoreFromStockRequest;
use App\Application\Http\Requests\Api\RawMedia\RawMediaStoreRequest;
use App\Application\Http\Requests\Api\RawMedia\RawMediaUploadRequest;
use App\Domain\Project\RawMedia\RelationType;
use App\Domain\RawMedia\HeavyMediaErrorRepository;
use App\Domain\RawMedia\RawMediaRepository;
use App\Domain\RawMedia\RawMediaSource;
use App\Domain\RawMedia\RawMediaType;
use App\Domain\Render\RenderMedia\RenderMediaStatus;
use App\Models\Project;
use App\Models\RawMedia;
use App\Models\Renders\RenderMedia;
use App\Models\User;
use App\Services\CloudStorageService;
use App\Services\Processing\RenderMediaService;
use App\Services\Stock\Getty\GettyImageStock;
use App\Services\Stock\Getty\GettyVideoStock;
use App\Services\Stock\Storyblocks\StoryblocksVideoStock;
use App\Services\Stock\Unsplash\UnsplashImageStock;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Testing\MimeType;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class RawMediaController extends BaseController
{
    use AuthorizesRequests;

    private HeavyMediaErrorRepository $heavyMediaErrorRepository;
    private RawMediaRepository $rawMediaRepository;
    private RenderMediaService $renderService;

    public function __construct(
        RenderMediaService $renderService,
        HeavyMediaErrorRepository $heavyMediaErrorRepository,
        RawMediaRepository $rawMediaRepository
    ) {
        $this->renderService = $renderService;
        $this->heavyMediaErrorRepository = $heavyMediaErrorRepository;
        $this->rawMediaRepository = $rawMediaRepository;
    }

    public function destroy(
        RawMedia $rawMedia,
        RawMediaDestroyRequest $request
    ): JsonResponse {
        /** @var ?Project $project */
        $project = Project::query()->find($request->get('project_id'));

        if ($project === null) {
            return $this->sendJsonResponse(new Collection([]), Response::HTTP_NO_CONTENT);
        }

        $this->authorize('destroy', [
            $rawMedia,
            $project,
        ]);

        // Todo: Move to repository
        $project->rawMedias()
            ->wherePivot(
                'relation_type',
                RelationType::fromKey(
                    $request->input('relation_type_key')
                )->value
            )
            ->detach($rawMedia->id);

        return $this->sendJsonResponse(new Collection(), Response::HTTP_NO_CONTENT);
    }

    public function index(RawMediaIndexRequest $request): JsonResponse
    {
        $rawMediaIds = $request->get('ids');
        $this->authorize('index', [RawMedia::class, $rawMediaIds]);

        return $this->sendJsonResponse(
            RawMedia::query()->whereIn('id', $rawMediaIds)->get(),
            Response::HTTP_OK
        );
    }

    public function show(RawMedia $rawMedia): JsonResponse
    {
        $this->authorize('view', $rawMedia);

        return $this->sendJsonResponse(new Collection([$rawMedia]), Response::HTTP_OK);
    }

    public function store(
        RawMediaStoreRequest $request,
        Guard $auth,
        CloudStorageService $gcs
    ): JsonResponse {
        /** @var ?Project $project */
        $project = $request->has('project_id')
            ? Project::query()->find($request->get('project_id'))
            : null;

        if ($project !== null) {
            $this->authorize('view', $project);
        }

        $url = $gcs->getUrlFromCdn($request->get('file_url'));

        $gcs->setAllReaderFromUrl($url);

        $rawMediaMd5Hash = $request->get('hash');
        /** @var ?RawMedia $rawMedia */
        $rawMedia = RawMedia::query()->where('md5', $rawMediaMd5Hash)->first();
        $rawMediaAlreadyExists = $rawMedia !== null;

        /** @var User $user */
        $user = $auth->user();
        $rawMedia = $this->createOrUpdateRawMediaAndRender(
            $rawMedia,
            [
                'source' => new RawMediaSource($request->get('source')),
                'name' => $request->get('name'),
                'content_type' => $request->get('content_type'),
                'md5' => $rawMediaMd5Hash,
                'url' => $url,
            ],
            $user,
            RelationType::fromKey($request->input('relation_type_key')),
            $project
        );

        return $this->sendJsonResponse(
            new Collection([$rawMedia]),
            $rawMediaAlreadyExists ? Response::HTTP_ALREADY_REPORTED : Response::HTTP_CREATED
        );
    }

    public function storeOrUpdateFromStock(
        RawMediaStoreFromStockRequest $request,
        Guard $auth,
        StoryblocksVideoStock $storyblocksStock,
        GettyImageStock $gettyImageStock,
        GettyVideoStock $gettyVideoStock,
        UnsplashImageStock $unsplashImageStock
    ): JsonResponse {
        /** @var Project $project */
        $project = Project::query()->find($request->get('project_id'));

        $this->authorize('view', $project);

        $source = $request->get('source');
        $type = $request->get('type');
        $stockId = $request->get('stock_id');
        $url = $request->get('file_url');
        $height = $request->get('height');
        $downloadSize = $request->get('download_size');
        $credit = $request->get('credit');

        $rawMediaMd5Hash = md5($source . $type . $stockId);
        /** @var ?RawMedia $rawMedia */
        $rawMedia = RawMedia::query()->where('md5', $rawMediaMd5Hash)->first();
        $rawMediaAlreadyExists = $rawMedia !== null;

        if ($rawMediaAlreadyExists === false) {
            if ($source === RawMediaSource::STORYBLOCKS && $this->isVideo($type)) {
                $url = $storyblocksStock->getDownload($stockId, $downloadSize);
            } elseif ($this->isImage($type) && $this->isFromGetty($source)) {
                $url = $gettyImageStock->getDownload($stockId, $height);
            } elseif ($this->isVideo($type) && $this->isFromGetty($source)) {
                $url = $gettyVideoStock->getDownload($stockId, $downloadSize);
            } elseif ($source === RawMediaSource::UNSPLASH && $this->isImage($type)) {
                $url = $unsplashImageStock->getDownload($stockId);
            }
        }

        $name = $rawMediaAlreadyExists ? $rawMedia->name : basename(parse_url($url, PHP_URL_PATH));
        $contentType = $rawMediaAlreadyExists ? $rawMedia->content_type : MimeType::from(parse_url($url, PHP_URL_PATH));

        /** @var User $user */
        $user = $auth->user();
        $rawMedia = $this->createOrUpdateRawMediaAndRender(
            $rawMedia,
            [
                'source' => new RawMediaSource($source),
                'md5' => $rawMediaMd5Hash,
                'name' => $name,
                'url' => $url,
                'type' => new RawMediaType($type),
                'content_type' => $contentType,
                'stock_id' => $stockId,
                'credit' => $credit,
            ],
            $user,
            RelationType::fromKey($request->input('relation_type_key')),
            $project
        );

        return $this->sendJsonResponse(
            new Collection([$rawMedia]),
            $rawMediaAlreadyExists === true ? Response::HTTP_ALREADY_REPORTED : Response::HTTP_CREATED
        );
    }

    public function uploadUrl(
        RawMediaUploadRequest $request,
        Guard $auth,
        CloudStorageService $gcs
    ): JsonResponse {
        if ($request->get('size') > RawMedia::UPLOAD_MAX_SIZE) {
            $this->heavyMediaErrorRepository->create($auth->id(), $request->get('size'), $request->get('extension'));
            $request->validate(['size' => 'required|integer|max:'.RawMedia::UPLOAD_MAX_SIZE]);
        }

        /** @var ?Project $project */
        $project = $request->has('project_id')
            ? Project::query()->find($request->get('project_id'))
            : null;

        if ($project !== null) {
            $this->authorize('view', $project);
        }

        /** @var ?RawMedia $rawMedia */
        $rawMedia = RawMedia::query()->where('md5', $request->get('hash'))->first();

        // If raw_media exists add relation between raw_media and project/user
        // If it's not a backoffice upload from option list, we need to return the raw_media
        // And return rawMedia object with code 208
        $isBoUpload = $request->boolean('bo_upload');
        if (!$isBoUpload && $rawMedia !== null) {
            // If raw media is errored retry identify
            if (!$rawMedia->hasValidLastRender()) {
                $renderMedia = $this->createRawMediaRender($rawMedia);
                $this->renderService->identify($renderMedia);
                $rawMedia->refresh();
            }

            $relationType = RelationType::fromKey($request->input('relation_type_key'));

            if ($project !== null) {
                $this->rawMediaRepository->linkRawMediaToProject($rawMedia, $project, $relationType);
            }

            /** @var User $user */
            $user = $auth->user();
            $this->rawMediaRepository->linkRawMediaToUser($rawMedia, $user);

            return $this->sendJsonResponse(
                new Collection([$rawMedia->refresh()]),
                Response::HTTP_ALREADY_REPORTED
            );
        }

        /** @var User $user */
        $user = $auth->user();
        $objectPath = $this->buildUploadPath(
            $user,
            $request->get('hash'),
            $request->get('extension'),
            $isBoUpload
        );
        $objectUrl = $gcs->getObjectUrl($objectPath);

        $gcsUploadUrl = $gcs->signedUploadUrl(
            $objectPath,
            $request->get('content_type'),
            base64_encode(hex2bin($request->get('hash')))
        );

        $cdnUploadUrl = $gcs->getCdnUrl($gcsUploadUrl);

        $responseData = ['upload_url' => $cdnUploadUrl];
        $responseData += $isBoUpload ? ['object_url' => $objectUrl] : [];

        return $this->sendJsonResponse(
            new Collection([$responseData]),
            Response::HTTP_OK
        );
    }

    public function setAllReaderFromUrl(Request $request, CloudStorageService $gcs): JsonResponse
    {
        $request->validate([
            'objectUrl' => 'required|url',
        ]);

        $objectUrl = $request->input('objectUrl');

        $gcs->setAllReaderFromUrl($objectUrl);

        return new JsonResponse(compact('objectUrl'), Response::HTTP_OK);
    }

    // TODO : refactor this to use RawMediaService::findOrCreateRawMedia instead
    private function createOrUpdateRawMediaAndRender(
        ?RawMedia $rawMedia,
        array $rawMediaData,
        User $user,
        RelationType $relationType,
        ?Project $project = null
    ): RawMedia {
        if ($rawMedia === null) {
            $rawMedia = $this->rawMediaRepository->create($rawMediaData);
        }

        // If raw media exists but stock id is not filled, updated it
        if (isset($rawMediaData['stock_id']) && !$rawMedia->stock_id) {
            $rawMedia->update(['stock_id' => $rawMediaData['stock_id']]);
        }

        // Create renders and launch identify rawMedia
        if (!$rawMedia->hasValidLastRender()) {
            /** @todo Use RenderService@createRenderMediaFor instead */
            $renderMedia = $this->createRawMediaRender($rawMedia);
            $this->renderService->identify($renderMedia);
        }

        /** @todo Move to domain service */
        if ($project !== null) {
            $this->rawMediaRepository->linkRawMediaToProject($rawMedia, $project, $relationType);
        }

        $this->rawMediaRepository->linkRawMediaToUser($rawMedia, $user);

        return $rawMedia;
    }

    private function createRawMediaRender(RawMedia $rawMedia): RenderMedia
    {
        return $rawMedia->renders()->create([
            'status' => RenderMediaStatus::toProcess(),
        ]);
    }

    private function buildUploadPath(
        ?User $user,
        string $hash,
        ?string $extension,
        bool $isBoUpload
    ): string {
        if ($isBoUpload) {
            return "uploads/{$user->id}/{$hash}.{$extension}";
        }

        if ($extension === null) {
            return "uploads/user-{$user->id}/{$hash}";
        }

        return "uploads/user-{$user->id}/{$hash}.{$extension}";
    }

    private function isVideo(string $type): bool
    {
        return $type === RawMediaType::VIDEO;
    }

    private function isImage(string $type): bool
    {
        return $type === RawMediaType::IMAGE;
    }

    private function isFromGetty(string $source): bool
    {
        return in_array($source, [RawMediaSource::GETTY, RawMediaSource::GETTY_EDITO], true);
    }
}
