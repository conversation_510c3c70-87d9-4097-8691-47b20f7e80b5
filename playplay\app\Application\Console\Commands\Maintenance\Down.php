<?php

namespace App\Application\Console\Commands\Maintenance;

use App\Domain\Maintenance\MaintenanceRepository;
use Illuminate\Foundation\Console\DownCommand as BaseDownCommand;
use Psr\Log\LoggerInterface;

class Down extends BaseDownCommand
{
    protected $signature = 'down {--message= : Message to be printed to the users (default message if blank). }
                                 {--l|light : Define light mode}';
    private MaintenanceRepository $maintenanceRepository;
    private LoggerInterface $logger;

    public function __construct(
        MaintenanceRepository $maintenanceRepository,
        LoggerInterface $logger,
    ) {
        parent::__construct();
        $this->maintenanceRepository = $maintenanceRepository;
        $this->logger = $logger;
    }

    public function handle(): int
    {
        if ($this->maintenanceRepository->getCurrent() !== null) {
            $this->comment('Application was already in maintenance mode.');

            return 0;
        }

        $isLightMode = $this->option('light') ?: false;
        $message = $this->option('message') ?: "Some really good stuff are coming! Please check back soon.";

        $this->maintenanceRepository->startNow($isLightMode, $message);

        $this->comment('Application is now in maintenance mode.');

        $this->logger->debug('Maintenance mode on');

        return 0;
    }
}
