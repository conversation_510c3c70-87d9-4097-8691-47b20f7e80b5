<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V4\Auth;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Auth\LoginWithCaptchaAnd2FARequest;
use App\Domain\Captcha\CaptchaValidationException;
use App\Domain\Captcha\CaptchaValidationInterface;
use App\Domain\TwoFactorAuth\CodeValidator;
use App\Domain\TwoFactorAuth\Exception\CodeExpiredException;
use App\Domain\TwoFactorAuth\Exception\CodeNotFoundException;
use App\Domain\User\UserRepository;
use App\Infrastructure\TwoFactorAuth\CookieService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotAcceptableHttpException;

final class LoginController extends BaseController
{
    private CookieService $cookieService;
    private CodeValidator $codeValidator;
    private UserRepository $userRepository;
    private CaptchaValidationInterface $captchaValidation;

    private const PASSWORD_NOT_ACCEPTABLE = 'password.not_acceptable';


    public function __construct(
        CookieService $cookieService,
        CodeValidator $codeValidator,
        UserRepository $userRepository,
        CaptchaValidationInterface $captchaValidation
    ) {
        $this->cookieService = $cookieService;
        $this->codeValidator = $codeValidator;
        $this->userRepository = $userRepository;
        $this->captchaValidation = $captchaValidation;
    }

    public function login(LoginWithCaptchaAnd2FARequest $request): JsonResponse
    {
        $this->validateUserCanLogin($request);

        $credentials = $request->only('email', 'password');
        $remember = $request->input('remember', false);

        if (!Auth::attempt($credentials, $remember)) {
            throw new NotAcceptableHttpException(self::PASSWORD_NOT_ACCEPTABLE);
        }

        if (!$request->hasCookie(CookieService::COOKIE_NAME)
            && $this->userRepository->userMustProvide2FA($request->input('email'))
        ) {
            $this->cookieService->setCookie($request->input('email'), $request->input('code'));
        }

        return $this->sendJsonResponse(new Collection([[]]), Response::HTTP_OK);
    }

    private function validateUserCanLogin(LoginWithCaptchaAnd2FARequest $request): void
    {
        $email = $request->input('email');
        if ($this->userRepository->userMustLoginWithSSO($email)) {
            throw new NotAcceptableHttpException('email.sso_mandatory');
        }

        $captchaToken = $request->input('captcha_token');
        $twoFactorAuthCode = $request->input('code');

        if ($captchaToken !== null && $twoFactorAuthCode === null) {
            $this->validateUserCanLoginWithCaptcha($request);
        } else {
            $this->validateUserCanLoginWithTwoFactorAuthCode($request);
        }
    }

    /**
     * Login flow #1 : User provided a captcha token & no 2fa code. We thus :
     *
     * - Validate the captcha
     * - Validate the credentials
     * - Check whether the user requires a 2fa code to continue login
     *
     * @param LoginWithCaptchaAnd2FARequest $request
     * @throws CaptchaValidationException
     */
    private function validateUserCanLoginWithCaptcha(LoginWithCaptchaAnd2FARequest $request): void
    {
        $captchaToken = $request->input('captcha_token');
        $credentials = $request->only('email', 'password');

        if (!$this->captchaValidation->validate($captchaToken) || !Auth::validate($credentials)) {
            throw new NotAcceptableHttpException(self::PASSWORD_NOT_ACCEPTABLE);
        }

        $this->validateTwoFactorAuthCode($request);
    }

    /**
     * Login flow #2 : User provided no captcha token & a 2fa code. We thus :
     *
     * - Validate the 2fa code. We do this first so a potential attacker can't use this flow to poke
     * our users table
     * - Validate the credentials
     *
     * @param LoginWithCaptchaAnd2FARequest $request
     */
    private function validateUserCanLoginWithTwoFactorAuthCode(LoginWithCaptchaAnd2FARequest $request): void
    {
        $this->validateTwoFactorAuthCode($request);

        $credentials = $request->only('email', 'password');
        if (!Auth::validate($credentials)) {
            throw new NotAcceptableHttpException(self::PASSWORD_NOT_ACCEPTABLE);
        }
    }

    private function validateTwoFactorAuthCode(LoginWithCaptchaAnd2FARequest $request): void
    {
        $email = $request->input('email');
        if (!$this->userRepository->userMustProvide2FA($email) || $request->hasCookie(CookieService::COOKIE_NAME)) {
            return;
        }

        $twoFactorAuthCode = $request->input('code');

        if ($twoFactorAuthCode === null) {
            throw new NotAcceptableHttpException('2fa_required');
        }

        $user = $this->userRepository->getUserFromEmail($email);

        try {
            // If the 2FA code is valid we do nothing
            $this->codeValidator->validate($user, $twoFactorAuthCode);
        } catch (CodeNotFoundException $exception) {
            throw new NotAcceptableHttpException('2fa_code.not_acceptable');
        } catch (CodeExpiredException $exception) {
            throw new NotAcceptableHttpException('2fa_code.expired');
        }
    }
}
