<?php

declare(strict_types=1);

namespace App\Application\Listeners\TimecodedElement\TeamCreated;

use App\Application\Events\TeamCreated;
use App\Domain\Team\TeamRepository;
use App\Domain\TimecodedElement\TimecodedElementsFamilyService;

final class AttachGenericFamiliesToTeam
{
    private TimecodedElementsFamilyService $timecodedElementsFamilyService;
    private TeamRepository $teamRepository;

    public function __construct(
        TimecodedElementsFamilyService $timecodedElementsFamilyService,
        TeamRepository $teamRepository
    ) {
        $this->timecodedElementsFamilyService = $timecodedElementsFamilyService;
        $this->teamRepository = $teamRepository;
    }

    public function handle(TeamCreated $teamCreated): void
    {
        $team = $this->teamRepository->getById($teamCreated->teamId);
        if ($team->company->hasTimecodedElementsFeature()) {
            $this->timecodedElementsFamilyService->attachAllGenericFamilies($teamCreated->teamId);
        }
    }
}
