<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Company;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\Company\CompanyStoreRequest;
use App\Domain\Company\CompanyFeatureService;
use App\Domain\Company\CompanyService;
use App\Domain\Plan\MissingPlanException;
use App\Domain\Plan\PlanRepository;
use App\Models\Company;
use App\Models\User;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

final class CompanyCreateController extends BaseController
{
    private PlanRepository $planRepository;
    private CompanyService $companyService;
    private CompanyFeatureService $companyFeatureService;
    private Factory $viewFactory;

    public function __construct(
        PlanRepository $planRepository,
        CompanyService $companyService,
        CompanyFeatureService $companyFeatureService,
        Factory $viewFactory,
    ) {
        $this->authorizeResource(Company::class);
        $this->planRepository = $planRepository;
        $this->companyService = $companyService;
        $this->companyFeatureService = $companyFeatureService;
        $this->viewFactory = $viewFactory;
    }

    public function create(): View
    {
        return $this->viewFactory->make('admin.companies.create', [
            // admin users that can be associated with company
            'adminUsers' => User::retrieveAdminUsers(),
            'plans' => $this->planRepository->getAll()->reverse(),
        ]);
    }

    /**
     * @throws MissingPlanException
     */
    public function store(CompanyStoreRequest $request): JsonResponse
    {
        $companyData = $request->all();
        $companyData['csm_id'] = $request->input('csm_id') ?: null;
        $companyData['plan_id'] = $request->input('plan_id') ?: null;
        $companyData['two_fa_enabled'] = 1;
        $companyData['data_is_restricted'] = $this->companyService
            ->shouldRestrictAccessToCompanyData((int) $companyData['plan_id']);

        /** @var Company $company */
        $company = Company::create($companyData);
        $this->companyFeatureService->updateCompanyFeaturesBasedOnItsPlan($company);

        $redirectUri = route('admin.companies.show', $company->id);

        return new JsonResponse(
            ['success' => true, 'redirect_uri' => $redirectUri],
            Response::HTTP_CREATED
        );
    }
}
