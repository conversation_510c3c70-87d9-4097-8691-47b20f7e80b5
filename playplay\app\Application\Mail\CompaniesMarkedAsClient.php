<?php

declare(strict_types=1);

namespace App\Application\Mail;

use App\Domain\Company\UpdatedButterflyCompaniesDTO;
use Illuminate\Mail\Mailable;

final class CompaniesMarkedAsClient extends Mailable
{
    private UpdatedButterflyCompaniesDTO $updatedButterflyCompaniesDTO;

    public function __construct(UpdatedButterflyCompaniesDTO $updatedButterflyCompaniesDTO)
    {
        $this->updatedButterflyCompaniesDTO = $updatedButterflyCompaniesDTO;
    }

    public function build(): self
    {
        return $this->from('<EMAIL>', __('email.from'))
            ->subject('Butterfly companies marked as Client')
            ->markdown('emails.companies-marked-as-client', ['companies' => $this->updatedButterflyCompaniesDTO]);
    }
}
