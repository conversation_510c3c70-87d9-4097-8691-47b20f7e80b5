<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\ProjectScreen;

use App\Application\Events\ProjectUpdated;
use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Project\ProjectReorderScreensRequest;
use App\Domain\ProjectScreen\ProjectScreenService;
use App\Domain\User\Action\UserAction;
use App\Domain\User\Action\UserActionService;
use App\Models\Project;
use App\Models\ProjectScreen;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

final class ReorderProjectScreensController extends BaseController
{
    use AuthorizesRequests;

    private ProjectScreenService $projectScreenService;
    private UserActionService $userActionService;
    private Dispatcher $eventDispatcher;

    public function __construct(
        ProjectScreenService $projectScreenService,
        UserActionService $userActionService,
        Dispatcher $eventDispatcher
    ) {
        $this->projectScreenService = $projectScreenService;
        $this->userActionService = $userActionService;
        $this->eventDispatcher = $eventDispatcher;
    }

    public function __invoke(Project $project, ProjectReorderScreensRequest $request): JsonResponse
    {
        $this->authorize('reorder', $project);

        $orderedProjectScreenIds = $request->input('project_screen_ids');

        $projectScreensToMove = $project->projectScreens
            ->filter(fn(ProjectScreen $screen) => in_array($screen->id, $orderedProjectScreenIds, true));

        if (count($projectScreensToMove) !== count($orderedProjectScreenIds)) {
            throw ValidationException::withMessages(['Cannot delete project screens not belonging to project']);
        }

        $this->projectScreenService->reorder(
            $project,
            $orderedProjectScreenIds
        );

        $this->eventDispatcher->dispatch(new ProjectUpdated($project));

        $this->userActionService->addUserAction(
            new UserAction(
                'project-screens-bulk-reorder',
                [],
                $project->team_id,
                $project->id,
            )
        );

        return $this->sendJsonResponse(new Collection([]), Response::HTTP_OK);
    }
}
