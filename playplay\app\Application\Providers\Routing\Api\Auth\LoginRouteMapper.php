<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Auth;

use App\Application\Http\Controllers\Api\V2\Auth\LoginController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class LoginRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::post('login', [LoginController::class, 'login'])->name('login');
    }
}
