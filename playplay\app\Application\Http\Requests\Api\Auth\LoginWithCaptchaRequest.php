<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\Auth;

use Illuminate\Foundation\Http\FormRequest;

class LoginWithCaptchaRequest extends FormRequest
{
    public static function getRules(): array
    {
        return [
            'email' => ['required', 'email', 'exists:users,email', 'uniqueUserInactive'],
            'password' => ['required'],
            'captcha_token' => ['required', 'string'],
            'remember' => ['sometimes', 'boolean'],
        ];
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return static::getRules();
    }
}
