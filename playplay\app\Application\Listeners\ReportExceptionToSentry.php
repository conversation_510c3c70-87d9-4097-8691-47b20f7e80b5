<?php

declare(strict_types=1);

namespace App\Application\Listeners;

use App\Application\Exceptions\ScopeConfigurationFactory;
use App\Models\User;
use Illuminate\Contracts\Auth\Guard;
use Sentry\State\HubInterface;
use Throwable;
use Illuminate\Config\Repository as ConfigRepository;

final class ReportExceptionToSentry
{
    private HubInterface $sentryService;
    private Guard $guard;
    private ConfigRepository $config;

    public function __construct(HubInterface $sentryService, Guard $guard, ConfigRepository $config)
    {
        $this->sentryService = $sentryService;
        $this->guard = $guard;
        $this->config = $config;
    }

    public function handle(Throwable $exception): void
    {
        /** @var User $user */
        $user = $this->guard->user();
        $this->sentryService->configureScope(
            (new ScopeConfigurationFactory($this->config))->create($exception, $user)
        );
        $this->sentryService->captureException($exception);
    }
}
