<?php

namespace App\Application\Http\Requests\Admin\Team;

use App\Models\Team;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Routing\Route;
use Illuminate\Validation\Factory;

class TeamRequest extends FormRequest
{
    public function __construct(Factory $validationFactory)
    {
        parent::__construct();
        // Check that users exists and belongs to the same company
        $this->addUsersValidator($validationFactory);
        $this->addHasUsersInMultipleTeamsValidator($validationFactory);
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function messages()
    {
        return [
            'name.required' => 'Name is required',
            'users.users_exists_in_company',
        ];
    }

    public function rules(): array
    {
        $rules = [
            'name' => ['sometimes', 'required'],
            'is_arabic' => ['sometimes', 'boolean'],
            'users.*.id' => ['sometimes', 'userExistsInCompany'],
            'users.*.app_role_id' => ['sometimes', 'integer', 'exists:app_roles,id'],
            'company_id' => ['sometimes', 'exists:companies,id', 'has_not_user_in_multiple_teams'],
        ];
        /** @todo separate request to different requests to avoid this condition */
        if ($this->method() === 'POST') {
            $rules['company_id'] = ['required', 'exists:companies,id'];
        }

        return $rules;
    }

    private function addHasUsersInMultipleTeamsValidator(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'has_not_user_in_multiple_teams',
            function ($attribute, $value) {
                /** @var Team $team */
                $team = request()->route('team');

                return !$team->has_users_in_multiple_teams;
            }
        );
    }

    private function addUsersValidator(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'userExistsInCompany',
            function ($attribute, $value) {
                /** @var Route $route */
                $route = request()?->route();
                /** @var ?Team $team */
                $team = $route->parameter('team');
                return User::query()->where('id', '=', $value)
                    ->where('company_id', '=', $team?->company_id)
                    ->exists();
            }
        );
    }
}
