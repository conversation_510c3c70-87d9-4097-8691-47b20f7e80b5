<?php

declare(strict_types=1);

namespace App\Domain\Font;

use InvalidArgumentException;

final class FontSize
{
    public const REGULAR = 'regular';
    public const AVAILABLE_SIZES_FOR_REGULAR = [
        self::SIZE_2,
        self::SIZE_3,
        self::SIZE_4,
        self::SIZE_5,
        self::SIZE_6,
        self::SIZE_7,
        self::SIZE_8,
        self::SIZE_9,
    ];
    private const SMALL = 'small';
    private const LARGE = 'large';
    private const SIZE_2 = 'size2';
    private const SIZE_3 = 'size3';
    private const SIZE_4 = 'size4';
    private const SIZE_5 = 'size5';
    private const SIZE_6 = 'size6';
    private const SIZE_7 = 'size7';
    private const SIZE_8 = 'size8';
    private const SIZE_9 = 'size9';
    private string $regular;
    private string|int|array $index;

    /**
     * @throw InvalidArgumentException
     */
    public function __construct(string $regular)
    {
        if (!in_array($regular, self::AVAILABLE_SIZES_FOR_REGULAR)) {
            throw new InvalidArgumentException();
        }

        $this->regular = $regular;
        $this->index = str_replace('size', '', $regular);
    }

    public function getSize(?string $size): string
    {
        switch ($size) {
            case self::SMALL:
                return $this->getSmall();
            case self::LARGE:
                return $this->getLarge();
            default:
                return $this->getRegular();
        }
    }

    private function getLarge(): string
    {
        $newIndex = $this->index + 1;

        return "size{$newIndex}";
    }

    private function getRegular(): string
    {
        return $this->regular;
    }

    private function getSmall(): string
    {
        $newIndex = $this->index - 1;

        return "size{$newIndex}";
    }
}
