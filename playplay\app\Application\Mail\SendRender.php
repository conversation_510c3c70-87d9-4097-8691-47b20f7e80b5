<?php

declare(strict_types=1);

namespace App\Application\Mail;

use App\Domain\Render\RenderInterface;
use App\Models\Renders\RenderStory;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Symfony\Component\HttpFoundation\File\File;

final class SendRender extends Mailable
{
    use Queueable, SerializesModels;

    private File $file;
    private User $user;
    private string $countChunks = '';

    public function __construct(RenderInterface $render, $file, User $user, int $nth = 0)
    {
        $this->file = $file;
        $this->user = $user;
        if ($render instanceof RenderStory) {
            $nbChunks = count($render->chunk_urls);
            $nth++;
            $this->countChunks = " - {$nth}/{$nbChunks}";
        }
    }

    public function build(): self
    {
        return $this->from('<EMAIL>')
            ->subject(__('email.send_video.subject', ['countChunks' => $this->countChunks]))
            ->markdown('emails.render', ['name' => $this->user->first_name])
            ->attach(
                $this->file->getPathname(),
                [
                    'as' => $this->file->getFilename(),
                ]
            );
    }
}
