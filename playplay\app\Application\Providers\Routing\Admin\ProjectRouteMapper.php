<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Admin;

use App\Application\Http\Controllers\Admin\ProjectController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class ProjectRouteMapper implements RouteMapper
{

    public function map(): void
    {
        Route::group([
            'prefix' => '/projects',
            'as' => 'projects.'
        ], static function (Router $router) {
            $router->get('/', [ProjectController::class, 'index'])->name('index');
            $router->put('/{project}', [ProjectController::class, 'restore'])->name('restore');
            $router->get('/{project}/renders', [ProjectController::class, 'renders'])->name('renders');
        });
    }
}
