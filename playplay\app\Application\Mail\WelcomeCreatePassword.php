<?php

declare(strict_types=1);

namespace App\Application\Mail;

use App\Models\User;
use Illuminate\Contracts\Routing\UrlGenerator;
use Illuminate\Foundation\Application;
use Illuminate\Mail\Mailable;

final class WelcomeCreatePassword extends Mailable
{
    private User $user;

    private Application $app;

    private UrlGenerator $urlGenerator;

    public function __construct(User $user, Application $app, UrlGenerator $urlGenerator)
    {
        $this->user = $user;
        $this->app = $app;
        $this->urlGenerator = $urlGenerator;
    }

    public function build(): self
    {
        return $this->from('<EMAIL>', __('email.from'))
            ->subject(__('email.welcome_external_user.subject'))
            ->markdown(
                'emails.welcome-create-password',
                [
                    'name' => $this->user->first_name,
                    'url' => $this->getCreatePasswordPageUrl(),
                ]
            );
    }

    private function getCreatePasswordPageUrl(): string
    {
        $token = $this->app->get('auth.password.broker.new_users')?->createToken($this->user);
        $email = http_build_query(['email' => $this->user->email]);

        return $this->urlGenerator->to("/app/password/create/{$token}?{$email}");
    }
}
