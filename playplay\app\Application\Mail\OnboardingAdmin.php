<?php

declare(strict_types=1);

namespace App\Application\Mail;

use App\Models\User;
use Illuminate\Mail\Mailable;

final class OnboardingAdmin extends Mailable
{
    private User $user;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    public function build(): Mailable
    {
        return $this->from('<EMAIL>', __('email.from'))
            ->subject(__('email.onboarding_admin.subject'))
            ->markdown('emails.onboarding-admin', [
                'name' => $this->user->first_name,
                'loginUrl' => $this->getLoginUrl(),
            ]);
    }

    private function getLoginUrl(): string
    {
        return url('app/login?lang=' . $this->user->preferredLocale()) . "#sso";
    }
}
