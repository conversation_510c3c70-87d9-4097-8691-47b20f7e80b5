<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\TransferMedia;

use App\Application\Http\Requests\Api\TransferMedia\GetProjectInfosRequest;
use App\Domain\Project\ProjectRepository;
use Illuminate\Routing\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

final class GetProjectInfosController extends Controller
{
    private ProjectRepository $projectRepository;

    public function __construct(ProjectRepository $projectRepository)
    {
        $this->projectRepository = $projectRepository;
    }

    public function __invoke(GetProjectInfosRequest $request): JsonResponse
    {
        $project = $this->projectRepository->findOneByUuid($request->get('uuid'));

        if ($project === null) {
            throw new NotFoundHttpException();
        }

        return new JsonResponse([
            'project_title' => $project->title,
            'user_name' => $project->user->name,
        ]);
    }
}
