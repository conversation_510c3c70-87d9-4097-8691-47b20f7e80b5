<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api;

use App\Application\Http\Requests\Api\SignUp\FreeTrialSignUpRequest;
use App\Application\Mail\WelcomeCreatePassword;
use App\Domain\User\Exception\EmailAlreadyUsedException;
use App\Services\SignUpService;
use Illuminate\Contracts\Mail\Mailer;
use Illuminate\Contracts\Routing\UrlGenerator;
use Illuminate\Foundation\Application;
use Illuminate\Routing\Controller;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

final class FreeTrialSignUpController extends Controller
{
    private SignUpService $signUpService;

    private Mailer $mailer;

    private Application $app;

    private UrlGenerator $urlGenerator;

    public function __construct(
        SignUpService $signUpService,
        Mailer $mailer,
        Application $app,
        UrlGenerator $urlGenerator
    ) {
        $this->signUpService = $signUpService;
        $this->mailer = $mailer;
        $this->app = $app;
        $this->urlGenerator = $urlGenerator;
    }

    public function freeTrialSignUp(FreeTrialSignUpRequest $request): Response
    {
        try {
            [$user,] = $this->signUpService->createAccount(
                $request->only([
                    'first_name',
                    'last_name',
                    'email',
                    'language',
                    'company_name',
                ]),
                []
            );
        } catch (EmailAlreadyUsedException) {
            throw new UnprocessableEntityHttpException('Email already used');
        }

        $this->mailer->to($user)->send(new WelcomeCreatePassword($user, $this->app, $this->urlGenerator));

        return new Response(null, Response::HTTP_CREATED);
    }
}
