<?php

namespace App\Application\Http\Requests\Api\ProcessedMedia\Store;

use App\Domain\RawMedia\RawMediaSource;
use App\Domain\Render\RenderMedia\Transformation\KeepSizeValue;
use App\Models\ProcessedMedia;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProcessedMediaGifTransformationsStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function messages(): array
    {
        return [
            'crop.required_without' => 'The crop field is required when keepSize is not present',
            'keepSize.required_without' => 'The keepSize field is required when crop is not present',
            'keepSize.in' => 'The selected keepSize is invalid: '.implode('|', KeepSizeValue::getAllValues()),
        ];
    }

    public function rules(): array
    {
        return [
            'source' => ['required', Rule::in($this->getValidSources())],
            'crop' => ['nullable', 'required_without:keepSize', 'crop'],
            'project_id' => ['required', 'exists:projects,id'],
            'param_id' => ['required', 'numeric', 'exists:layout_params,id', 'paramIsInProject'],
            'raw_media_id' => ['required', 'exists:raw_medias,id', 'isGif'],
            'keepSize' => ['nullable', 'required_without:crop', Rule::in(KeepSizeValue::getAllValues())],
        ];
    }

    private function getValidSources(): array
    {
        return [
            RawMediaSource::UPLOAD,
            RawMediaSource::GIPHY,
            ProcessedMedia::SOURCE_FAVORITES,
            ProcessedMedia::SOURCE_RECENTLY_USED,
            ProcessedMedia::SOURCE_LIBRARY,
        ];
    }
}
