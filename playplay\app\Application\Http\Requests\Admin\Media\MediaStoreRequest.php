<?php

namespace App\Application\Http\Requests\Admin\Media;

use Illuminate\Foundation\Http\FormRequest;

/**
 * @deprecated This is v1 legacy, media are replaced by RawMedia and ProcessedMedia.
 */
class MediaStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title' => 'required',
            'file' => 'required',
        ];
    }
}
