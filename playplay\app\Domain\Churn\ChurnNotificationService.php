<?php

declare(strict_types=1);

namespace App\Domain\Churn;

use App\Domain\Billing\BillingPlanRepository;
use App\Domain\Billing\SubscriptionDuration;
use App\Models\Subscription;

final class ChurnNotificationService
{
    private ChurnNotificationSender $notificationSender;

    private BillingPlanRepository $billingPlanRepository;

    public function __construct(
        ChurnNotificationSender $notificationSender,
        BillingPlanRepository $billingPlanRepository
    ) {
        $this->notificationSender = $notificationSender;
        $this->billingPlanRepository = $billingPlanRepository;
    }

    /**
     * @throws NotificationException
     */
    public function sendChurnNotification(Subscription $subscription): void
    {
        $billingPlan = $this->billingPlanRepository->getBillingPlan($subscription->billingPlan->stripe_plan_id);

        $this->notificationSender->send(
            new ChurnMessage(
                $subscription->company,
                $billingPlan->subscriptionRevenue,
                new SubscriptionDuration($subscription->started_at, $subscription->ended_at)
            )
        );
    }
}
