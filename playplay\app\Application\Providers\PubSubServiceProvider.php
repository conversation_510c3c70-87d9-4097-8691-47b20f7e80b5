<?php

namespace App\Application\Providers;

use App\Domain\Queueing\PubSubService as PubSubServiceInterface;
use App\Infrastructure\Queueing\PubSubService;
use Google\Cloud\PubSub\PubSubClient;
use Illuminate\Support\ServiceProvider;

final class PubSubServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->app->singleton(PubSubClient::class, function () {
            return new PubSubClient([
                'projectId' => config('filesystems.disks.gcs.project_id'),
                'keyFilePath' => config('filesystems.disks.gcs.key_file'),
            ]);
        });
    }

    public function register(): void
    {
        $this->app->bind(PubSubServiceInterface::class, PubSubService::class);
    }
}
