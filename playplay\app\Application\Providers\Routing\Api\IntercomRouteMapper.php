<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\V2\Intercom\GetHashedUserIdController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class IntercomRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::get('/intercom/hash', GetHashedUserIdController::class)->name('intercom-hash');
    }
}
