<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Company;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Infrastructure\Company\QueryBuilder\CompanyQueryBuilder;
use App\Models\Company;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

final class CompanyListingController extends BaseController
{
    private CompanyQueryBuilder $companyQueryBuilder;
    private Guard $auth;
    private Factory $viewFactory;

    public function __construct(
        CompanyQueryBuilder $companyQueryBuilder,
        Guard $auth,
        Factory $viewFactory
    ) {
        $this->authorizeResource(Company::class);
        $this->companyQueryBuilder = $companyQueryBuilder;
        $this->auth = $auth;
        $this->viewFactory = $viewFactory;
    }

    public function index(Request $request): View
    {
        return $this->viewFactory->make(
            'admin.companies.index',
            ['companies' => $this->getPaginatedFilteredCompanies($request)]
        );
    }

    public function filters(Request $request): JsonResponse
    {
        $term = $request->get('term');
        $companies = $this->companyQueryBuilder->getBuilder()->select('id', 'name AS text')
            ->orderBy('name')
            ->where('name', 'LIKE', "%{$term}%")
            ->when($this->auth->user()->isUsAdminPlayPlayer(), function (QueryBuilder $qb) {
                return $qb->where('data_is_restricted', false);
            })->simplePaginate();

        return new JsonResponse(['success' => true, 'results' => $companies], Response::HTTP_OK);
    }

    private function getPaginatedFilteredCompanies(Request $request): Paginator
    {
        $type = $request->input('type');
        $status = $request->input('status', Company::STATUS_ACTIVE);
        $search = $request->input('search');
        $perPage = $request->input('per_page', 10);

        return $this->companyQueryBuilder->getBuilder()
            ->orderBy('name')
            ->when($type, function (QueryBuilder $qb) use ($type) {
                return $qb->where('type', $type);
            })->when($status, function (QueryBuilder $qb) use ($status) {
                return $qb->where('status', $status);
            })->when($search, function (QueryBuilder $qb) use ($search) {
                return $qb->where('name', 'LIKE', "%{$search}%");
            })->when($this->auth->user()->isUsAdminPlayPlayer(), function (QueryBuilder $qb) {
                return $qb->where('data_is_restricted', false);
            })->with([
                'csm:id,first_name',
                'plan:id,displayed_name',
            ])->paginate($perPage)
            ->appends($request->all());
    }
}
