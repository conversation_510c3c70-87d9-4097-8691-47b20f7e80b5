<?php

namespace App\Application\Providers\Routing\Api\ProjectScreen;

use App\Application\Http\Controllers\Api\V2\ProjectScreen\CreateProjectScreenController;
use App\Application\Http\Controllers\Api\V2\ProjectScreen\DeleteManyProjectScreensController;
use App\Application\Http\Controllers\Api\V2\ProjectScreen\DuplicateManyProjectScreensController;
use App\Application\Http\Controllers\Api\V2\ProjectScreen\UpdateProjectScreenController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class ProjectScreenRouteMapper implements RouteMapper
{
    /** @var RouteMapper[] */
    private array $routeMappers;

    public function __construct()
    {
        $this->routeMappers = [
            new ScreenFamilyRouteMapper(),
            new ProjectScreenLayoutRouteMapper(),
        ];
    }

    public function map(): void
    {
        Route::group([
            'prefix' => '/projects/{project}/projectScreens',
            'as' => 'projects.projectScreens.',
        ], function (Router $router) {
            $router->post('/', CreateProjectScreenController::class)->name('store');
            $router->delete('/', DeleteManyProjectScreensController::class)->name('bulk-delete');
            $router->post('/duplicate-many', DuplicateManyProjectScreensController::class)->name('duplicate-many');

            Route::group(['prefix' => '/{projectScreen}'], function (Router $router) {
                $router->match(['PUT', 'PATCH'], '', UpdateProjectScreenController::class)->name('update');
                foreach ($this->routeMappers as $routeMapper) {
                    $routeMapper->map();
                }
            });
        });
    }
}
