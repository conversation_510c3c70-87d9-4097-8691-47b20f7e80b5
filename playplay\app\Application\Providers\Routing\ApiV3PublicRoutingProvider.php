<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing;

use App\Application\Providers\Routing\Api\Auth\LoginWithCaptchaRouteMapper;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider;
use Illuminate\Support\Facades\Route;

final class ApiV3PublicRoutingProvider extends RouteServiceProvider
{
    /** @var RouteMapper[] */
    private array $routeMappers;

    public function __construct(Application $app)
    {
        parent::__construct($app);
        // Declare all route mappers
        $this->routeMappers = [
            new LoginWithCaptchaRouteMapper(),
        ];
    }

    public function map(): void
    {
        Route::group([
            'middleware' => ['api'],
            'prefix' => 'api/v3',
            'as' => 'api.v3.',
        ], function () {
            foreach ($this->routeMappers as $routeMapper) {
                $routeMapper->map();
            }
        });
    }
}
