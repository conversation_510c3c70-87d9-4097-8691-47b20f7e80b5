<?php

namespace App\Application\Http\Requests\Api\User;

use App\Domain\Localization\SupportedLanguages;
use Illuminate\Contracts\Validation\Factory;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class UserUpdateRequest extends FormRequest
{
    public static function rules(): array
    {
        return [
            'current_password' => [
                'required_with_all:new_password,new_password_confirmation',
                'current_password_valid',
            ],
            'new_password' => [
                'required_with_all:current_password,new_password_confirmation',
                'check_password_rule',
                'confirmed',
            ],
            'new_password_confirmation' => ['required_with_all:current_password,new_password', 'check_password_rule'],
            'language' => ['sometimes', Rule::in(SupportedLanguages::values())],
        ];
    }

    public function __construct()
    {
        $validationFactory = app(Factory::class);
        $this->checkCurrentPasswordValid($validationFactory);
        parent::__construct();
    }

    public function authorize(): bool
    {
        return true;
    }

    public function checkCurrentPasswordValid(Factory $validationFactory): void
    {
        $validationFactory->extendImplicit(
            'current_password_valid',
            function ($attribute, $value, $parameters) {
                $user = auth()->user();

                return !$value || $user && Hash::check($value, $user->getAuthPassword());
            }
        );
    }
}
