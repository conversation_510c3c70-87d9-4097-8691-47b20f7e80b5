<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\DataSeeding\SeedingController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class DataSeedingRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'as' => 'data-seeding.',
        ], static function (Router $router) {
            $router->get('/seed-data', [SeedingController::class, 'seed'])->name('seed');
            $router->get('/clean-seeds', [SeedingController::class, 'clean'])->name('clean');
        });
    }
}
