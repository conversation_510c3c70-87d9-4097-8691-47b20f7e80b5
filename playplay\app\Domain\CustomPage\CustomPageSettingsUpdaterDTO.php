<?php

declare(strict_types=1);

namespace App\Domain\CustomPage;

final class CustomPageSettingsUpdaterDTO
{
    public readonly string $title;
    public readonly string $description;
    public readonly string $primaryColor;
    public readonly int $fontId;
    public readonly ?int $mainLogoProcessedMediaId;

    public function __construct(
        ?string $title,
        ?string $description,
        string $primaryColor,
        int $fontId,
        ?int $mainLogoProcessedMediaId
    ) {
        $this->title = $title ?? '';
        $this->description = $description ?? '';
        $this->primaryColor = $primaryColor;
        $this->fontId = $fontId;
        $this->mainLogoProcessedMediaId = $mainLogoProcessedMediaId;
    }
}
