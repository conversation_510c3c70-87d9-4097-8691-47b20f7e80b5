<?php

namespace App\Application\Http\Requests\Api\Project\ProcessedMedia\Store;

use App\Application\Http\Requests\Api\Project\ProcessedMedia\ProcessedMediaAudioRequest;

class ProcessedMediaAudioTrimRequest extends ProcessedMediaAudioRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'trim_start' => ['required', 'numeric', 'min:0'],
            'trim_end' => ['required', 'numeric', 'greater_than:trim_start'],
            'start' => ['required', 'numeric', 'min:0'],
        ];
    }
}
