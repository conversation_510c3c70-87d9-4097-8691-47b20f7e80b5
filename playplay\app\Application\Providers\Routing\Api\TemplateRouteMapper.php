<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\V2\Template\GetHomepageTeamTemplatesController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class TemplateRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/teams',
            'as' => 'teams.',
        ], static function (Router $router) {
            $router->get('/{team}/templates', GetHomepageTeamTemplatesController::class)
                ->name('templates.index');
        });
    }
}
