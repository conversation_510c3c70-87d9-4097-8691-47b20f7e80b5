<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\TimecodedElement;

use App\Application\Http\Controllers\Api\BaseController;
use App\Domain\TimecodedElement\Serializers\TimecodedElementsSerializer;
use App\Domain\TimecodedElement\TimecodedElementDuplicator;
use App\Models\ProjectScreen;
use App\Models\TimecodedElement\TimecodedElement;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

final class DuplicateTimecodedElementController extends BaseController
{
    use AuthorizesRequests;

    private TimecodedElementsSerializer $timecodedElementsSerializer;
    private TimecodedElementDuplicator $timecodedElementDuplicator;

    public function __construct(
        TimecodedElementsSerializer $timecodedElementsSerializer,
        TimecodedElementDuplicator $timecodedElementDuplicator,
    ) {
        $this->timecodedElementsSerializer = $timecodedElementsSerializer;
        $this->timecodedElementDuplicator = $timecodedElementDuplicator;
    }

    /**
     * @throws AuthorizationException
     */
    public function __invoke(
        ProjectScreen $projectScreen,
        TimecodedElement $timecodedElement
    ): JsonResponse {
        $this->authorize('duplicate', [$timecodedElement, $projectScreen]);

        $duplicatedTimecodedElement = $this->timecodedElementDuplicator->duplicate($timecodedElement);

        return $this->sendJsonResponse(
            new Collection(
                $this->timecodedElementsSerializer->serialize(
                    [$duplicatedTimecodedElement]
                )
            ),
            Response::HTTP_CREATED
        );
    }
}
