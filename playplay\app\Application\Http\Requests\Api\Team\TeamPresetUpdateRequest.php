<?php

namespace App\Application\Http\Requests\Api\Team;

use App\Domain\Project\ProjectFormat;
use App\Models\Team;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Routing\Route;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Factory;

class TeamPresetUpdateRequest extends FormRequest
{
    public static function getRules(): array
    {
        return [
            'logos' => ['sometimes', 'array'],
            'logos.*.*' => ['exists:processed_medias,id', 'maxPrimaryLogosIsReached'],
            'colors' => ['sometimes', 'array', 'nullable', 'min:2'],
            'colors.*' => ['sometimes', 'hexColor'],
            'outros' => ['sometimes', 'array'],
            'outros.*' => ['sometimes', 'array', 'isValidFormat'],
            'outros.*.*' => ['exists:processed_medias,id'],
        ];
    }

    public function __construct()
    {
        $validationFactory = app(Factory::class);
        $this->checkIfFormatsAreValid($validationFactory);
        $this->checkMaxPrimaryLogos($validationFactory);

        parent::__construct();
    }

    public function authorize(): bool
    {
        return true;
    }

    public function checkIfFormatsAreValid(Factory $validationFactory): void
    {
        $validationFactory->extendImplicit('isValidFormat', function ($attribute) {
            return in_array(explode('.', $attribute)[1], ProjectFormat::FORMATS);
        });
    }

    public function checkMaxPrimaryLogos(Factory $validationFactory): void
    {
        $validationFactory->extendImplicit(
            'maxPrimaryLogosIsReached',
            function ($attribute, $value) {
                if (!$value) {
                    return true;
                }

                /** @var Route $route */
                $route = request()?->route();
                /** @var Team $team */
                $team = $route->parameter('team');
                $maxLogo = DB::table('company_features')
                    ->join('features', 'features.id', '=', 'company_features.feature_id')
                    ->where('name', 'max_logos')
                    ->where('company_id', $team->company_id)
                    ->value('value');

                return $maxLogo === null || $maxLogo >= count(request()->get('logos')['primary']);
            }
        );
    }

    public function rules(): array
    {
        return static::getRules();
    }
}
