<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Front;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Domain\ShareableLink\ShareableLinkMeta;
use App\Domain\ShareableLink\ShareableLinkRepository;
use App\Domain\Video\Format\VideoFormatRepository;
use Illuminate\Config\Repository as ConfigRepository;
use Illuminate\Support\Facades\Http;

final class ShareLinkController extends BaseController
{
    private ConfigRepository $configRepository;
    private ShareableLinkRepository $shareableLinkRepository;
    private VideoFormatRepository $videoFormatRepository;

    public function __construct(
        ConfigRepository $configRepository,
        ShareableLinkRepository $shareLinkMetaRepository,
        VideoFormatRepository $videoFormatRepository
    ) {
        $this->configRepository = $configRepository;
        $this->shareableLinkRepository = $shareLinkMetaRepository;
        $this->videoFormatRepository = $videoFormatRepository;
    }

    public function show(string $companySlug, string $shareLinkToken)
    {
        $frontPageUrl = $this->configRepository->get('app.vue_url') ?: $this->configRepository->get('app.url');
        $frontPageRawContent = Http::get($frontPageUrl . "/app/index.html")->body();

        $shareableLink = $this->shareableLinkRepository->getByToken($shareLinkToken);
        if ($shareableLink === null || $shareableLink->has_password === true) {
            return $frontPageRawContent;
        }

        $sharelinkMeta = new ShareableLinkMeta(
            $shareableLink,
            $this->videoFormatRepository->getByFormat($shareableLink->renderProjectHtml->format ?? 'square')
        );

        $metaOG = <<<META
            <meta charset='utf-8'>
            <meta property='og:title' content='{$sharelinkMeta->ogTitle()}'/>
            <meta property='og:image' content='{$sharelinkMeta->ogImage()}'/>
            <meta property='og:description' content='{$sharelinkMeta->ogDescription()}'/>
            <meta property='og:url' content='{$sharelinkMeta->ogUrl()}'/>
            <meta property='og:video:type' content='video/mp4'/>
            <meta property='og:video' content='{$sharelinkMeta->ogVideo()}'/>
            <meta property='og:video:secure_url' content='{$sharelinkMeta->ogVideo()}'/>
            <meta property='og:video:width' content='{$sharelinkMeta->ogVideoWidth()}'/>
            <meta property='og:video:height' content='{$sharelinkMeta->ogVideoHeight()}'/>
            META;

        return str_replace('<meta charset="utf-8">', $metaOG, $frontPageRawContent);
    }
}
