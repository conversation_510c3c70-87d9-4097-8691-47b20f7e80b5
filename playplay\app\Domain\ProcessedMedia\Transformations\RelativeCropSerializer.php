<?php

declare(strict_types=1);

namespace App\Domain\ProcessedMedia\Transformations;

use App\Domain\Render\RenderMedia\Crop\CropFactory;
use App\Models\ProcessedMedia;

final class RelativeCropSerializer
{
    public function serialize(ProcessedMedia $processedMedia): ?array
    {
        $crop = $processedMedia->lastRender?->data?->getCrop();

        if ($crop === null || $crop->isNull()) {
            return null;
        }

        $relativeCrop = $crop->getRelative();
        if ($relativeCrop !== null && !$relativeCrop->isNull()) {
            return $relativeCrop->toArray();
        }

        $absoluteCrop = $crop->getAbsolute();
        if ($absoluteCrop === null
            || $absoluteCrop->isNull()
            || $processedMedia->rawMedia->height === null
            || $processedMedia->rawMedia->width === null
        ) {
            return null;
        }

        $newCrop = CropFactory::createCropFromAbsoluteCropDataAndOriginalMediaDimensions(
            $absoluteCrop,
            $processedMedia->rawMedia->width,
            $processedMedia->rawMedia->height
        );

        return $newCrop->getRelative()?->toArray();
    }
}
