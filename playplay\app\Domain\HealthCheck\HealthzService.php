<?php

declare(strict_types=1);

namespace App\Domain\HealthCheck;

class HealthzService
{
    /** @var InfrastructureChecker[] */
    private iterable $infrastructureCheckers;

    public function __construct(iterable $infrastructureCheckers)
    {
        $this->infrastructureCheckers = $infrastructureCheckers;
    }

    /**
     * @throws HealthzException
     */
    public function check(): void
    {
        foreach ($this->infrastructureCheckers as $infrastructureChecker) {
            if (!$infrastructureChecker->isFine()) {
                throw new HealthzException(get_class($infrastructureChecker) . ' check failed.');
            }
        }
    }
}
