<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\TeamPreset;

use App\Application\Http\Requests\Admin\TeamPresets\TeamPresetLogosRequest;
use App\Application\Http\Requests\Admin\TeamPresets\TeamPresetsColorsRequest;
use App\Application\Http\Requests\Admin\TeamPresets\TeamPresetsMusicsRequest;
use App\Application\Http\Requests\Admin\TeamPresets\TeamPresetsOutrosRequest;
use App\Domain\Font\FontRepository;
use App\Domain\Music\MusicListRepository;
use App\Domain\Outro\OutroFileService;
use App\Domain\TeamPreset\ColorSanitizer;
use App\Domain\TeamPreset\TeamPresetService;
use App\Models\Team;
use App\Models\TeamPreset;
use App\Services\TeamPresetLogosFileService;
use Illuminate\Config\Repository as ConfigRepository;
use Illuminate\Contracts\View\Factory as ViewFactory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;

final class TeamPresetController extends AbstractTeamPresetController
{
    private ConfigRepository $config;
    private OutroFileService $outroFileService;
    private MusicListRepository $musicListRepository;
    private ViewFactory $viewFactory;
    private FontRepository $fontRepository;

    public function __construct(
        ConfigRepository $config,
        OutroFileService $outroFileService,
        MusicListRepository $musicListRepository,
        FontRepository $fontRepository,
        ViewFactory $viewFactory,
    ) {
        parent::__construct();

        $this->config = $config;
        $this->outroFileService = $outroFileService;
        $this->musicListRepository = $musicListRepository;
        $this->fontRepository = $fontRepository;
        $this->viewFactory = $viewFactory;
    }

    public function deleteForcedOutroInFormat(Team $team, TeamPreset $teamPreset, string $format): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);
        $teamPreset->outros = $this->outroFileService->deleteForcedOutroInFormat($teamPreset->outros, $format);
        $teamPreset->save();

        return new JsonResponse();
    }

    public function editBranding(Team $team, TeamPreset $teamPreset): View
    {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('edit', $teamPreset);

        $tabActive = 'branding';
        $defaultColors = $this->config->get('team-preset-colors.colors.main.values');

        return $this->viewFactory->make('admin.teams.team-presets.branding.edit', [
            'team' => $team,
            'teamPreset' => $teamPreset,
            'tabActive' => $tabActive,
            'defaultColors' => $defaultColors,
            'outros' => $this->outroFileService->retrieveOutrosWithRawMedia($teamPreset),
            'extension' => $this->config->get('app.playplay.fonts.extension'),
            'basicFonts' => $this->fontRepository->getBasicFonts(),
        ]);
    }

    public function editMusics(Team $team, TeamPreset $teamPreset): View
    {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('edit', $teamPreset);

        $tabActive = 'musics';

        $musicOptionLists = $this->musicListRepository->getOrderedMusicLists();

        return $this->viewFactory->make(
            'admin.teams.team-presets.musics.edit',
            compact('team', 'teamPreset', 'tabActive', 'musicOptionLists')
        );
    }

    public function updateLogos(
        Team $team,
        TeamPreset $teamPreset,
        TeamPresetLogosRequest $request,
        TeamPresetLogosFileService $teamPresetLogosFileService
    ): RedirectResponse {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('update', $teamPreset);

        $logos = $teamPresetLogosFileService->handleLogosFromTeamPresetLogosRequest($request, $teamPreset);
        $teamPreset->logos = $logos;

        $teamPreset->save();

        return redirect()->back();
    }

    public function updateColors(
        Team $team,
        TeamPreset $teamPreset,
        TeamPresetsColorsRequest $request,
        ColorSanitizer $colorSanitizer
    ): RedirectResponse {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('update', $teamPreset);

        $teamPreset->colors = $colorSanitizer->sanitize($request->get('colors', []));
        $teamPreset->save();

        return redirect()->back();
    }

    public function updateMusics(
        Team $team,
        TeamPreset $teamPreset,
        TeamPresetsMusicsRequest $request
    ): RedirectResponse {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('update', $teamPreset);

        $teamPreset->musics = $request->get('musics');
        $optionLists = TeamPresetService::buildOptionListsPivots($request->get('optionListIds', []));

        $teamPreset->musicListsMusics()->sync($optionLists);

        $teamPreset->save();

        return redirect()->back();
    }

    public function updateOutros(
        Team $team,
        TeamPreset $teamPreset,
        TeamPresetsOutrosRequest $request
    ): RedirectResponse {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('update', $teamPreset);

        $optionalOutros = $request->get('outros');

        $optionalOutroFilePaths = [];
        foreach ($optionalOutros['values'] ?? [] as $outroPosition => $outro) {
            $optionalOutroFilePaths[] = $request->input('outros.values.' . $outroPosition . '.file');
        }

        $finalOutros = $this->outroFileService->updateTeamPresetOutros(
            $teamPreset,
            $optionalOutros,
            $optionalOutroFilePaths,
            $request->get('forced-outro-paths', [])
        );

        $teamPreset->outros = $finalOutros;
        $teamPreset->save();

        return redirect()->back();
    }
}
