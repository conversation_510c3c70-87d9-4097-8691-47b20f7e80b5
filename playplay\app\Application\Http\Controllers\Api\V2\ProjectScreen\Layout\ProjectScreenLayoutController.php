<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\ProjectScreen\Layout;

use App\Application\Events\ProjectScreenUpdated;
use App\Application\Events\ProjectSilentlyUpdated;
use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Project\ProjectScreen\Layout\MoveLayoutRequest;
use App\Application\Http\Requests\Api\Project\ProjectScreen\Layout\ProjectScreenLayoutRequest;
use App\Domain\ProjectScreen\Layout\ProjectScreenLayoutService;
use App\Models\Project;
use App\Models\ProjectScreen;
use App\Models\ScreenLayout;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

final class ProjectScreenLayoutController extends BaseController
{
    use AuthorizesRequests;

    private ProjectScreenLayoutService $projectScreenLayoutService;
    private Dispatcher $eventDispatcher;

    public function __construct(ProjectScreenLayoutService $service, Dispatcher $eventDispatcher)
    {
        $this->projectScreenLayoutService = $service;
        $this->eventDispatcher = $eventDispatcher;
    }

    /**
     * @throws AuthorizationException
     * @throws ValidationException
     */
    public function store(
        ProjectScreenLayoutRequest $request,
        Project $project,
        ProjectScreen $projectScreen,
        ScreenLayout $layout
    ): JsonResponse {
        $this->authorize('view', [ProjectScreen::class, $projectScreen]);

        $nthLayout = $request->get('nth_layout');

        if ($nthLayout >= $layout->max) {
            throw ValidationException::withMessages(['Reach max layout']);
        }

        $this->projectScreenLayoutService->add($projectScreen, $layout->id, $nthLayout);

        return $this->sendJsonResponse(
            new Collection([$projectScreen]),
            Response::HTTP_CREATED
        );
    }

    /**
     * @throws AuthorizationException
     * @throws ValidationException
     */
    public function destroy(
        ProjectScreenLayoutRequest $request,
        Project $project,
        ProjectScreen $projectScreen,
        ScreenLayout $layout
    ): JsonResponse {
        $this->authorize('view', [ProjectScreen::class, $projectScreen]);

        $nthLayout = (int) $request->input('nth_layout');
        if ($nthLayout >= $layout->max) {
            throw ValidationException::withMessages(['Layout index is above the limit']);
        }

        $this->projectScreenLayoutService->delete($projectScreen, $layout->id, $nthLayout);

        $this->eventDispatcher->dispatch(new ProjectScreenUpdated($projectScreen));
        $this->eventDispatcher->dispatch(new ProjectSilentlyUpdated($project->id));

        return $this->sendJsonResponse(new Collection([$projectScreen]), Response::HTTP_OK);
    }

    public function move(
        MoveLayoutRequest $request,
        Project $project,
        ProjectScreen $projectScreen,
        ScreenLayout $layout
    ): JsonResponse {
        $this->authorize('view', [ProjectScreen::class, $projectScreen]);

        $newNthLayout = (int) $request->input('new_nth_layout');
        $nthLayout = (int) $request->input('nth_layout');
        if ($newNthLayout >= $layout->max) {
            throw ValidationException::withMessages(['Layout index is above the limit']);
        }

        $this->projectScreenLayoutService->move($projectScreen, $layout->id, $nthLayout, $newNthLayout);

        $this->eventDispatcher->dispatch(new ProjectScreenUpdated($projectScreen));
        $this->eventDispatcher->dispatch(new ProjectSilentlyUpdated($project->id));

        return $this->sendJsonResponse(new Collection([$projectScreen]), Response::HTTP_OK);
    }
}
