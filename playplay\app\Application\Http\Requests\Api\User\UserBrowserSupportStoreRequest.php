<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\User;

use Illuminate\Foundation\Http\FormRequest;

class UserBrowserSupportStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'rendering_mode' => ['required', 'in:canvas,webgl1,webgl2'],
            'user_agent' => ['required', 'string'],
        ];
    }
}
