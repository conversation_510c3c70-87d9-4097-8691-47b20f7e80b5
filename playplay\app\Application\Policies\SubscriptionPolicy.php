<?php

namespace App\Application\Policies;

use App\Domain\Billing\SubscriptionPolicy as DomainSubscriptionPolicy;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class SubscriptionPolicy extends DefaultPolicy
{
    use HandlesAuthorization;

    private DomainSubscriptionPolicy $domainSubscriptionPolicy;

    public function __construct(DomainSubscriptionPolicy $domainSubscriptionPolicy)
    {
        $this->domainSubscriptionPolicy = $domainSubscriptionPolicy;
    }

    public function create(User $authUser): bool
    {
        return $this->domainSubscriptionPolicy->create($authUser);
    }

    public function churn(User $authUser, Subscription $subscription): bool
    {
        return $this->domainSubscriptionPolicy->churn($authUser, $subscription);
    }
}
