<?php

declare(strict_types=1);

namespace App\Domain\Project\Duplicator;

use App\Models\Project;
use App\Models\RawMedia;

final class ProjectUploadedRawMediasDuplicator
{
    public function duplicate(Project $sourceProject, Project $destinationProject): void
    {
        // TODO: Replace with call from repository see  RawMediaRepository@getAllByProject()
        $sourceRawMedias = $sourceProject->getUploadedRawMedias();

        $destinationProject->rawMedias()->attach(
            $sourceRawMedias->pluck('id')->combine(
                $sourceRawMedias->map(
                    fn(RawMedia $rawMedia) => ['relation_type' => $rawMedia->pivot->relation_type]
                )
            )
        );
    }
}
