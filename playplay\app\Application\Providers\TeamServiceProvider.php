<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Team\TeamRepository;
use App\Domain\Template\Repositories\TeamTemplateRepository;
use App\Infrastructure\Team\Repositories\EloquentTeamRepository;
use App\Infrastructure\Template\Repositories\EloquentTeamTemplateRepository;
use Illuminate\Support\ServiceProvider;

class TeamServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(TeamRepository::class, EloquentTeamRepository::class);
        $this->app->bind(TeamTemplateRepository::class, EloquentTeamTemplateRepository::class);
    }
}
