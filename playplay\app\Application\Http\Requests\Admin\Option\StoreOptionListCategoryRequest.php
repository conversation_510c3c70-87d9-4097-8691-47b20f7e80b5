<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\Option;

use App\Models\Screen\Parameters\OptionListCategory;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreOptionListCategoryRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:45'],
            'backoffice_name' => ['required', 'string', 'max:45'],
            'is_option_required' => ['required', 'boolean'],
            'is_thumbnail_padded' => ['required', 'boolean'],
        ];
    }
}
