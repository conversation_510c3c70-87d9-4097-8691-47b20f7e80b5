<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\TeamPresets;

use App\Models\TeamPreset;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Factory;

final class TeamPresetLogosRequest extends FormRequest
{
    private const CUSTOM_POSITION = 'custom';
    private const STANDARD_POSITION = 'standard';
    private const POSITION_TYPES = [
        self::STANDARD_POSITION,
        self::CUSTOM_POSITION,
    ];

    public function __construct()
    {
        $validationFactory = app(Factory::class);
        $this->checkIsPositionDisplayValid($validationFactory);
        parent::__construct();
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function checkIsPositionDisplayValid(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'isPositionDisplayValid',
            function ($attribute, $value, $parameters, $validator) {
                // Position can be display only if position is not custom
                $positionValueAttribute = str_replace('display', 'default.value', $attribute);
                $positionDefaultValue = data_get($validator->getData(), $positionValueAttribute);

                return !(bool) $value || in_array($positionDefaultValue, TeamPreset::LOGOS_POSITIONS);
            }
        );
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            // logos
            'logos.*.canUpload' => ['required', 'boolean'],
            'logos.*.position.default.type' => ['in:' . implode(',', static::POSITION_TYPES)],
            'logos.*.position.display' => ['required', 'boolean', 'isPositionDisplayValid'],
            'logos.*.position.default.value' => ['required', 'position'],
            'logos.*.logos.*.title' => ['sometimes', 'required', 'string'],
            'logos.*.logos.*.file' => ['sometimes', 'required', 'url'],
            'logos.*.logos.*.auto_resize' => ['required', 'boolean'],
            'logos.*.logos.*.square.width' => ['required_if:logos.*.logos.*.auto_resize,0', 'integer'],
            'logos.*.logos.*.square.height' => ['required_if:logos.*.logos.*.auto_resize,0', 'integer'],
            'logos.*.logos.*.vertical.width' => ['required_if:logos.*.logos.*.auto_resize,0', 'integer'],
            'logos.*.logos.*.vertical.height' => ['required_if:logos.*.logos.*.auto_resize,0', 'integer'],
            'logos.*.logos.*.horizontal.width' => ['required_if:logos.*.logos.*.auto_resize,0', 'integer'],
            'logos.*.logos.*.horizontal.height' => ['required_if:logos.*.logos.*.auto_resize,0', 'integer'],
            'logos.*.logos.*.square.project_media_id' => [
                'sometimes',
                'required_without:logos.*.logos.*.file',
                'exists:processed_medias,id',
            ],
            'logos.*.logos.*.horizontal.project_media_id' => [
                'sometimes',
                'required_without:logos.*.logos.*.file',
                'exists:processed_medias,id',
            ],
            'logos.*.logos.*.vertical.project_media_id' => [
                'sometimes',
                'required_without:logos.*.logos.*.file',
                'exists:processed_medias,id',
            ],
            'logos.secondary.display' => ['required', 'boolean'],
        ];

        return $rules;
    }
}
