<?php

namespace App\Domain\Logs\ModelHistory;

use Illuminate\Contracts\Events\Dispatcher;
use Throwable;

class ModelHistoryService
{
    /** @var ModelHistory[] */
    private array $modelHistories = [];

    private ModelHistoryDispatcher $modelHistoryDispatcher;

    private Dispatcher $eventDispatcher;

    public function __construct(
        ModelHistoryDispatcher $modelHistoryDispatcher,
        Dispatcher $eventDispatcher
    ) {
        $this->modelHistoryDispatcher = $modelHistoryDispatcher;
        $this->eventDispatcher = $eventDispatcher;
    }

    public function addModelHistory(ModelHistory $modelHistory): void
    {
        // Important: In here we are overriding the latest log/history of a specific model, that way we'll have
        // per model only the latest modification. Be wary of models that don't have IDs as they will all
        // be stored in the same table (as of the time being we don't have any loggable models without IDs).
        $this->modelHistories[$modelHistory->id()] = $modelHistory;
    }

    public function sendLogs(): void
    {
        foreach ($this->modelHistories as $modelHistory) {
            try {
                $this->modelHistoryDispatcher->sendMessage($modelHistory);
            } catch (Throwable $e) {
                $this->eventDispatcher->dispatch(
                    new UnableToLogModelHistoryException($e->getMessage(), $e->getCode())
                );
            }
        }
    }
}
