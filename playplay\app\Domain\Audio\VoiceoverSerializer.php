<?php

declare(strict_types=1);

namespace App\Domain\Audio;

use App\Domain\Time\Duration;
use App\Models\ProcessedMedia;

class VoiceoverSerializer
{
    /**
     * @param ProcessedMedia[] $processedMedias
     */
    public function serialize(array $processedMedias): array
    {
        return array_map(
            static function (ProcessedMedia $processedMedia) {
                $data = $processedMedia->data ?? [];

                $trimStart = $data['trimStart'] ?? null;
                $trimEnd = $data['trimEnd'] ?? null;
                $start = $data['start'] ?? 0;
                $duration = $processedMedia->duration ?? 0.0;

                if ($trimStart !== null && $trimEnd !== null) {
                    $duration = $trimEnd - $trimStart;
                }

                $lastRender = $processedMedia->lastRender;

                return [
                    'start' => (new Duration($start))->toMilliseconds()->getValue(),
                    'end' => (new Duration($start + $duration))->toMilliseconds()->getValue(),
                    'duration' => (new Duration($duration))->toMilliseconds()->getValue(),
                    'url' => $lastRender->thumbnail_url ?? null,
                    'render_media_id' => $lastRender->id ?? null,
                ];
            },
            $processedMedias
        );
    }
}
