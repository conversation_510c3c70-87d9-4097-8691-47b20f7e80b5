<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Template;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\Template\StoreOrUpdateSubCategoryRequest;
use App\Domain\Template\Repositories\CategoryRepository;
use App\Domain\Template\Repositories\SubCategoryRepository;
use App\Domain\Template\Repositories\TemplateRepository;
use App\Domain\Template\Services\SubCategoryService;
use App\Models\SubCategory;
use App\Models\Template;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;

final class SubCategoryController extends BaseController
{
    private CategoryRepository $categoryRepository;
    private SubCategoryRepository $subCategoryRepository;
    private TemplateRepository $templateRepository;
    private SubCategoryService $subCategoryService;
    private Factory $viewFactory;

    public function __construct(
        CategoryRepository $categoryRepository,
        SubCategoryRepository $subCategoryRepository,
        SubCategoryService $subCategoryService,
        TemplateRepository $templateRepository,
        Factory $viewFactory,
    ) {
        $this->authorizeResource(SubCategory::class, SubCategory::class);
        $this->categoryRepository = $categoryRepository;
        $this->subCategoryRepository = $subCategoryRepository;
        $this->subCategoryService = $subCategoryService;
        $this->templateRepository = $templateRepository;
        $this->viewFactory = $viewFactory;
    }

    public function card(Template $template): View
    {
        return $this->viewFactory->make('admin.templates.sub-categories.template-card', ['template' => $template]);
    }

    public function destroy(SubCategory $subCategory): JsonResponse
    {
        $this->subCategoryService->deleteSubCategoryAndReorderCategories($subCategory);

        return new JsonResponse([
            'success' => true,
            'redirect' => route('admin.templateSubCategories.index'),
        ]);
    }

    public function index(): View
    {
        return $this->viewFactory->make(
            'admin.templates.sub-categories.index',
            [
                'subCategories' => $this->subCategoryRepository->getPaginatedSubCategoriesOrderedByUpdatedAtAndId(),
                'tabActive' => 'subCategories',
            ]
        );
    }

    public function create(): View
    {
        return $this->viewFactory->make('admin.templates.sub-categories.create', [
            'subCategory' => new SubCategory(),
            'categories' =>
                $this->categoryRepository->getAll()->pluck('backoffice_name', 'id')->toArray(),
            'templates' => $this->templateRepository->getAllV3Templates(),
        ]);
    }

    public function store(StoreOrUpdateSubCategoryRequest $request): RedirectResponse
    {
        $subCategory = $this->subCategoryRepository
            ->createFromNames($request->input('name'), $request->input('backoffice_name'));
        $this->subCategoryRepository->updateTemplatesOfASubCategory($subCategory, $request->get('templates_ids', []));
        $this->subCategoryService->updateCategories($subCategory, $request->get('categories_ids', []));

        return redirect()->route('admin.templateSubCategories.index');
    }

    public function edit(SubCategory $subCategory): View
    {
        return $this->viewFactory->make('admin.templates.sub-categories.edit', [
            'categories' =>
                $this->categoryRepository->getAll()->pluck('backoffice_name', 'id')->toArray(),
            'subCategory' => $subCategory,
            'templates' => $this->templateRepository->getAllV3Templates(),
        ]);
    }

    public function showDangerZone(SubCategory $subCategory): View
    {
        return $this->viewFactory->make('admin.templates.sub-categories.danger-zone', [
            'subCategory' => $subCategory,
        ]);
    }

    public function update(SubCategory $subCategory, StoreOrUpdateSubCategoryRequest $request): RedirectResponse
    {
        $subCategory->update($request->only(['name', 'backoffice_name']));

        $this->subCategoryRepository->updateTemplatesOfASubCategory($subCategory, $request->get('templates_ids', []));
        $this->subCategoryService->updateCategories($subCategory, $request->get('categories_ids', []));

        return redirect()->route('admin.templateSubCategories.index');
    }
}
