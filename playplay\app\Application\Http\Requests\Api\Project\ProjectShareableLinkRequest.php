<?php

namespace App\Application\Http\Requests\Api\Project;

use Illuminate\Foundation\Http\FormRequest;

class ProjectShareableLinkRequest extends FormRequest
{

    public static function getRules()
    {
        return [
            'has_comments' => ['required', 'boolean'],
            'can_download' => ['sometimes', 'boolean'],
            'has_password' => ['sometimes', 'boolean'],
            'password'     => ['sometimes', 'string', 'nullable', 'max:255'],
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return static::getRules();
    }
}
