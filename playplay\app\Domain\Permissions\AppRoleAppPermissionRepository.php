<?php

declare(strict_types=1);

namespace App\Domain\Permissions;

use App\Models\Permissions\AppPermission;
use App\Models\Permissions\AppRole;
use Illuminate\Support\Collection;

interface AppRoleAppPermissionRepository
{
    /**
     * @param AppRole $appRole
     *
     * @return Collection<AppPermission>
     */
    public function getEnabledByDefaultAppPermissionsFor(AppRole $appRole): Collection;
}
