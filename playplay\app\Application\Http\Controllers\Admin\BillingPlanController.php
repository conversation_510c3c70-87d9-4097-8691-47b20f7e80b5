<?php

namespace App\Application\Http\Controllers\Admin;

use App\Application\Http\Requests\Admin\BillingPlan\BillingPlanStoreRequest;
use App\Application\Http\Requests\Admin\BillingPlan\BillingPlanUpdateRequest;
use App\Models\BillingPlan;
use App\Models\Feature;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;

class BillingPlanController extends BaseController
{
    private Factory $viewFactory;

    public function __construct(Factory $viewFactory)
    {
        $this->authorizeResource(BillingPlan::class, BillingPlan::class);
        $this->viewFactory = $viewFactory;
    }

    public function create(): View
    {
        return $this->viewFactory->make('admin.billing-plans.create');
    }

    public function index(): View
    {
        $billingPlans = BillingPlan::all();

        return $this->viewFactory->make('admin.billing-plans.index', [
            'billingPlans' => $billingPlans,
        ]);
    }

    public function show(BillingPlan $billingPlan): View
    {
        $features = Feature::for('company')->get();

        return $this->viewFactory->make('admin.billing-plans.show', [
            'billingPlan' => $billingPlan,
            'features' => $features,
        ]);
    }

    public function store(BillingPlanStoreRequest $request): JsonResponse
    {
        $billingPlan = BillingPlan::create($request->all());
        Feature::for('company')->each(function (Feature $feature) use ($billingPlan) {
            $billingPlan->features()->attach($feature->id, ['value' => $feature->default]);
        });
        $redirectUri = route('admin.billing-plans.show', $billingPlan->id);

        return new JsonResponse(
            ['success' => true, 'redirect_uri' => $redirectUri],
            JsonResponse::HTTP_CREATED
        );
    }

    public function update(BillingPlanUpdateRequest $request, BillingPlan $billingPlan): RedirectResponse
    {
        $billingPlan->update($request->all());

        $features = [];
        foreach ($request->get('features', []) as $featureId => $featureValue) {
            if ($featureValue !== ''  && $featureValue !== null) {
                $features[$featureId] = ['value' => (int) $featureValue];
            }
        }

        $billingPlan->features()->sync($features);

        return redirect()->route('admin.billing-plans.show', [
            'billing_plan' => $billingPlan
        ]);
    }
}
