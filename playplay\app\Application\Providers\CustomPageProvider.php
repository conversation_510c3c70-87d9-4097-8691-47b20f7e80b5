<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Common\StringSluggifierInterface;
use App\Domain\CustomPage\CustomPageRepository;
use App\Domain\CustomPage\CustomPageTokenGeneratorInterface;
use App\Domain\CustomPage\DefaultColorsRepository;
use App\Domain\CustomPage\DefaultFontRepository;
use App\Infrastructure\Common\StringSluggifier;
use App\Infrastructure\CustomPage\ConfigDefaultColorsRepository;
use App\Infrastructure\CustomPage\EloquentCustomPageRepository;
use App\Infrastructure\CustomPage\FreeTrialDefaultFontRepository;
use App\Infrastructure\CustomPage\UuidCustomPageTokenGenerator;
use Illuminate\Support\ServiceProvider;

final class CustomPageProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(CustomPageRepository::class, EloquentCustomPageRepository::class);
        $this->app->bind(CustomPageTokenGeneratorInterface::class, UuidCustomPageTokenGenerator::class);
        $this->app->bind(StringSluggifierInterface::class, StringSluggifier::class);
        $this->app->bind(DefaultColorsRepository::class, ConfigDefaultColorsRepository::class);
        $this->app->bind(DefaultFontRepository::class, FreeTrialDefaultFontRepository::class);
    }
}
