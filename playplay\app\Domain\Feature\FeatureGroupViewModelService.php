<?php

declare(strict_types=1);

namespace App\Domain\Feature;

use App\Domain\Plan\PlanRepository;
use App\Domain\Feature\Repositories\FeatureGroupRepository;

class FeatureGroupViewModelService
{
    private PlanRepository $planRepository;

    private FeatureGroupRepository $featureGroupRepository;

    private FeatureGroupViewModelCollectionFactory $collectionFactory;

    public function __construct(
        PlanRepository $planRepository,
        FeatureGroupRepository $featureGroupRepository,
        FeatureGroupViewModelCollectionFactory $collectionFactory
    ) {
        $this->planRepository = $planRepository;
        $this->featureGroupRepository = $featureGroupRepository;
        $this->collectionFactory = $collectionFactory;
    }

    public function getFeatureGroupViewModel(
        HasFeatures $baseModel
    ): FeatureGroupViewModelCollection {
        $plans = $this->planRepository->getAllPlansWithFeatures();

        $featureGroups = $this->featureGroupRepository->getOrderedFeatureGroupsWithFeatures();

        return $this->collectionFactory->createFeatureGroupViewModelCollection($baseModel, $featureGroups, $plans);
    }
}
