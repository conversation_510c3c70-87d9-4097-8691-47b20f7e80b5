<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\TimecodedElement;

use Illuminate\Foundation\Http\FormRequest;

final class TimecodedElementsFamilyIndexRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'page' => ['sometimes', 'integer', 'min:1'],
            'type' => ['bail', 'sometimes', 'string', 'isValidTimecodedElementsFamilyType'],
        ];
    }
}
