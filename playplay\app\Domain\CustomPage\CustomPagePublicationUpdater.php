<?php

declare(strict_types=1);

namespace App\Domain\CustomPage;

use App\Domain\User\Action\UserAction;
use App\Domain\User\Action\UserActionService;
use App\Models\CustomPage;

final class CustomPagePublicationUpdater
{
    private CustomPageRepository $customPageRepository;

    private UserActionService $userActionService;

    public function __construct(
        CustomPageRepository $customPageRepository,
        UserActionService $userActionService,
    ) {
        $this->customPageRepository = $customPageRepository;
        $this->userActionService = $userActionService;
    }

    public function publish(CustomPage $customPage): void
    {
        if ($customPage->published === true) {
            return;
        }

        $this->customPageRepository->publish($customPage);
        $this->userActionService->addUserAction(
            new UserAction(
                'custom-page-published',
                [],
                $customPage->project->team_id,
                $customPage->project_id,
            )
        );
    }

    public function unpublish(CustomPage $customPage): void
    {
        if ($customPage->published === false) {
            return;
        }

        $this->customPageRepository->unpublish($customPage);
        $this->userActionService->addUserAction(
            new UserAction(
                'custom-page-unpublished',
                [],
                $customPage->project->team_id,
                $customPage->project_id,
            )
        );
    }
}
