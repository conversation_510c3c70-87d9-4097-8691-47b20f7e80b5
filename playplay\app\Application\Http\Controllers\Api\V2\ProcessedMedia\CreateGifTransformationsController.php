<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\ProcessedMedia;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\ProcessedMedia\Store\ProcessedMediaGifTransformationsStoreRequest;
use App\Infrastructure\ProcessedMedia\Transformations\TransformationsFactory;
use App\Services\ProcessedMedia\AccessChecker;
use App\Services\ProcessedMedia\Factory;
use App\Services\Processing\RenderMedia\GifService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;

final class CreateGifTransformationsController extends BaseController
{
    use AuthorizesRequests;

    private Factory $processedMediaFactory;
    private AccessChecker $accessChecker;
    private GifService $gifService;
    private TransformationsFactory $transformationsFactory;

    public function __construct(
        AccessChecker $accessChecker,
        Factory $processedMediaFactory,
        GifService $gifService,
        TransformationsFactory $transformationsFactory
    ) {
        $this->accessChecker = $accessChecker;
        $this->processedMediaFactory = $processedMediaFactory;
        $this->gifService = $gifService;
        $this->transformationsFactory = $transformationsFactory;
    }

    public function __invoke(ProcessedMediaGifTransformationsStoreRequest $request): JsonResponse
    {
        $rawMediaId = $request->get('raw_media_id');
        $source = $request->get('source');
        $projectId = $request->get('project_id');

        $this->accessChecker->checkIfCanCreateProcessedMediaFromRawMediaAndSourceAndProject(
            $rawMediaId,
            $source,
            $projectId
        );

        $processedMedia = $this->processedMediaFactory->create(
            $rawMediaId,
            $source,
            $projectId
        );

        $transformations = $this->transformationsFactory->create(
            $projectId,
            $request->get('param_id'),
            $request->get('crop'),
            $request->get('keepSize')
        );

        $this->gifService->applyTransformations($processedMedia, $transformations);

        return $this->sendJsonResponse(new Collection([$processedMedia]), Response::HTTP_CREATED);
    }
}
