<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\User;

use Illuminate\Foundation\Http\FormRequest;

class UserStoreRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'first_name' => ['required', 'max:50', 'name'],
            'last_name' => ['required', 'max:50', 'name'],
            'email' => ['required', 'email', 'uniqueUserActive'],
            'teams.*.id' => ['required', 'exists:teams,id'],
            'teams.*.app_role_id' => ['required', 'exists:app_roles,id'],
        ];
    }
}
