<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\V2\RenderProject\DownloadRenderProjectHtmlController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class DownloadProjectRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::get('/projects/{project}/download', DownloadRenderProjectHtmlController::class)
            ->name('projects.download');
    }
}
