<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\ProcessedMedia;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Project\ProcessedMedia\Store\ProcessedMediaImageCropStoreRequest;
use App\Domain\Workflow\Config\MediaWorkflow;
use App\Domain\Render\RenderMedia\Crop\CropFactory;
use App\Services\ProcessedMedia\AccessChecker;
use App\Services\ProcessedMedia\Factory;
use App\Services\ProcessedMedia\FilterApplier;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;

final class CreateImageCropController extends BaseController
{
    use AuthorizesRequests;

    private Factory $processedMediaFactory;
    private FilterApplier $filterApplier;
    private AccessChecker $accessChecker;
    private MediaWorkflow $mediaWorkflow;

    public function __construct(
        AccessChecker $accessChecker,
        Factory $processedMediaFactory,
        FilterApplier $filterApplier,
        MediaWorkflow $mediaWorkflow
    ) {
        $this->accessChecker = $accessChecker;
        $this->processedMediaFactory = $processedMediaFactory;
        $this->filterApplier = $filterApplier;
        $this->mediaWorkflow = $mediaWorkflow;
    }

    public function __invoke(ProcessedMediaImageCropStoreRequest $request): JsonResponse
    {
        $rawMediaId = $request->get('raw_media_id');
        $source = $request->get('source');
        $projectId = $request->get('project_id');

        $this->accessChecker->checkIfCanCreateProcessedMediaFromRawMediaAndSourceAndProject(
            $rawMediaId,
            $source,
            $projectId
        );

        $processedMedia = $this->processedMediaFactory->create(
            $rawMediaId,
            $source,
            $projectId
        );

        $this->mediaWorkflow->start($processedMedia->rawMedia);

        $this->filterApplier->cropImageBasedOnParam(
            $processedMedia,
            $request->get('param_id'),
            CropFactory::createCropFromArray($request->get('crop') ?? [])
        );

        return $this->sendJsonResponse(new Collection([$processedMedia]), \Illuminate\Http\Response::HTTP_CREATED);
    }
}
