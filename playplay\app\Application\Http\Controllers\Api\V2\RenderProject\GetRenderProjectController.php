<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\RenderProject;

use App\Application\Http\Controllers\Api\BaseController;
use App\Models\Project;
use App\Models\Renders\RenderProjectHtml;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;

final class GetRenderProjectController extends BaseController
{
    public function __construct()
    {
        $this->middleware('can:view,project');
    }

    public function __invoke(Project $project, RenderProjectHtml $render): JsonResponse
    {
        if ($render->project_id !== $project->id) {
            throw new AccessDeniedHttpException('This render doesn\'t belong to this project');
        }

        return $this->sendJsonResponse(new Collection([$render]), Response::HTTP_OK);
    }
}
