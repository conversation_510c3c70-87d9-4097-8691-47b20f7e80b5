<?php

declare(strict_types=1);

namespace App\Domain\Company\Repositories;

use App\Models\Company;
use App\Models\CompanyHistory;
use DateTimeInterface;
use Illuminate\Support\Collection;

interface CompanyHistoryRepository
{
    public function create(Company $company): void;

    /**
     * @param Company[] $companies
     */
    public function createMany(array $companies): void;

    public function ends(CompanyHistory $history, DateTimeInterface $endsAt): void;

    public function endMany(array $historiesIds, DateTimeInterface $endsAt): void;

    public function getAll(Company $company): Collection;

    public function getLast(Company $company): ?CompanyHistory;

    public function getLastFromMany(Collection $companies): Collection;
}
