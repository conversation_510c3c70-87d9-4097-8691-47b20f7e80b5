<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\Project\ProjectScreen\Param\CutawayShot;

use Illuminate\Validation\Rule;

class StoreCutawayShotImageRequest extends CutawayShotRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'raw_media_id' => ['required', 'isImage'],
            'source' => ['required', Rule::in($this->getAllowedSources())],
            'crop' => ['required', 'crop'],
            'keep_size' => ['required', 'boolean'],
            'start' => ['required', 'numeric', 'min:0'],
            'duration' => ['required', 'numeric', 'isValidCutawayShotItemDuration'],
        ];
    }
}
