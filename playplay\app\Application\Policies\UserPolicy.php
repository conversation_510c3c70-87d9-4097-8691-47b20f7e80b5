<?php

namespace App\Application\Policies;

use App\Domain\UserTeam\UserTeamAppPermissionService;
use App\Domain\UserTeam\UserTeamService;
use App\Models\Permissions\AppPermission;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Support\Collection;

class UserPolicy extends DefaultPolicy
{
    use HandlesAuthorization;

    private UserTeamAppPermissionService $userTeamAppPermissionService;
    private UserTeamService $userTeamService;

    public function __construct(
        UserTeamAppPermissionService $userTeamAppPermissionService,
        UserTeamService $userTeamService
    ) {
        $this->userTeamAppPermissionService = $userTeamAppPermissionService;
        $this->userTeamService = $userTeamService;
    }

    /** @todo Add missing tests. */
    public function before(User $authUser, $ability, $user = null)
    {
        /** check backoffice permission */
        if ($authUser->can('manage-users')) {
            return true;
        }

        if (in_array($ability, ['view', 'update'])) {
            return $authUser->is($user);
        }
    }

    public function create(User $authUser, Collection $teamIdsWithAppRoles): bool
    {
        $teamIds = array_column($teamIdsWithAppRoles->toArray(), 'team_id');

        $hasAppPermissionToCreateVideos = $this->userTeamAppPermissionService
            ->userHasAppPermissionInTeams($authUser, $teamIds, AppPermission::CAN_MANAGE_USERS);

        if ($hasAppPermissionToCreateVideos === false) {
            return false;
        }

        return $this->userTeamService->canAddUserWithAppRoleToAllTeams($teamIdsWithAppRoles);
    }

    public function destroy(User $authUser, User $userToDelete): bool
    {
        $userTeamsId = $userToDelete->teams->pluck('id')->toArray();

        return $this->userTeamAppPermissionService
            ->userHasAppPermissionInTeams($authUser, $userTeamsId, AppPermission::CAN_MANAGE_USERS);
    }

    /** @todo Add missing tests. */
    public function viewGetty(User $authUser, User $user, bool $isEdito): bool
    {
        if ($isEdito) {
            return $user->company->hasGettyFeature() && $user->company->hasGettyEditoFeature();
        }

        return $user->company->hasGettyFeature();
    }
}
