<?php

declare(strict_types=1);

namespace App\Application\Html;

use Collective\Html\FormBuilder;
use Collective\Html\HtmlBuilder;
use Illuminate\Contracts\Config\Repository as Config;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Contracts\Translation\Translator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use InvalidArgumentException;
use Watson\BootstrapForm\BootstrapForm;

final class AdminForm extends BootstrapForm
{
    private const BUTTON_TYPES = [
        'edit' => ['class' => 'btn-warning', 'fa-icon' => 'edit'],
        'sign-in-as' => ['class' => 'btn-success', 'fa-icon' => 'user-secret'],
        'join-team' => ['class' => 'btn-primary', 'fa-icon' => 'users'],
        'delete' => ['class' => 'btn-danger', 'fa-icon' => 'times'],
        'deactivate' => ['class' => 'btn-danger', 'fa-icon' => 'times'],
        'reset-password' => ['class' => 'btn-primary', 'fa-icon' => 'envelope'],
        'projects' => ['class' => 'btn-info', 'fa-icon' => 'film'],
        'view' => ['class' => 'btn-info', 'fa-icon' => 'eye'],
        'error' => ['class' => 'btn-danger', 'fa-icon' => 'exclamation-triangle'],
        'duplicate' => ['class' => 'btn-default', 'fa-icon' => 'copy'],
        'reset-features' => ['class' => 'btn-default', 'fa-icon' => 'refresh'],
        'show-render' => ['class' => '', 'fa-icon' => ''],
        'show-last-render' => ['class' => '', 'fa-icon' => ''],
    ];

    private Translator $translator;

    public function __construct(
        HtmlBuilder $html,
        FormBuilder $form,
        Config $config,
        Translator $translator,
    ) {
        parent::__construct($html, $form, $config);

        $this->translator = $translator;
    }

    public function actionLinkWithTooltip(
        string $type,
        array $options,
        ?string $faIcon = null,
        string $message = ''
    ): string {
        $content = $this->generateButtonContent($type, $faIcon, $message);
        $options = $this->generateButtonOptions($type, $options);

        return $this->html->link($options['href'] ?? null, $content, $options, null, false)->toHtml();
    }

    public function actionSubmitButtonWithTooltip(string $type, string $message = ''): string
    {
        $content = $this->generateButtonContent($type, null, $message);
        $options = $this->generateButtonOptions($type, ['type' => 'submit']);

        /** @var Htmlable $button */
        $button = $this->button($content, $options);

        return strip_tags($button->toHtml(), ['button', 'i']);
    }

    public function input($type, $name, $label = null, $value = null, array $options = []): string
    {
        $options = $this->getFieldOptions($options, $name);
        $inputElement = $type === 'password'
            ? $this->form->password($name, $options)
            : $this->form->{$type}(
                $name,
                $value,
                $options
            );

        $wrapperOptions = $this->isHorizontal() ? ['class' => $this->getRightColumnClass()] : [];
        $wrapperElement = '<div'
            . $this->html->attributes($wrapperOptions)
            . '>'
            . $inputElement
            . $this->getFieldError($name)
            . $this->getHelpText($name, $options)
            . '</div>';

        /** @var Htmlable $formGroup */
        $formGroup = $this->getFormGroup($name, $this->getLabelTitle($label, $name), $wrapperElement);

        return $formGroup->toHtml();
    }

    public function select(
        $name,
        $label = null,
        $list = [],
        $selected = null,
        array $options = []
    ): HtmlString|string {
        $options = array_merge(['rel' => 'select2'], $options);

        return parent::select($name, $label, $list, $selected, $options);
    }

    public function tags(
        $name,
        $label = null,
        $list = [],
        $selected = null,
        array $options = [],
        $creating = false
    ): string {
        $options = array_merge(['rel' => 'taginput', 'multiple', 'data-tags' => var_export($creating, true)], $options);
        /** @var Htmlable $select */
        $select = parent::select($name, $label, $list, $selected, $options);

        return $select->toHtml();
    }

    public function radiosFromModelsCollection(
        string $name,
        Collection $choices,
        string $radioInputValueAttribute,
        string $radioInputLabelAttribute,
        $radioGroupLabel = null,
        $checkedValue = null,
        $inline = false,
        array $options = []
    ): string {
        /** @var Htmlable $radios */
        $radios = $this->radios(
            $name,
            $radioGroupLabel,
            $choices->pluck($radioInputValueAttribute, $radioInputLabelAttribute)->toArray(),
            $checkedValue,
            $inline,
            $options
        );

        return $radios->toHtml();
    }

    /**
     * @param string|bool|null $label
     */
    protected function getLabelTitle($label, $name): ?string
    {
        if ($label === false) {
            return null;
        }

        if ($label === null && $this->translator->has("validation.attributes.{$name}")) {
            return $this->translator->get("validation.attributes.{$name}");
        }

        return $label ?: str_replace('_', ' ', Str::title($name));
    }

    /**
     * Generate options for tooltip buttons/links
     *
     * @throws InvalidArgumentException
     */
    private function generateButtonOptions(string $type, array $options): array
    {
        if (!array_key_exists($type, self::BUTTON_TYPES)) {
            throw new InvalidArgumentException('Invalid button type.');
        }

        $title = Lang::get('message.action_tooltips.' . $type);
        $buttonClasses = self::BUTTON_TYPES[$type]['class'] . ' btn btn-xs action-btn ' . ($options['class'] ?? '');

        return array_merge(
            $options,
            ['class' => $buttonClasses, 'title' => $title, 'data-toggle' => 'tooltip', 'data-placement' => 'top']
        );
    }

    private function generateButtonContent(string $type, ?string $faIcon, string $message): string
    {
        if ($message !== '') {
            return $message;
        }

        return '<i class="fa fa-' . ($faIcon ?? self::BUTTON_TYPES[$type]['fa-icon']) . '"></i>';
    }
}
