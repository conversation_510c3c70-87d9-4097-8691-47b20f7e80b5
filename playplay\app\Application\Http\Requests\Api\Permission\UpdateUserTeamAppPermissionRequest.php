<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\Permission;

use Illuminate\Foundation\Http\FormRequest;

final class UpdateUserTeamAppPermissionRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'app_permission_ids' => 'required|array',
            'app_permission_ids.*' => 'required|integer|exists:app_permissions,id',
        ];
    }
}
