<?php

declare(strict_types=1);

namespace App\Domain\CutawayShot\Mappers;

use App\Domain\CutawayShot\CutawayShotImageRenderMediaData;
use App\Domain\Render\RenderMedia\LegacyRenderMediaDataInterface;
use App\Domain\Render\RenderMedia\TargetDimension;
use Exception;
use TypeError;

final class CutawayShotImageRenderMediaDataMapper
{
    public function fromLegacyRenderMediaData(?LegacyRenderMediaDataInterface $data): CutawayShotImageRenderMediaData
    {
        if ($data === null) {
            throw new InvalidCutawayShotRenderMediaDataMappingException();
        }

        try {
            return new CutawayShotImageRenderMediaData(
                $data->getCrop(),
                $data->getDuration(),
                $data->getLegacyKeepSize(),
                $data->getStart(),
                new TargetDimension(
                    $data->getMaxHeight(),
                    $data->getMaxWidth(),
                ),
            );
        } catch (TypeError | Exception $e) {
            throw new InvalidCutawayShotRenderMediaDataMappingException($e);
        }
    }
}
