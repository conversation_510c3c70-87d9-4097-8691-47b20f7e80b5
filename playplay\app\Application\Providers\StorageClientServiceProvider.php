<?php

namespace App\Application\Providers;

use Google\Cloud\Storage\StorageClient;
use Illuminate\Support\ServiceProvider;

class StorageClientServiceProvider extends ServiceProvider
{
    public function boot()
    {
        $this->app->singleton(StorageClient::class, function () {
            return new StorageClient([
                'projectId' => config('filesystems.disks.gcs.project_id'),
                'keyFilePath' => config('filesystems.disks.gcs.key_file'),
                'retries' => config('filesystems.disks.gcs.retries'),
                'restCalcDelayFunction' => 'custom_calculate_delay',
            ]);
        });
    }
}
