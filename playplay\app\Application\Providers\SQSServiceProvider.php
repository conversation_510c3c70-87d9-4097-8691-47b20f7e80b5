<?php

namespace App\Application\Providers;

use App\Domain\Processing\ProcessingJobDispatcher;
use App\Domain\Queueing\QueueMessageService;
use App\Domain\Queueing\QueueMetadataService;
use App\Domain\RenderJob\RenderingJobDispatcher;
use App\Infrastructure\Processing\SqsProcessingJobDispatcher;
use App\Infrastructure\Queueing\SqsQueueMetadataService;
use App\Infrastructure\Queueing\SQSService;
use App\Infrastructure\RenderJob\SqsRenderingJobDispatcher;
use Aws\Sqs\SqsClient;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class SQSServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->singleton(SqsClient::class, function () {
            return new SqsClient([
                'version' => '2012-11-05',
                'region' => config('queue.connections.sqs.region'),
                'credentials' => [
                    'key' => config('queue.connections.sqs.key'),
                    'secret' => config('queue.connections.sqs.secret'),
                ],
            ]);
        });
        $this->app->singleton(QueueMessageService::class, SQSService::class);
        $this->app->singleton(RenderingJobDispatcher::class, SqsRenderingJobDispatcher::class);
        $this->app->singleton(ProcessingJobDispatcher::class, SqsProcessingJobDispatcher::class);
        $this->app->bind(QueueMetadataService::class, SqsQueueMetadataService::class);
    }
}
