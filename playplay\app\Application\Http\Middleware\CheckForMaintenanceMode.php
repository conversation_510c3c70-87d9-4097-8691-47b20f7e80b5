<?php

declare(strict_types=1);

namespace App\Application\Http\Middleware;

use App\Domain\Maintenance\MaintenanceRepository;
use App\Models\User;
use Closure;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Http\Request;

final class CheckForMaintenanceMode
{
    private MaintenanceRepository $repository;
    private Guard $guard;

    public function __construct(MaintenanceRepository $repository, Guard $guard)
    {
        $this->repository = $repository;
        $this->guard = $guard;
    }

    public function handle(Request $request, Closure $next)
    {
        $maintenance = $this->repository->getCurrent();

        if ($maintenance !== null) {
            /** @var User $user */
            $user = $this->guard->user();
            if ($maintenance->isUserAllowedToAccessPlateform($user)) {
                return $next($request);
            }

            if ($request->ajax()) {
                return response()->json([
                    'message' => $maintenance->message,
                ], 503);
            }

            return response()->view('errors.503', compact('maintenance'), 503);
        }

        return $next($request);
    }
}
