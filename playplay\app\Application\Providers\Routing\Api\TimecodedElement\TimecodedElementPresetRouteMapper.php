<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\TimecodedElement;

use App\Application\Http\Controllers\Api\V2\TimecodedElement\TimecodedElementPresetController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class TimecodedElementPresetRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::get(
            '/teams/{team}/timecoded-element-presets',
            [TimecodedElementPresetController::class, 'index']
        )->name('teams.timecoded-element-presets.index');
    }
}
