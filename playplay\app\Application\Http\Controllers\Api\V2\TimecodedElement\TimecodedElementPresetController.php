<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\TimecodedElement;

use App\Application\Http\Controllers\Api\BaseController;
use App\Domain\TimecodedElement\Serializers\TimecodedElementPresetsSerializer;
use App\Domain\TimecodedElement\TimecodedElementPresetService;
use App\Models\Team;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class TimecodedElementPresetController extends BaseController
{
    use AuthorizesRequests;

    private TimecodedElementPresetService $timecodedElementPresetService;
    private TimecodedElementPresetsSerializer $timecodedElementPresetsSerializer;

    public function __construct(
        TimecodedElementPresetService $timecodedElementPresetService,
        TimecodedElementPresetsSerializer $timecodedElementPresetsSerializer
    ) {
        $this->timecodedElementPresetService = $timecodedElementPresetService;
        $this->timecodedElementPresetsSerializer = $timecodedElementPresetsSerializer;
    }

    /**
     * @throws AuthorizationException
     */
    public function index(Team $team): JsonResponse
    {
        $this->authorize('view', [Team::class, $team]);

        $timecodedElementPresets = $this->timecodedElementPresetService->getByTeamIdIndexedByFamilyType($team->id);

        return $this->sendJsonResponse(
            new Collection($this->timecodedElementPresetsSerializer->serialize($timecodedElementPresets)),
            Response::HTTP_OK
        );
    }
}
