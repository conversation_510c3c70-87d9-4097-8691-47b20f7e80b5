<?php

declare(strict_types=1);

namespace App\Application\Console\Commands\CustomAtScale;

use App\Domain\Screen\ScreenRepository;
use App\Domain\Team\TeamRepository;
use App\Domain\TeamScreen\CacheTeamScreenService;
use DateTimeImmutable;
use Illuminate\Console\Command;
use Psr\Log\LoggerInterface;

final class AddActivatedGenericScreensToTeams extends Command
{
    protected $signature = 'cas:add-activated-generic-screens-to-teams
                            {--D|date= : Screen activation date.}';

    protected $description = 'Add new generic screens to teams that have auto add enabled.';

    private ScreenRepository $screenRepository;
    private TeamRepository $teamRepository;
    private CacheTeamScreenService $cacheTeamScreenService;
    private LoggerInterface $logger;

    public function __construct(
        ScreenRepository $screenRepository,
        TeamRepository $teamRepository,
        CacheTeamScreenService $cacheTeamScreenService,
        LoggerInterface $logger
    ) {
        parent::__construct();

        $this->screenRepository = $screenRepository;
        $this->teamRepository = $teamRepository;
        $this->cacheTeamScreenService = $cacheTeamScreenService;
        $this->logger = $logger;
    }

    public function handle(): int
    {
        $screenActivatedAt = $this->option('date')
            ? new DateTimeImmutable($this->option('date'))
            : new DateTimeImmutable('yesterday');

        $this->info('--------------------------------------------------------------------------');
        $this->logAndDisplayOnConsole("Add new generic screens activated at {$screenActivatedAt->format('d-m-Y')}");
        $this->info('--------------------------------------------------------------------------');

        $screens = $this->screenRepository->getGenericScreensByActivationDate($screenActivatedAt);

        if ($screens->isEmpty()) {
            $this->logAndDisplayOnConsole("No generic screens to add.");

            return 0;
        }

        $this->logAndDisplayOnConsole("Screens to add {$screens->pluck('id')->join(', ')}");
        $this->teamRepository->linkGenericScreensToTeamsThatHaveAutoAddGenericScreensEnabled($screens);

        /**
         * @note We only need to update the cache for one screen because it's the same thing for other screens.
         */
        $this->cacheTeamScreenService->forgetCacheForAllTeamsHavingScreenId($screens->first()->id);

        return 0;
    }

    private function logAndDisplayOnConsole(string $message): void
    {
        $this->info($message);
        $this->logger->info($message);
    }
}
