<?php

namespace App\Application\Http\Requests\Admin\User;

use App\Models\Company;
use App\Models\Team;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Factory;

class UserSwitchProjectRequest extends FormRequest
{
    public function __construct(Factory $validationFactory)
    {
        $this->checkIsTeamInCompany($validationFactory);
        $this->checkIsUserInCompany($validationFactory);
        parent::__construct();
    }

    public function authorize()
    {
        return true;
    }

    public function checkIsTeamInCompany(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'team_in_company',
            function ($attribute, $value) {
                /** @var ?Team $team */
                $team = Team::query()->find($value);
                return $team?->company_id === (int) request()?->get('company_id');
            }
        );
    }

    public function checkIsUserInCompany(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'user_in_company',
            function ($attribute, $value) {
                return User::find($value)->company_id === (int) request()->get('company_id');
            }
        );
    }

    public function rules()
    {
        return [
            'company_id' => ['required', 'exists:companies,id'],
            'team_id' => ['required', 'team_in_company'],
            'user_id' => ['required', 'user_in_company'],
            'project_ids' => ['sometimes'],
            'transfer_all' => ['sometimes'],
        ];
    }
}
