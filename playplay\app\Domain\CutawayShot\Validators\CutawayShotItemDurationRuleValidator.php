<?php

namespace App\Domain\CutawayShot\Validators;

use App\Domain\Time\Duration;

final class CutawayShotItemDurationRuleValidator
{
    private const MIN_CUTAWAY_SHOT_DURATION_IN_MILLISECONDS = 1000;

    public function isValid(array $data): bool
    {
        return $this->getDurationInMilliseconds($data) >= self::MIN_CUTAWAY_SHOT_DURATION_IN_MILLISECONDS;
    }

    private function getDurationInMilliseconds(array $data): int
    {
        $duration = 0;

        if (array_key_exists('duration', $data)) {
            $duration = $data['duration'];
        } elseif (array_key_exists('trim_start', $data) && array_key_exists('trim_end', $data)) {
            $duration = $data['trim_end'] - $data['trim_start'];
        }

        return (new Duration($duration))->toMilliseconds()->getValue();
    }
}
