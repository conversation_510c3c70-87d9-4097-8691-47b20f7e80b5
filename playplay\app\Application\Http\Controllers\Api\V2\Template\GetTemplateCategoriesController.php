<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Template;

use App\Application\Http\Controllers\Api\BaseController;
use App\Models\Team;
use App\Models\User;
use App\Services\CategoryService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

final class GetTemplateCategoriesController extends BaseController
{
    use AuthorizesRequests;

    private Guard $guard;
    private CategoryService $categoryService;

    public function __construct(Guard $guard, CategoryService $categoryService)
    {
        $this->guard = $guard;
        $this->categoryService = $categoryService;
    }

    /**
     * @throws AuthorizationException
     */
    public function __invoke(Team $team): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('view', $team);
        /** @var User $user */
        $user = $this->guard->user();
        $categories = $this->categoryService->getOrderedTemplateCategoriesWithSubCategoriesForTeam(
            $user,
            $team
        );

        return $this->sendJsonResponse($categories->values(), Response::HTTP_OK);
    }
}
