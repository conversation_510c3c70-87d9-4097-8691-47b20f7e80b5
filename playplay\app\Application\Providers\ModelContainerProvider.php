<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\DataSeeding\ModelContainer;
use App\Infrastructure\DataSeeding\References\InMemoryModelContainer;
use Illuminate\Support\ServiceProvider;

final class ModelContainerProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->singleton(ModelContainer::class, function () {
            return new InMemoryModelContainer();
        });
    }
}
