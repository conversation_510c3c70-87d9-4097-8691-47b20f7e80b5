<?php

namespace App\Application\Events\Renders;

use App\Models\Renders\RenderProjectHtml;

class RenderProjectUpdated
{
    private RenderProjectHtml $renderProject;

    public function __construct(RenderProjectHtml $renderProject)
    {
        $this->renderProject = $renderProject;
    }

    public function getRenderProject(): RenderProjectHtml
    {
        return $this->renderProject;
    }
}
