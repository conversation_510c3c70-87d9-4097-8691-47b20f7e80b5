<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\V2\RenderProject\CreateRenderProjectController;
use App\Application\Http\Controllers\Api\V2\RenderProject\GetRenderProjectController;
use App\Application\Http\Controllers\Api\V2\RenderProject\Legacy\CreateRenderProjectController as LegacyRenderProjectController;
use App\Application\Http\Controllers\Api\V2\RenderProject\UpdateRenderProjectHtmlController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class RenderProjectRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/projects/{project}/renders',
            'as' => 'projects.renders.',
        ], static function (Router $router) {
            $router->get('/{render}', GetRenderProjectController::class)->name('show');
            $router->post('', LegacyRenderProjectController::class)->name('store');
            $router->post('/create', CreateRenderProjectController::class)->name('create');
        });

        Route::put('/render-projects-html/{renderProjectHtml}', UpdateRenderProjectHtmlController::class)
            ->name('render-projects-html.update');
    }
}
