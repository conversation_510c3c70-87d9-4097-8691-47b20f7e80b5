<?php

declare(strict_types=1);

namespace App\Application\Http\Middleware;

use Closure;
use Illuminate\Auth\Middleware\Authenticate;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

final class CompanyApiAuthentication
{
    public function handle(Request $request, Closure $next)
    {
        $token = $request->header('Authorization');

        if ($token && !Str::startsWith($token, 'Bearer')) {
            return $next($request);
        }

        return app(Authenticate::class)->handle($request, function ($request) use ($next) {
            return $next($request);
        });
    }
}
