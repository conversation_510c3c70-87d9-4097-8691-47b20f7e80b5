<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Font\DefaultFontRepository;
use App\Domain\Font\FontRepository;
use App\Infrastructure\Font\ConfigDefaultFontRepository;
use App\Infrastructure\Font\EloquentFontRepository;
use Illuminate\Support\ServiceProvider;

final class FontServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(FontRepository::class, EloquentFontRepository::class);
        $this->app->bind(DefaultFontRepository::class, ConfigDefaultFontRepository::class);
    }
}
