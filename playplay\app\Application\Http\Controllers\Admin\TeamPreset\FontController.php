<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\TeamPreset;

use App\Application\Http\Requests\Admin\TeamPresets\TeamPresetsFontsRequest;
use App\Domain\TeamPreset\TeamPresetFontService;
use App\Models\Team;
use App\Models\TeamPreset;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\RedirectResponse;

final class FontController extends AbstractTeamPresetController
{
    private TeamPresetFontService $teamPresetFontService;

    public function __construct(
        TeamPresetFontService $teamPresetFontService,
    ) {
        parent::__construct();

        $this->authorizeResource(TeamPreset::class, TeamPreset::class);
        $this->teamPresetFontService = $teamPresetFontService;
    }

    /**
     * @throws AuthorizationException
     */
    public function update(
        Team $team,
        TeamPreset $teamPreset,
        TeamPresetsFontsRequest $request
    ): RedirectResponse {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->teamPresetFontService->updateFonts($teamPreset, $request->input('fonts', []));

        return redirect()->back();
    }
}
