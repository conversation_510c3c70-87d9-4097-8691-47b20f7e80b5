<?php

namespace App\Application\Http\Requests\Api\Stock\Getty;

use Illuminate\Foundation\Http\FormRequest;

class GettyStockVideosRequest extends FormRequest
{
    public static function getRules()
    {
        return [
            'page' => ['sometimes', 'integer', 'min:1'],
            'per_page' => ['sometimes', 'integer', 'min:1'],
            'is_edito' => ['sometimes', 'boolean'],
            'query' => ['sometimes', 'string'],
            'keyword_ids' => ['sometimes', 'string'],
            'min_clip_length' => ['sometimes', 'integer'],
            'max_clip_length' => ['sometimes', 'integer'],
        ];
    }

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return static::getRules();
    }
}
