<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Team;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Team\UserStoreRequest;
use App\Application\Http\Requests\Api\Team\UserUpdateRequest;
use App\Domain\Common\Exceptions\EntityNotFoundException;
use App\Domain\Permissions\AppRoleRepository;
use App\Domain\User\Action\UserAction;
use App\Domain\User\Action\UserActionService;
use App\Domain\User\Serializer\UserWithTeamsAppRolesSerializer;
use App\Domain\User\UserRepository;
use App\Domain\UserTeam\UserTeamService;
use App\Models\Team;
use App\Models\User;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class TeamUserController extends BaseController
{
    use AuthorizesRequests;

    private UserActionService $userActionService;
    private AppRoleRepository $appRoleRepository;
    private UserTeamService $userTeamService;
    private UserWithTeamsAppRolesSerializer $userWithTeamsAppRolesSerializer;
    private UserRepository $userRepository;

    public function __construct(
        UserActionService $userActionService,
        AppRoleRepository $appRoleRepository,
        UserTeamService $userTeamService,
        UserWithTeamsAppRolesSerializer $userWithTeamsAppRolesSerializer,
        UserRepository $userRepository
    ) {
        $this->userActionService = $userActionService;
        $this->appRoleRepository = $appRoleRepository;
        $this->userTeamService = $userTeamService;
        $this->userWithTeamsAppRolesSerializer = $userWithTeamsAppRolesSerializer;
        $this->userRepository = $userRepository;
    }

    /**
     * @throws AuthorizationException
     */
    public function destroy(Team $team, User $user): JsonResponse
    {
        $this->authorize('detach-user', [$team, $user]);

        $this->userTeamService->removeUserFromTeam($user, $team);

        if (!$user->teams()->exists()) {
            $user->delete();
            $this->userActionService->addUserAction(
                new UserAction('user-deleted', [], $team->id)
            );
        }

        $this->userActionService->addUserAction(new UserAction('team-user-removed', [], $team->id));

        return $this->sendJsonResponse(new Collection([]), Response::HTTP_NO_CONTENT);
    }

    /**
     * @throws AuthorizationException
     */
    public function index(Team $team): JsonResponse
    {
        $this->authorize('view', $team);
        $users = $team->getSortedUsers();

        return $this->sendJsonResponse(new Collection($users), Response::HTTP_OK);
    }

    /**
     * @throws AuthorizationException
     * @throws EntityNotFoundException
     */
    public function store(Team $team, UserStoreRequest $request): JsonResponse
    {
        $appRole = $this->appRoleRepository->getById($request->get('app_role_id'));
        $user = $this->userRepository->getById((int)$request->get('user_id'));

        $this->authorize('add-user', [$team, $appRole, $user]);

        $this->userTeamService->addUserToTeamWithAppRole($user, $team, $appRole);

        return $this->sendJsonResponse(new Collection(), Response::HTTP_CREATED);
    }

    /**
     * @throws AuthorizationException
     * @throws EntityNotFoundException
     */
    public function update(Team $team, User $userToUpdate, UserUpdateRequest $request): JsonResponse
    {
        $newAppRole = $this->appRoleRepository->getById($request->get('app_role_id'));
        $this->authorize('update-user', [$team, $userToUpdate, $newAppRole]);

        $this->userTeamService->updateAppRoleAndPermissionsOfUserInTeam($userToUpdate, $team, $newAppRole);

        $this->userActionService->addUserAction(new UserAction('team-user-role-updated', [], $team->id));

        return new JsonResponse(
            $this->userWithTeamsAppRolesSerializer->serialize([$userToUpdate->fresh()]),
            Response::HTTP_OK
        );
    }
}
