<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\Company;

use App\Application\Http\Controllers\Api\BaseController;
use App\Domain\Company\Serializer\CompanyTeamsUsersSerializer;
use App\Models\Company;
use App\Models\User;
use Illuminate\Contracts\Auth\Access\Gate;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

final class GetCompanyController extends BaseController
{
    use AuthorizesRequests;

    private Gate $gate;

    private Guard $guard;

    private CompanyTeamsUsersSerializer $companyTeamsUserSerializer;

    public function __construct(Gate $gate, Guard $guard, CompanyTeamsUsersSerializer $companyTeamsUserSerializer)
    {
        $this->gate = $gate;
        $this->guard = $guard;
        $this->companyTeamsUserSerializer = $companyTeamsUserSerializer;
    }

    public function __invoke(Company $company): JsonResponse
    {
        $this->gate->authorize('show', $company);

        /** @var User $user */
        $user = $this->guard->user();

        return new JsonResponse(
            [
                'results' => [$this->companyTeamsUserSerializer->serialize($company, $user->teams->all())],
                'nb_results' => 1,
            ],
            Response::HTTP_OK
        );
    }
}
