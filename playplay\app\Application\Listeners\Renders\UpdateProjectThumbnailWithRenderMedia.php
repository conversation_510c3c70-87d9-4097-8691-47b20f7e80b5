<?php

declare(strict_types=1);

namespace App\Application\Listeners\Renders;

use App\Application\Events\Renders\RenderMediaProcessed;
use App\Domain\RawMedia\RawMediaType;
use App\Models\Project;
use App\Models\Renders\RenderMedia;

final class UpdateProjectThumbnailWithRenderMedia
{
    public function handle(RenderMediaProcessed $event): void
    {
        $renderMedia = $event->getRenderMedia();

        $parent = $renderMedia->parent;
        $project = $parent->project;
        if (!$this->shouldUpdateProjectThumbnailUrl($renderMedia, $project)) {
            return;
        }

        $project->update([
            'last_thumbnail_url' => $parent->thumbnail_url,
        ]);
    }

    private function shouldUpdateProjectThumbnailUrl(RenderMedia $renderMedia, ?Project $project): bool
    {
        if ($renderMedia->isParentRawMedia()) {
            return false;
        }

        if (!in_array(
            $renderMedia->type,
            [RawMediaType::IMAGE, RawMediaType::VIDEO, RawMediaType::GIF],
            true
        )) {
            return false;
        }

        if ($project === null) {
            return false;
        }

        if ($project->last_thumbnail_url !== null) {
            return false;
        }

        return true;
    }
}
