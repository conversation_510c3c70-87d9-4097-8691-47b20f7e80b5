<?php

declare(strict_types=1);

namespace App\Domain\Metrics;

final class ClusterSizeCalculator implements Calculator
{
    // Number of users => servers needed
    private const CLUSTER_SIZE_PER_USER = [
        295 => 22,
        270 => 21,
        245 => 20,
        220 => 19,
        195 => 18,
        170 => 17,
        145 => 16,
        120 => 15,
        97 => 14,
        76 => 13,
        58 => 12,
        43 => 11,
        31 => 10,
        22 => 9,
        15 => 8,
        10 => 7,
        7 => 6,
        5 => 5,
        3 => 4,
        2 => 3,
        1 => 2,
        0 => 0,
    ];

    public function calculateProcessingHtmlClusterSize(int $activeV3UsersCount, int $totalDurationInHtmlRender): int
    {
        return $this->calculateClusterSize($activeV3UsersCount, $totalDurationInHtmlRender);
    }

    public function calculateRenderingClusterSize(int $activeStudioUsersCount, int $totalDurationInRender): int
    {
        return $this->calculateClusterSize($activeStudioUsersCount, $totalDurationInRender);
    }

    public function getClusterSizePerUser(float $activeUsersCount): int
    {
        foreach (self::CLUSTER_SIZE_PER_USER as $usersCount => $size) {
            if ($activeUsersCount >= $usersCount) {
                return $size;
            }
        }

        return 0;
    }

    private function calculateClusterSize(float $activeStudioUsersCount, int $totalDurationInRender): int
    {
        $clusterSizePerUser = $this->getClusterSizePerUser($activeStudioUsersCount);
        $secondsOfVideoToAdd = $this->calculateSecondsOfVideoToAdd($clusterSizePerUser);
        $clusterSizeFromVideoDuration = (int) round(
            $totalDurationInRender / $secondsOfVideoToAdd
        );

        return (int) ceil($clusterSizePerUser + $clusterSizeFromVideoDuration);
    }

    /**
     * Add one server unit since 5 minutes in render and then one more for each 10 minutes of videos in render
     * Examples : 6 minutes in render = 1 more server unit, 18 minutes in render = 2 more server units
     * Add less server units when size is already high
     */
    private function calculateSecondsOfVideoToAdd(int $clusterSize): int
    {
        return max(600, 100 * $clusterSize);
    }
}
