<?php

declare(strict_types=1);

namespace App\Domain\Project\Duplicator;

use App\Application\Events\ProjectSettingsUpdated;
use App\Domain\Project\Services\ProjectSanitizer;
use App\Models\Project;
use App\Models\User;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

final class ProjectDuplicator
{
    private Dispatcher $eventDispatcher;
    private ProjectSanitizer $projectSanitizer;
    /** @todo create a common interface for delegated duplications to ease dependency injection */
    private ProjectUploadedRawMediasDuplicator $projectUploadedRawMediasDuplicator;
    private ProjectMusicsDuplicator $projectMusicsDuplicator;
    private ProjectVoiceoversDuplicator $projectVoiceoversDuplicator;
    private ProjectProjectScreensDuplicator $projectProjectScreensDuplicator;

    public function __construct(
        Dispatcher $eventDispatcher,
        ProjectSanitizer $projectSanitizer,
        ProjectUploadedRawMediasDuplicator $projectUploadedRawMediasDuplicator,
        ProjectMusicsDuplicator $projectMusicsDuplicator,
        ProjectVoiceoversDuplicator $projectVoiceoversDuplicator,
        ProjectProjectScreensDuplicator $projectProjectScreensDuplicator
    ) {
        $this->eventDispatcher = $eventDispatcher;
        $this->projectSanitizer = $projectSanitizer;
        $this->projectUploadedRawMediasDuplicator = $projectUploadedRawMediasDuplicator;
        $this->projectMusicsDuplicator = $projectMusicsDuplicator;
        $this->projectVoiceoversDuplicator = $projectVoiceoversDuplicator;
        $this->projectProjectScreensDuplicator = $projectProjectScreensDuplicator;
    }

    public function duplicateAndSanitize(Project $project, User $user): Project
    {
        $duplicatedProject = $this->duplicateProject($project, $user);

        // We sanitize presets on duplicated project
        $this->projectSanitizer->sanitize($duplicatedProject, false);

        if (!$project->created_by_playplay && $project->preset_id !== $duplicatedProject->preset_id) {
            /* TODO: Move this event from the app layer to domain */
            $this->eventDispatcher->dispatch(new ProjectSettingsUpdated($duplicatedProject));
        }

        return $duplicatedProject;
    }

    public function duplicateAsModel(Project $project, User $user): Project
    {
        $duplicatedProject = $this->duplicateProject($project, $user, true);
        // We sanitize presets on duplicated project
        $this->projectSanitizer->sanitize($duplicatedProject, false);

        /* TODO: Move this event from the app layer to domain */
        $this->eventDispatcher->dispatch(new ProjectSettingsUpdated($duplicatedProject));

        return $duplicatedProject;
    }

    private function duplicateProjectAttributes(Project $project, User $user, bool $keepTitle): Project
    {
        // Load project medias
        $projectValue = (new Collection($project))
            ->except('user', 'status', 'created_at', 'updated_at', 'deleted_at')
            ->toArray();

        $duplicatedProject = new Project($projectValue);
        $duplicatedProject->uuid = Str::uuid()->toString();
        $duplicatedProject->template_id = $project->template_id;
        $duplicatedProject->status = Project::STATUS_DRAFT;
        $duplicatedProject->logos = $project->duplicateLogos($duplicatedProject);
        $duplicatedProject->project_duplicated_id = $project->id;
        $duplicatedProject->assignUser($user);
        $duplicatedProject->created_by_playplay = false;
        $duplicatedProject->render_up_to_date = false;
        $duplicatedProject->snapshot_up_to_date = false;

        if (!$keepTitle) {
            $duplicatedProject->title = mb_substr($duplicatedProject->title, 0, 248) . ' (copy)';
        }

        $duplicatedProject->save();

        return $duplicatedProject;
    }

    public function duplicateProject(Project $project, User $user, bool $keepTitle = false): Project
    {
        $duplicatedProject = $this->duplicateProjectAttributes($project, $user, $keepTitle);

        $project->loadProjectScreens();
        $this->projectUploadedRawMediasDuplicator->duplicate($project, $duplicatedProject);
        $this->projectMusicsDuplicator->duplicate($project, $duplicatedProject);
        $this->projectVoiceoversDuplicator->duplicate($project, $duplicatedProject);
        $this->projectProjectScreensDuplicator->duplicate($project, $duplicatedProject);

        $duplicatedProject->setRelation('lastRender', null);
        $duplicatedProject->setRelation('lastSnapshot', null);

        return $duplicatedProject;
    }
}
