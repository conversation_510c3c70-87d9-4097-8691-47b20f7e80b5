<?php

declare(strict_types=1);

namespace App\Application\Http\Middleware;

use Closure;
use Illuminate\Config\Repository;
use Illuminate\Database\DatabaseManager;
use Illuminate\Http\Request;

final class UseAdminDatabaseConnection
{
    private Repository $configRepository;
    private DatabaseManager $databaseManager;

    public function __construct(Repository $configRepository, DatabaseManager $databaseManager)
    {
        $this->configRepository = $configRepository;
        $this->databaseManager = $databaseManager;
    }

    public function handle(Request $request, Closure $next)
    {
        $adminConnection = $this->configRepository->get('database.admin');

        if ($adminConnection !== null) {
            $this->configRepository->set('database.default', $adminConnection);
            $this->databaseManager->purge($adminConnection);
            $this->databaseManager->reconnect($adminConnection);
        }

        return $next($request);
    }
}
