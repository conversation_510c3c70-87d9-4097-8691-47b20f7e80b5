<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Screen\Parameters\OptionListCategoryRepository;
use App\Domain\Screen\Parameters\OptionListRepository;
use App\Domain\Screen\Parameters\OptionRepository;
use App\Infrastructure\Screen\Parameters\EloquentOptionListCategoryRepository;
use App\Infrastructure\Screen\Parameters\EloquentOptionListRepository;
use App\Infrastructure\Screen\Parameters\InMemoryOptionRepository;
use Illuminate\Support\ServiceProvider;

final class OptionListProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(OptionListCategoryRepository::class, EloquentOptionListCategoryRepository::class);
        $this->app->bind(OptionListRepository::class, EloquentOptionListRepository::class);
        $this->app->bind(OptionRepository::class, InMemoryOptionRepository::class);
    }
}
