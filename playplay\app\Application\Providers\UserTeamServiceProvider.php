<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\UserTeam\UserTeamAppRoleAppPermissionsRepository;
use App\Domain\UserTeam\UserTeamAppRoleRepository;
use App\Infrastructure\UserTeam\EloquentUserTeamAppRoleAppPermissionsRepository;
use App\Infrastructure\UserTeam\EloquentUserTeamAppRoleRepository;
use Carbon\Laravel\ServiceProvider;

final class UserTeamServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(UserTeamAppRoleRepository::class, EloquentUserTeamAppRoleRepository::class);
        $this->app->bind(
            UserTeamAppRoleAppPermissionsRepository::class,
            EloquentUserTeamAppRoleAppPermissionsRepository::class
        );
    }
}
