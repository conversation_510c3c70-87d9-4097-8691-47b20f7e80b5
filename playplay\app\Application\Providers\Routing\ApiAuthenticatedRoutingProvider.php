<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing;

use App\Application\Http\Controllers\Api\Media\MediaController;
use App\Application\Providers\Routing\Api\Permission\PermissionRouteMapper;
use App\Application\Providers\Routing\Api\Team\TeamBackgroundOptionsRouteMapper;
use App\Application\Providers\Routing\Api\TeamUser\TeamUserRouteMapper;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class ApiAuthenticatedRoutingProvider extends RouteServiceProvider
{
    public function map(): void
    {
        Route::group([
            'middleware' => ['api', 'auth'],
            'prefix' => 'api',
            'as' => 'api.',
        ], function () {
            $this->mapMediasRoutes();
            (new TeamBackgroundOptionsRouteMapper())->map();
            (new PermissionRouteMapper())->map();
            (new TeamUserRouteMapper())->map();
        });
    }

    private function mapMediasRoutes(): void
    {
        Route::group([
            'prefix' => '/medias',
            'as' => 'medias.',
        ], static function (Router $router) {
            $router->get('fetch/{media}', [MediaController::class, 'show'])->name('show');
        });
    }
}
