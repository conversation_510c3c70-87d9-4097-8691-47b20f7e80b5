<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Project;

use App\Application\Http\Controllers\Api\BaseController;
use App\Domain\Project\Services\ProjectSanitizer;
use App\Models\Project;
use App\Models\User;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

final class GetProjectController extends BaseController
{
    use AuthorizesRequests;

    private Guard $guard;
    private ProjectSanitizer $projectSanitizer;

    public function __construct(Guard $guard, ProjectSanitizer $projectSanitizerService)
    {
        $this->guard = $guard;
        $this->projectSanitizer = $projectSanitizerService;
    }

    public function __invoke(Project $project): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $project->company);
        $this->authorize('view', $project);

        /** @var User $user */
        $user = $this->guard->user();
        if ($project->version !== 3 || ($project->is_team_template_project && !$user->isPlayPlayAdmin())) {
            throw new NotFoundHttpException();
        }

        $this->projectSanitizer->sanitize($project);

        return $this->sendJsonResponse(new Collection([$project]), Response::HTTP_OK);
    }
}
