<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\Screen;

use Illuminate\Foundation\Http\FormRequest;

class ScreenStoreOrUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'max:120'],
            'backoffice_name' => ['required', 'max:120'],
            'screen_type' => ['required', 'string', 'regex:/(^([a-z0-9]+)$)/u', 'max:120'],
            'has_transition' => ['boolean'],
            'has_timecoded_elements' => ['boolean', 'required'],
            'addable' => ['boolean'],
            'activated_at' => 'nullable|date',
            'covers.image.*' => ['url'],
            'covers.video.*' => ['url'],
            'category_ids' => ['exists:screen_categories,id'],
            'is_generic' => ['boolean'],
        ];
    }

    public function messages(): array
    {
        return [
            'screen_type.regex' => 'The screen type must be lowercase only or/and numbers',
        ];
    }
}
