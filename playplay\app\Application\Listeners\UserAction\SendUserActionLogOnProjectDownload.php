<?php

declare(strict_types=1);

namespace App\Application\Listeners\UserAction;

use App\Application\Events\ProjectDownloaded;
use App\Domain\User\Action\UserAction;
use App\Domain\User\Action\UserActionService;

class SendUserActionLogOnProjectDownload
{
    private UserActionService $userActionService;

    public function __construct(UserActionService $userActionService)
    {
        $this->userActionService = $userActionService;
    }

    public function handle(ProjectDownloaded $event): void
    {
        $project = $event->getProject();

        $this->userActionService->addUserAction(
            new UserAction(
                'project-downloaded',
                [],
                $project->team_id,
                $project->id
            )
        );
    }
}
