<?php

declare(strict_types=1);

namespace App\Domain\Project\Sanitizer;

use App\Application\Events\ProjectScreenUpdated;
use App\Domain\TimecodedElement\Repositories\TeamTimecodedElementPresetRepository;
use App\Domain\TimecodedElement\Repositories\TimecodedElementPresetParamValueRepository;
use App\Domain\TimecodedElement\Repositories\TimecodedElementRepository;
use App\Domain\TimecodedElement\Sanitizer\PresetParamsSanitizer;
use App\Domain\TimecodedElement\TimecodedElementService;
use App\Models\Project;
use App\Models\ProjectScreen;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Support\Collection;

class TimecodedElementSanitizer implements ProjectElementsSanitizer
{
    private TimecodedElementRepository $timecodedElementRepository;
    private TimecodedElementPresetParamValueRepository $timecodedElementPresetParamValueRepository;
    private TeamTimecodedElementPresetRepository $teamTimecodedElementPresetRepository;
    private TimecodedElementService $timecodedElementService;
    private Dispatcher $eventDispatcher;
    private PresetParamsSanitizer $presetParamsSanitizer;

    public function __construct(
        TimecodedElementRepository $timecodedElementRepository,
        TimecodedElementPresetParamValueRepository $timecodedElementPresetParamValueRepository,
        TeamTimecodedElementPresetRepository $teamTimecodedElementPresetRepository,
        TimecodedElementService $timecodedElementService,
        Dispatcher $eventDispatcher,
        PresetParamsSanitizer $presetParamsSanitizer,
    ) {
        $this->timecodedElementRepository = $timecodedElementRepository;
        $this->timecodedElementPresetParamValueRepository = $timecodedElementPresetParamValueRepository;
        $this->teamTimecodedElementPresetRepository = $teamTimecodedElementPresetRepository;
        $this->timecodedElementService = $timecodedElementService;
        $this->eventDispatcher = $eventDispatcher;
        $this->presetParamsSanitizer = $presetParamsSanitizer;
    }

    public function sanitize(Project $project): bool
    {
        $projectScreensSanitized = new Collection();
        $projectScreensSanitized = $projectScreensSanitized->concat($this->sanitizeProjectScreens($project));
        $projectScreensSanitized = $projectScreensSanitized->concat(
            $this->sanitizeTimecodedElementPresetsAndValues($project)
        );

        $projectScreensSanitized
            ->unique()
            ->each(
                fn(ProjectScreen $projectScreen) => $this->eventDispatcher->dispatch(
                    new ProjectScreenUpdated($projectScreen)
                )
            );

        return !$projectScreensSanitized->isEmpty();
    }

    /** @return Collection<ProjectScreen> */
    private function getProjectScreensToSanitize(Project $project): Collection
    {
        if (!$project->company->hasTimecodedElementsFeature()) {
            return $project->projectScreens->filter(
                fn(ProjectScreen $projectScreen) => $projectScreen->timecodedElements->count() > 0
            );
        }

        $project->projectScreens->loadMissing('screen', 'timecodedElements');

        return $project
            ->projectScreens
            ->filter(
                fn(ProjectScreen $projectScreen) => !$projectScreen->screen->has_timecoded_elements
                    && $projectScreen->timecodedElements->count() > 0
            );
    }

    /** @return Collection<ProjectScreen> */
    private function sanitizeProjectScreens(Project $project): Collection
    {
        $projectScreensToSanitize = $this->getProjectScreensToSanitize($project);
        if ($projectScreensToSanitize->count() === 0) {
            return new Collection();
        }

        $projectScreenIdsToSanitize = $projectScreensToSanitize
            ->pluck('id')
            ->all();

        $this->timecodedElementPresetParamValueRepository->deleteAllByProjectScreenIds(
            $projectScreenIdsToSanitize
        );

        $this->timecodedElementRepository->deleteByProjectScreenIds(
            $projectScreenIdsToSanitize
        );

        return $projectScreensToSanitize;
    }

    /** @return Collection<ProjectScreen> */
    private function sanitizeTimecodedElementPresetsAndValues(Project $project): Collection
    {
        $projectScreensSanitized = new Collection();

        $timecodedElementPresetIdsAttachedToTeam = $this->getTimecodedElementPresetIdsAttachedToTeam($project);

        $project->projectScreens->load('timecodedElements');

        foreach ($project->projectScreens as $projectScreen) {
            foreach ($projectScreen->timecodedElements as $timecodedElement) {
                $timecodedElementHasBeenSanitized = false;
                if (!in_array(
                    $timecodedElement->timecodedElementPreset?->id,
                    $timecodedElementPresetIdsAttachedToTeam,
                    true
                )
                ) {
                    $this->timecodedElementService->deleteWithoutEventDispatching($timecodedElement);
                    $timecodedElementHasBeenSanitized = true;
                } else {
                    $timecodedElementHasBeenSanitized |= $this->presetParamsSanitizer->sanitize($timecodedElement);
                }

                if (!$timecodedElementHasBeenSanitized) {
                    continue;
                }

                if (!$projectScreensSanitized->contains($projectScreen)) {
                    $projectScreensSanitized->add($projectScreen);
                }
            }
        }

        return $projectScreensSanitized;
    }

    private function getTimecodedElementPresetIdsAttachedToTeam(Project $project): array
    {
        if ($project->team === null) {
            return [];
        }

        return $this->teamTimecodedElementPresetRepository
            ->getByTeamId($project->team->id)
            ->pluck('timecoded_element_preset_id')
            ->all();
    }
}
