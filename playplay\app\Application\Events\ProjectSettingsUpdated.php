<?php

namespace App\Application\Events;

use App\Models\Project;
use Illuminate\Queue\SerializesModels;

class ProjectSettingsUpdated
{
    use SerializesModels;

    private Project $project;
    private array $settings;

    public function __construct(Project $project, array $settings = [])
    {
        $this->project = $project;
        $this->settings = $settings;
    }

    public function getProject(): Project
    {
        return $this->project;
    }

    public function getSettings(): array
    {
        return $this->settings;
    }
}
