<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Admin;

use App\Application\Http\Controllers\Admin\RenderMedia\CancelRenderingController;
use App\Application\Http\Controllers\Admin\RenderMedia\ListRenderMediaController;
use App\Application\Http\Controllers\Admin\RenderMedia\ShowRenderMediaController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class RenderMediaRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/renderMedias',
            'as' => 'renderMedias.',
        ], static function (Router $router) {
            $router->get('/', ListRenderMediaController::class)->name('index');
            $router->get('/{renderMedia}', ShowRenderMediaController::class)->name('show');
            $router->get('/{renderMedia}/cancelRendering', CancelRenderingController::class)->name('cancelRendering');
        });
    }
}
