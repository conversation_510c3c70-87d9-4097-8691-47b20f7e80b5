<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Plan;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Models\Plan;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;

final class PlanListingController extends BaseController
{
    private Factory $viewFactory;

    public function __construct(Factory $viewFactory)
    {
        $this->authorizeResource(Plan::class);
        $this->viewFactory = $viewFactory;
    }

    public function index(): View
    {
        return $this->viewFactory->make('admin.plans.index', [
            'plans' => Plan::all(),
        ]);
    }
}
