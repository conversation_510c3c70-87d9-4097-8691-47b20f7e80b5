<?php

namespace App\Application\Http\Requests\Api\Billing;

use Illuminate\Foundation\Http\FormRequest;

class BillingUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'billing_info' => ['sometimes', 'array'],
            'billing_info.country' => ['nullable', 'string', 'country_iso3166_alpha2'],
            'billing_info.company_name' => ['nullable', 'string'],
            'billing_info.address_line1' => ['nullable', 'string'],
            'billing_info.address_line2' => ['nullable', 'string'],
            'billing_info.address_postal_code' => ['nullable', 'string'],
            'billing_info.address_state' => ['nullable', 'string'],
            'billing_info.address_city' => ['nullable', 'string'],
            'billing_info.vat' => ['nullable', 'string'],
            'stripe_token' => ['sometimes', 'string'],
        ];
    }
}
