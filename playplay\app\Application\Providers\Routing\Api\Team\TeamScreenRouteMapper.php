<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Team;

use App\Application\Http\Controllers\Api\V2\Team\GetTeamScreensController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class TeamScreenRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::get('/team/{team}/screens', GetTeamScreensController::class)->name('team.screens.index');
    }
}
