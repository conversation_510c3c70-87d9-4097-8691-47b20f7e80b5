<?php

namespace App\Application\Http\Requests\Api\Preview;

use App\Models\Snapshot;
use Illuminate\Foundation\Http\FormRequest;

class SnapshotStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'parent_id' => ['required', 'integer', 'min:1'],
            'parent_type' => ['required', 'in:' . Snapshot::TYPE_PROJECT . ',' . Snapshot::TYPE_PROJECT_SCREEN],
        ];
    }
}
