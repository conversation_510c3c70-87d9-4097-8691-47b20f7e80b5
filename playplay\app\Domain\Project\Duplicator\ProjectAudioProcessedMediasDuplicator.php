<?php

declare(strict_types=1);

namespace App\Domain\Project\Duplicator;

use App\Domain\Render\RenderMedia\LegacyRenderMediaDataFactory;
use App\Domain\Render\RenderMedia\RenderMediaStatus;
use App\Models\ProcessedMedia;
use App\Models\Project;
use App\Services\ProcessedMedia\Factory as ProcessedMediaFactory;
use Illuminate\Support\Collection;

/**
 * @internal this class doesn't have its own tests
 */
final class ProjectAudioProcessedMediasDuplicator
{
    private ProcessedMediaFactory $processedMediaFactory;

    public function __construct(ProcessedMediaFactory $processedMediaFactory)
    {
        $this->processedMediaFactory = $processedMediaFactory;
    }

    public function duplicate(Project $duplicatedProject, Collection $audioProcessedMedia): void
    {
        /** @var ProcessedMedia $processedMedia */
        foreach ($audioProcessedMedia as $processedMedia) {
            $newProcessedMedia = $this->processedMediaFactory->create(
                $processedMedia->raw_media_id,
                $processedMedia->source,
                $duplicatedProject->id,
                $processedMedia->relation_type,
            );

            $data = $processedMedia->data ?? [];

            $shouldCreateNewRenderMediaToProcess = $processedMedia->lastRender === null
                || $processedMedia->lastRender->isProcessing();

            if ($shouldCreateNewRenderMediaToProcess) {
                $this->createRenderMedia($newProcessedMedia, $data, RenderMediaStatus::toProcess(), null);

                continue;
            }

            $this->createRenderMedia(
                $newProcessedMedia,
                $data,
                $processedMedia->lastRender->status,
                $processedMedia->lastRender->thumbnail_url
            );
        }
    }

    private function createRenderMedia(
        ProcessedMedia $duplicatedProcessedMedia,
        array $data,
        RenderMediaStatus $status,
        ?string $thumbnailUrl
    ): void {
        $duplicatedProcessedMedia->renders()->create([
            'status' => $status,
            'thumbnail_url' => $thumbnailUrl,
            'data' => LegacyRenderMediaDataFactory::createProcessedMediaData([
                'trimStart' => $data['trimStart'] ?? null,
                'trimEnd' => $data['trimEnd'] ?? null,
                'start' => $data['start'] ?? null,
            ]),
        ]);
    }
}
