<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\Music;

use Illuminate\Foundation\Http\FormRequest;

final class MusicStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'music_list_id' => ['required', 'exists:music_lists,id'],
            'file_url' => ['required', 'url'],
        ];
    }
}
