<?php

namespace App\Application\Http\Requests\Admin\Company;

use Illuminate\Foundation\Http\FormRequest;

class CompanyUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => ['string'],
            'type' => ['nullable', 'string'],
            'csm_id' => ['sometimes', 'exists:users,id'],
            'plan_id' => ['required', 'exists:plans,id'],
            'data_is_restricted' => ['sometimes', 'boolean'],
            'client_until' => ['nullable', 'date'],
            'client_from' => ['nullable', 'date'],
            'feature_items.*' => ['nullable', 'integer', 'min:0'],
        ];
    }
}
