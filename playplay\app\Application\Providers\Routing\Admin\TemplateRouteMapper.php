<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Admin;

use App\Application\Http\Controllers\Admin\Template\CategoryController;
use App\Application\Http\Controllers\Admin\Template\SubCategoryController;
use App\Application\Http\Controllers\Admin\Template\TemplateController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class TemplateRouteMapper implements RouteMapper
{
    public function map(): void
    {
        $this->mapTemplates();
        $this->mapTemplateCategories();
        $this->mapTemplateSubCategories();
    }

    private function mapTemplateCategories(): void
    {
        Route::group([
            'prefix' => '/templateCategories',
            'as' => 'templateCategories.',
        ], static function (Router $router) {
            $router->put('/reorder', [CategoryController::class, 'reorder'])->name('reorder');
            $router->get('/', [CategoryController::class, 'index'])->name('index');
            $router->get('/create', [CategoryController::class, 'create'])->name('create');
            $router->post('/', [CategoryController::class, 'store'])->name('store');
            $router->get('/{category}/edit', [CategoryController::class, 'edit'])->name('edit');
            $router->put('/{category}', [CategoryController::class, 'update'])->name('update');
            $router->delete('/{category}', [CategoryController::class, 'destroy'])->name('destroy');
            $router->get('/{category}/danger-zone', [CategoryController::class, 'showDangerZone'])
                ->name('danger-zone');
            $router->get('/subCategory/{subCategory}/card', [CategoryController::class, 'card'])
                ->name('subCategories.card');
        });
    }

    private function mapTemplateSubCategories(): void
    {
        Route::group([
            'prefix' => '/templateSubCategories',
            'as' => 'templateSubCategories.',
        ], static function (Router $router) {
            $router->get('/', [SubCategoryController::class, 'index'])->name('index');
            $router->get('/create', [SubCategoryController::class, 'create'])->name('create');
            $router->post('/', [SubCategoryController::class, 'store'])->name('store');
            $router->get('/{subCategory}/edit', [SubCategoryController::class, 'edit'])->name('edit');
            $router->put('/{subCategory}', [SubCategoryController::class, 'update'])->name('update');
            $router->delete('/{subCategory}', [SubCategoryController::class, 'destroy'])->name('destroy');
            $router->get('/{subCategory}/danger-zone', [SubCategoryController::class, 'showDangerZone'])
                ->name('danger-zone');
            $router->get('/template/{template}/card', [SubCategoryController::class, 'card'])
                ->name('templates.card');
        });
    }

    private function mapTemplates(): void
    {
        Route::group([
            'prefix' => '/templates',
            'as' => 'templates.',
        ], static function (Router $router) {
            $router->get('/', [TemplateController::class, 'index'])->name('index');
            $router->get('/create', [TemplateController::class, 'create'])->name('create');
            $router->post('/', [TemplateController::class, 'store'])->name('store');
            $router->get('/{template}/edit', [TemplateController::class, 'edit'])->name('edit');
            $router->put('/{template}', [TemplateController::class, 'update'])->name('update');
            $router->delete('/{template}', [TemplateController::class, 'destroy'])->name('destroy');
            $router->get('/{template}/danger-zone', [TemplateController::class, 'showDangerZone'])
                ->name('danger-zone');
        });
    }
}
