<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\ProjectScreen;

use App\Application\Events\ProjectUpdated;
use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Project\ProjectScreen\ProjectScreenBulkUpdateRequest;
use App\Domain\ProjectScreen\ProjectScreenService;
use App\Domain\User\Action\UserAction;
use App\Domain\User\Action\UserActionService;
use App\Models\Project;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

final class DuplicateManyProjectScreensController extends BaseController
{
    use AuthorizesRequests;

    private Dispatcher $eventDispatcher;
    private UserActionService $userActionService;
    private ProjectScreenService $projectScreenService;

    public function __construct(
        Dispatcher $eventDispatcher,
        UserActionService $userActionService,
        ProjectScreenService $projectScreenService,
    ) {
        $this->middleware('can:view,project');
        $this->eventDispatcher = $eventDispatcher;
        $this->userActionService = $userActionService;
        $this->projectScreenService = $projectScreenService;
    }

    public function __invoke(ProjectScreenBulkUpdateRequest $request, Project $project): JsonResponse
    {
        $this->authorize('update', $project);
        $projectScreens = $this->projectScreenService->duplicateAndReorder(
            $project,
            $request->input('ids')
        );
        // @todo should be in domain service
        $this->eventDispatcher->dispatch(new ProjectUpdated($project));
        $this->userActionService->addUserAction(
            new UserAction(
                'project-screens-bulk-duplicate',
                [],
                $project->team_id,
                $project->id,
            )
        );

        return $this->sendJsonResponse($projectScreens->values(), Response::HTTP_CREATED);
    }
}
