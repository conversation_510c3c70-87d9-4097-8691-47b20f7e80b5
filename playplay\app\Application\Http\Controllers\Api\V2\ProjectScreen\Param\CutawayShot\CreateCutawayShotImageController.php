<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\ProjectScreen\Param\CutawayShot;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Mappers\CutawayShot\CutawayShotImageRenderMediaDataMapper;
use App\Application\Http\Requests\Api\Project\ProjectScreen\Param\CutawayShot\StoreCutawayShotImageRequest;
use App\Application\Policies\CutawayShotPolicy;
use App\Domain\CutawayShot\CutawayShotService;
use App\Domain\RawMedia\RawMediaRepository;
use App\Domain\Workflow\Config\MediaWorkflow;
use App\Models\ProjectScreen;
use App\Models\ScreenParams\ParamCutawayShot;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class CreateCutawayShotImageController extends BaseController
{
    use AuthorizesRequests;

    private CutawayShotService $cutawayShotService;
    private CutawayShotImageRenderMediaDataMapper $cutawayShotImageRenderMediaDataMapper;
    private MediaWorkflow $mediaWorkflow;
    private RawMediaRepository $rawMediaRepository;

    public function __construct(
        CutawayShotService $cutawayShotService,
        CutawayShotImageRenderMediaDataMapper $cutawayShotImageRenderMediaDataMapper,
        MediaWorkflow $mediaWorkflow,
        RawMediaRepository $rawMediaRepository,
    ) {
        $this->cutawayShotService = $cutawayShotService;
        $this->cutawayShotImageRenderMediaDataMapper = $cutawayShotImageRenderMediaDataMapper;
        $this->mediaWorkflow = $mediaWorkflow;
        $this->rawMediaRepository = $rawMediaRepository;
    }

    /**
     * @throws AuthorizationException
     */
    public function __invoke(
        ProjectScreen $projectScreen,
        ParamCutawayShot $param,
        StoreCutawayShotImageRequest $request
    ): JsonResponse {
        $rawMedia = $this->rawMediaRepository->getById($request->get('raw_media_id'));
        $project = $projectScreen->project;
        $source = $request->get('source');
        $this->authorize('create', [CutawayShotPolicy::RESOURCE_NAME, $rawMedia, $project, $source]);
        $cutawayShotImageRenderMediaData = $this->cutawayShotImageRenderMediaDataMapper->fromRequest($request);

        $processedMedia = $this->cutawayShotService->create(
            $projectScreen->project_id,
            $rawMedia->id,
            $source,
            $cutawayShotImageRenderMediaData
        );

        $this->mediaWorkflow->start($rawMedia);

        return $this->sendJsonResponse(new Collection([$processedMedia]), Response::HTTP_CREATED);
    }
}
