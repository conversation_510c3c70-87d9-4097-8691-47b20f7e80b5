<?php

declare(strict_types=1);

namespace App\Application\Http\Middleware;

use Illuminate\Config\Repository as ConfigRepository;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Http\Middleware\TrustHosts as Middleware;

final class TrustHosts extends Middleware
{
    private ConfigRepository $configRepository;

    public function __construct(Application $app, ConfigRepository $configRepository)
    {
        parent::__construct($app);
        $this->configRepository = $configRepository;
    }

    public function hosts(): array
    {
        return array_merge([
            $this->allSubdomainsOfApplicationUrl(),
        ], $this->configRepository->get('infrastructure.authorized_private_networks', []));
    }
}
