<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api;

use App\Domain\HealthCheck\HealthzService;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Symfony\Component\HttpFoundation\Response;

final class HealthzController extends Controller
{
    private HealthzService $healthzService;

    public function __construct(HealthzService $healthzService)
    {
        $this->healthzService = $healthzService;
    }

    public function __invoke(): JsonResponse
    {
        $this->healthzService->check();

        return new JsonResponse(['status' => 'ok'], Response::HTTP_OK);
    }
}
