<?php

namespace App\Application\Providers;

use App\Domain\RecentlyUsedScreens\RecentlyUsedScreensRepository;
use App\Infrastructure\RecentlyUsedScreens\EloquentRecentlyUsedScreensRepository;
use Illuminate\Support\ServiceProvider;

final class RecentlyUsedScreensRepositoryProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(RecentlyUsedScreensRepository::class, EloquentRecentlyUsedScreensRepository::class);
    }
}
