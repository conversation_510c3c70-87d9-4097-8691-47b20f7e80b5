<?php

namespace App\Application\Providers\Routing\Admin;

use App\Application\Http\Controllers\Admin\User\CheckUserEmailExistenceController;
use App\Application\Http\Controllers\Admin\User\UserListingController;
use App\Application\Http\Controllers\Admin\User\UserLogAsController;
use App\Application\Http\Controllers\Admin\User\UserMigrateProjectController;
use App\Application\Http\Controllers\Admin\User\UserPermissionController;
use App\Application\Http\Controllers\Admin\User\UserTeamController;
use App\Application\Http\Controllers\Admin\User\UserTransferController;
use App\Application\Http\Controllers\Admin\UserController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

class UserRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/users',
            'as' => 'users.'
        ], function (Router $router): void {
            $router->get('/', [UserController::class, 'index'])->name('index');
            $router->post('/', [UserController::class, 'store'])->name('store');
            $router->get('/create', [UserController::class, 'create'])->name('create');
            $router->put('/{user}', [UserController::class, 'update'])->name('update');
            $router->get('/{user}/edit', [UserController::class, 'edit'])->name('edit');

            $this->mapUserListing($router);
            $this->mapUserPermission($router);
            $this->mapUserLogAs($router);
            $this->mapUserProject($router);
            $this->mapUserTransfer($router);
            $this->mapUserTeam($router);

            $router->get('/email-exists', CheckUserEmailExistenceController::class)->name('email-exists');
        });
    }

    private function mapUserListing(Router $router): void
    {
        $router->get('users-filters', [UserListingController::class, 'filters'])->name('filters');
        $router->get('/teams/filter-company', [UserListingController::class, 'filterCompany'])
            ->name('filter-company-teams');
    }

    private function mapUserLogAs(Router $router): void
    {
        $router->get('/log_as/{user?}', [UserLogAsController::class, 'logAs'])->name('log-as');
    }

    private function mapUserPermission(Router $router): void
    {
        $router->get('/{user}/danger-zone', [UserPermissionController::class, 'showDangerZone'])
            ->name('danger-zone');
        $router->put('/update-permissions/{user}', [UserPermissionController::class, 'updatePermissions'])
            ->name('update-permissions');
        $router->get('/{user}/deactivate-account', [UserPermissionController::class, 'showDeactivateAccount'])
            ->name('show-deactivate-account');
        $router->delete('/{user}/deactivate-account', [UserPermissionController::class, 'deactivateAccount'])
            ->name('deactivate-account');
    }

    private function mapUserProject(Router $router): void
    {
        $router->get('/migrate-projects/{user}', [UserMigrateProjectController::class, 'show'])
            ->name('show-migrate-projects');
        $router->put('/migrate-projects/{user}', [UserMigrateProjectController::class, 'migrateProjectsToAnotherUser'])
            ->name('migrate-projects');
    }

    private function mapUserTransfer(Router $router): void
    {
        $router->get('/transfer-user/{user}', [UserTransferController::class, 'show'])->name('show-transfer-user');
        $router->put('/transfer-user/{user}', [UserTransferController::class, 'transferUser'])->name('transfer-user');
    }

    private function mapUserTeam(Router $router): void
    {
        $router->get('/show-teams/{user}', [UserTeamController::class, 'show'])->name('show-teams');
        $router->put('/update-teams/{user}', [UserTeamController::class, 'updateTeamAndCompany'])->name('update-teams');
    }
}
