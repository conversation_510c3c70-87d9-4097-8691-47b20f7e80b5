<?php

namespace App\Application\Http\Controllers\Api\V2\Media;

use App\Application\Events\ProjectUpdated;
use App\Application\Http\Controllers\Api\BaseController;
use App\Domain\Workflow\Config\MediaWorkflow;
use App\Domain\RawMedia\RawMediaRepository;
use App\Domain\RawMedia\RawMediaType;
use App\Domain\Render\RenderMedia\LegacyRenderMediaDataFactory;
use App\Domain\Render\RenderMedia\RawMetadataFactory;
use App\Domain\Render\RenderMedia\RenderMediaRepository;
use App\Domain\Render\RenderMedia\RenderMediaStatus;
use App\Domain\RenderJob\RenderJobRepository;
use App\Domain\RenderJob\RenderJobStatus;
use App\Domain\RenderJob\RenderJobType;
use App\Models\Media;
use App\Models\ProcessedMedia;
use App\Models\RawMedia;
use App\Models\Renders\RenderMedia;
use App\Services\Processing\ProcessingQueue;
use App\Services\Processing\RenderMediaService;
use Carbon\Carbon;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use LogicException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

final class RenderMediaController extends BaseController
{
    private ProcessingQueue $processingQueue;
    private RawMediaRepository $rawMediaRepository;
    private RenderMediaRepository $renderMediaRepository;
    private RenderJobRepository $renderJobRepository;
    private RenderMediaService $renderService;
    private Dispatcher $dispatcher;
    private MediaWorkflow $mediaWorkflow;

    public function __construct(
        RenderMediaService $renderService,
        ProcessingQueue $processingQueue,
        RenderMediaRepository $renderMediaRepository,
        RenderJobRepository $renderJobRepository,
        RawMediaRepository $rawMediaRepository,
        Dispatcher $dispatcher,
        MediaWorkflow $mediaWorkflow
    ) {
        $this->renderService = $renderService;
        $this->processingQueue = $processingQueue;
        $this->renderMediaRepository = $renderMediaRepository;
        $this->renderJobRepository = $renderJobRepository;
        $this->rawMediaRepository = $rawMediaRepository;
        $this->dispatcher = $dispatcher;
        $this->mediaWorkflow = $mediaWorkflow;
    }

    public function callbackProcessedMedia(RenderMedia $renderMedia, Request $request): JsonResponse
    {
        if ($renderMedia->parent_type !== ProcessedMedia::DEPRECATED_CLASS) {
            throw new NotFoundHttpException();
        }

        $this->handleCallback($renderMedia, $request);

        if ($renderMedia->type === Media::IMAGE_TYPE) {
            $renderJobType = $this->identifyRenderMediaRenderJobType($renderMedia);
            $this->createRenderJob($renderMedia, $request, $renderJobType);
        }

        $this->renderService->dispatchRendersIfReady($renderMedia);

        if ($renderMedia->parent->project !== null && !$renderMedia->parent->trashed()) {
            $this->dispatcher->dispatch(new ProjectUpdated($renderMedia->parent->project));
        }

        return new JsonResponse();
    }

    public function callbackRawMedia(RenderMedia $renderMedia, Request $request): JsonResponse
    {
        if ($renderMedia->parent_type !== RawMedia::DEPRECATED_CLASS) {
            throw new NotFoundHttpException();
        }

        try {
            $this->handleCallback($renderMedia, $request);

            if ($request->input('should_update_project', true)) {
                // Update all projects linked to raw media
                foreach ($renderMedia->parent->projects as $project) {
                    if ($project !== null) {
                        $this->dispatcher->dispatch(new ProjectUpdated($project));
                    }
                }
            }
        } catch (LogicException $exception) {
            // Ignore callback : CM-270 canceled job in front : removed media after asking for preview
            // Avoid Error to be logged on sentry as it's an acceptable behavior
        }

        if ($this->isFullyCompletedIdentifyJob($request)) {
            $this->createRenderJob($renderMedia, $request, RenderJobType::IDENTIFY);

            $this->mediaWorkflow->start($renderMedia->parent);
        }

        return new JsonResponse();
    }

    private function handleCallback(RenderMedia $renderMedia, Request $request): void
    {
        $metrics = (new Collection($request->get('metrics', [])))->keyBy('label');

        if ($request->has('error')) {
            $this->handleRenderMediaError($renderMedia, $request->get('error', []), $metrics);
        } elseif ($request->has('result')) {
            $this->handleRenderMediaProcessed($renderMedia, $request->get('result', []), $metrics);
        }
    }

    private function identifyRenderMediaRenderJobType(RenderMedia $renderMedia): RenderJobType
    {
        $toQueueData = $renderMedia->to_queue_data ?? [];
        if ((array_key_exists('crop', $toQueueData) && $toQueueData['crop'] !== null)
            || (array_key_exists('keepSize', $toQueueData) && $toQueueData['keepSize'] !== null)
        ) {
            return RenderJobType::IMAGE_RESIZE;
        }

        return RenderJobType::IMAGE_RESIZE_AUTO;
    }

    private function isFullyCompletedIdentifyJob(Request $request): bool
    {
        $result = $request->get('result', []);

        return array_key_exists('raw_metadata', $result)
            && $result['raw_metadata'] !== null
            && array_key_exists('renderedUrl', $result)
            && $result['renderedUrl'] !== null
            && array_key_exists('thumbnailUrl', $result)
            && $result['thumbnailUrl'] !== null;
    }

    private function createRenderJob(RenderMedia $renderMedia, Request $request, RenderJobType $type): void
    {
        $this->renderJobRepository->create([
            'type' => $type,
            'status' => RenderJobStatus::DONE,
            'data' => $renderMedia->to_queue_data,
            'result' => $request->get('result'),
            'metrics' => $request->get('metrics'),
            'config' => $request->get('config'),
            'parent_id' => $renderMedia->id,
            'parent_type' => RenderMedia::class,
        ]);
    }

    private function handleRenderMediaError(RenderMedia $renderMedia, array $data, Collection $metrics): void
    {
        $renderMediaData = $renderMedia->data?->toArray();
        // Prevent from overriding exciting error
        $renderMediaDataErrors = data_get($renderMediaData, 'error', []);
        $renderMediaData['error'] = [...$renderMediaDataErrors, ...$data];

        $dataToFill = [
            'data' => $renderMediaData,
            'status' => RenderMediaStatus::canceled(),
            'rendered_url' => data_get($data, 'renderedUrl') ?: $renderMedia->rendered_url,
            'thumbnail_url' => data_get($data, 'thumbnailUrl') ?: $renderMedia->thumbnail_url,
        ];

        if ($metrics->isNotEmpty()) {
            $dataToFill['launched_at'] = Carbon::createFromTimestampMs($metrics['start']['ts']);
            $dataToFill['rendered_at'] = Carbon::createFromTimestampMs($metrics['api']['ts']);
        }

        $this->updateRenderMedia($renderMedia, $dataToFill);

        $renderMedia->errors()->create($data);

        if ($renderMedia->isParentRawMedia()) {
            $this->renderService->cancelRenderMediasLinkedToRawMedia($renderMedia);

            /** @var RawMedia $rawMedia */
            $rawMedia = $renderMedia->parent;

            if ($rawMedia->isSourceWithExpiredLink()) {
                $this->rawMediaRepository->markAsErrored([$rawMedia->id]);
            }
        }
    }

    private function handleRenderMediaProcessed(RenderMedia $renderMedia, array $data, Collection $metrics): void
    {
        $dataToFill = [
            'data' => $data,
            'rendered_url' => data_get($data, 'renderedUrl') ?: data_get($data, 'output') ?: $renderMedia->rendered_url,
            'thumbnail_url' => data_get($data, 'thumbnailUrl') ?: $renderMedia->thumbnail_url,
        ];

        if ($metrics->isNotEmpty()) {
            $dataToFill['launched_at'] = Carbon::createFromTimestampMs($metrics['start']['ts']);
            $dataToFill['rendered_at'] = Carbon::createFromTimestampMs($metrics['api']['ts']);
        }

        $this->updateRenderMedia($renderMedia, $dataToFill);

        if ($renderMedia->isParentRawMedia()) {
            $type = data_get($data, 'type');
            if (($data['subtype'] ?? null) === RawMediaType::GIF && data_get($data, 'metadata.frames') > 1) {
                $type = RawMediaType::GIF;
            }

            // Update raw media type
            $renderMedia->parent->update([
                'url' => data_get($data, 'objectUrl'),
                'type' => new RawMediaType($type),
            ]);

            if ($type === RawMediaType::AUDIO && $renderMedia->rendered_url
                && !$this->renderMediaRepository->hasAudioPeaksDataJob($renderMedia)
            ) {
                $this->processingQueue->extractAudioPeaksData($renderMedia);
            }

            if ($renderMedia->isProcessed()) {
                $this->renderService->dispatchRenderMedias($renderMedia);
            }
        }
    }

    private function updateRenderMedia(RenderMedia $renderMedia, array $rawData): void
    {
        if (array_key_exists('data', $rawData) && $rawData['data'] !== null) {
            // handle raw metadata
            if (array_key_exists('raw_metadata', $rawData['data']) && $rawData['data']['raw_metadata'] !== null) {
                $rawMetadata = RawMetadataFactory::createFromArray($rawData['data']['raw_metadata']);
                unset($rawData['data']['raw_metadata']);
                $data = LegacyRenderMediaDataFactory::createFromParentType($renderMedia->parent_type, $rawData['data']);
                $this->renderMediaRepository->updateDataWithRawMetadata(
                    $renderMedia,
                    $data,
                    $rawMetadata
                );
            } else {
                $data = LegacyRenderMediaDataFactory::createFromParentType($renderMedia->parent_type, $rawData['data']);
                $this->renderMediaRepository->updateData(
                    $renderMedia,
                    $data
                );
            }

            unset($rawData['data']);
        }

        $renderMedia->update($rawData);
        $this->renderMediaRepository->updateStatusAndRenderedAtAndDispatchEvents($renderMedia);
    }
}
