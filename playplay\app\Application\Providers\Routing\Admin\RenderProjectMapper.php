<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Admin;

use App\Application\Http\Controllers\Admin\RenderProject\CancelRenderingController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class RenderProjectMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/render-project/{render}',
            'as' => 'render-project.',
        ], static function (Router $router) {
            $router->get('/cancel-rendering', CancelRenderingController::class)->name('cancel-rendering');
        });
    }
}
