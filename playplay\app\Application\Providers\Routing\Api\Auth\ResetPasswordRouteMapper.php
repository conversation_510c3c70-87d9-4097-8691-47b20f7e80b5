<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Auth;

use App\Application\Http\Controllers\Api\V2\Auth\ResetPasswordController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class ResetPasswordRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/password',
            'as' => 'password.',
        ], static function (Router $router) {
            $router->get('/reset/{passwordReset}', [ResetPasswordController::class, 'show'])->name('reset.show');
            $router->post('/reset', [ResetPasswordController::class, 'store'])->name('reset.store')
                ->middleware('set-cookies');
            $router->post('/email', [ResetPasswordController::class, 'email'])->name('reset.email')
                ->middleware('user-from-date-not-reached');
        });
    }
}
