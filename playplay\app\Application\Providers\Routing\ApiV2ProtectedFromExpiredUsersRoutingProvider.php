<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing;

use App\Application\Providers\Routing\Api\CustomPage\CustomPageRouteMapper;
use App\Application\Providers\Routing\Api\GettyStockRouteMapper;
use App\Application\Providers\Routing\Api\Media\ProcessedMediaRouteMapper;
use App\Application\Providers\Routing\Api\Media\RawMediaRouteMapper;
use App\Application\Providers\Routing\Api\Project\ProjectRouteMapper;
use App\Application\Providers\Routing\Api\ProjectScreen\CutawayShotRouteMapper;
use App\Application\Providers\Routing\Api\ProjectScreen\ProjectScreenRouteMapper;
use App\Application\Providers\Routing\Api\ProjectScreenParamRouteMapper;
use App\Application\Providers\Routing\Api\RenderProjectRouteMapper;
use App\Application\Providers\Routing\Api\ScreenRouteMapper;
use App\Application\Providers\Routing\Api\ShareableLinkSettingsRouteMapper;
use App\Application\Providers\Routing\Api\SnapshotRouteMapper;
use App\Application\Providers\Routing\Api\StockRouteMapper;
use App\Application\Providers\Routing\Api\SubtitleRouteMapper;
use App\Application\Providers\Routing\Api\Team\TeamPresetV2RouteMapper;
use App\Application\Providers\Routing\Api\Team\TeamScreenRouteMapper;
use App\Application\Providers\Routing\Api\Team\TeamUserRouteMapper;
use App\Application\Providers\Routing\Api\TeamsTemplateRouteMapper;
use App\Application\Providers\Routing\Api\TimecodedElement\TimecodedElementPresetRouteMapper;
use App\Application\Providers\Routing\Api\TimecodedElement\TimecodedElementRouteMapper;
use App\Application\Providers\Routing\Api\User\UserNotificationRouteMapper;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider;
use Illuminate\Support\Facades\Route;

final class ApiV2ProtectedFromExpiredUsersRoutingProvider extends RouteServiceProvider
{
    /** @var RouteMapper[] */
    private array $routeMappers;

    public function __construct(Application $app)
    {
        parent::__construct($app);
        // Declare all route mappers
        $this->routeMappers = [
            new CutawayShotRouteMapper(),
            new GettyStockRouteMapper(),
            new ProcessedMediaRouteMapper(),
            new ProjectRouteMapper(),
            new ProjectScreenParamRouteMapper(),
            new ProjectScreenRouteMapper(),
            new RawMediaRouteMapper(),
            new RenderProjectRouteMapper(),
            new ScreenRouteMapper(),
            new ShareableLinkSettingsRouteMapper(),
            new SnapshotRouteMapper(),
            new StockRouteMapper(),
            new SubtitleRouteMapper(),
            new TeamPresetV2RouteMapper(),
            new TeamScreenRouteMapper(),
            new TeamsTemplateRouteMapper(),
            new TeamUserRouteMapper(),
            new TimecodedElementPresetRouteMapper(),
            new TimecodedElementRouteMapper(),
            new UserNotificationRouteMapper(),
            new CustomPageRouteMapper(),
        ];
    }

    public function map(): void
    {
        Route::group([
            'middleware' => ['api', 'auth', 'expired-users', 'check-inactive-company'],
            'prefix' => 'api/v2',
            'as' => 'api.v2.',
        ], function () {
            foreach ($this->routeMappers as $routeMapper) {
                $routeMapper->map();
            }
        });
    }
}
