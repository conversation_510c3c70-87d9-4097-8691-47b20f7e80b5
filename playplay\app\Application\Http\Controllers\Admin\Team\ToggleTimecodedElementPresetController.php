<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Team;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\Team\TimecodedElement\ToggleTimecodedElementPresetRequest;
use App\Domain\TimecodedElement\ToggleTimecodedElementPresetService;
use App\Models\Team;
use App\Models\TimecodedElement\TimecodedElementPreset;
use Illuminate\Http\JsonResponse;

final class ToggleTimecodedElementPresetController extends BaseController
{
    private ToggleTimecodedElementPresetService $toggleTimecodedElementPresetService;

    public function __construct(ToggleTimecodedElementPresetService $toggleTimecodedElementPresetService)
    {
        $this->toggleTimecodedElementPresetService = $toggleTimecodedElementPresetService;
    }

    public function __invoke(
        Team $team,
        TimecodedElementPreset $timecodedElementPreset,
        ToggleTimecodedElementPresetRequest $request
    ): JsonResponse {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('update', $team);

        $this->toggleTimecodedElementPresetService->toggleTimecodedElementPreset(
            $team->id,
            $timecodedElementPreset->id,
            (bool) $request->get('is_enabled')
        );

        return new JsonResponse();
    }
}
