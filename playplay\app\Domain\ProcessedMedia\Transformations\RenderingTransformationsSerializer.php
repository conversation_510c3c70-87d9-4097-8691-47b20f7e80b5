<?php

declare(strict_types=1);

namespace App\Domain\ProcessedMedia\Transformations;

use App\Models\ProcessedMedia;

final class RenderingTransformationsSerializer implements TransformationsSerializer
{
    private LegacyRenderingTransformationsSerializer $legacySerializer;
    private SnapshotTransformationsSerializer $snapshotSerializer;

    public function __construct(
        LegacyRenderingTransformationsSerializer $legacyRenderingTransformationsSerializer,
        SnapshotTransformationsSerializer $snapshotTransformationsSerializer
    ) {
        $this->legacySerializer = $legacyRenderingTransformationsSerializer;
        $this->snapshotSerializer = $snapshotTransformationsSerializer;
    }

    public function serialize(ProcessedMedia $processedMedia): array
    {
        if ($processedMedia->rawMedia->isImage()) {
            return $this->snapshotSerializer->serialize($processedMedia);
        }

        return $this->legacySerializer->serialize($processedMedia);
    }
}
