<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\TeamPresets;

use Illuminate\Foundation\Http\FormRequest;

class TeamPresetsOutrosRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'outros.canUpload' => ['required', 'boolean'],
            'outros.values.*.square.project_media_id' => ['exists:processed_medias,id'],
            'outros.values.*.horizontal.project_media_id' => ['exists:processed_medias,id'],
            'outros.values.*.vertical.project_media_id' => ['exists:processed_medias,id'],
            'outros.values.*.file' => ['url'],
        ];
    }
}
