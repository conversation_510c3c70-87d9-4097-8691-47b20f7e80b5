<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\ProjectScreen;

use App\Application\Events\ProjectUpdated;
use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Project\ProjectScreen\ProjectScreenStoreRequest;
use App\Domain\ProjectScreen\ProjectScreenRepository;
use App\Domain\User\Action\UserAction;
use App\Domain\User\Action\UserActionService;
use App\Models\Project;
use App\Models\ProjectScreen;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;

final class CreateProjectScreenController extends BaseController
{
    use AuthorizesRequests;

    private Dispatcher $eventDispatcher;
    private UserActionService $userActionService;
    private ProjectScreenRepository $projectScreenRepository;

    public function __construct(
        Dispatcher $eventDispatcher,
        UserActionService $userActionService,
        ProjectScreenRepository $projectScreenRepository
    ) {
        $this->middleware('can:view,project');
        $this->eventDispatcher = $eventDispatcher;
        $this->userActionService = $userActionService;
        $this->projectScreenRepository = $projectScreenRepository;
    }

    public function __invoke(
        Project $project,
        ProjectScreenStoreRequest $request
    ): JsonResponse {
        $newProjectScreen = ProjectScreen::create([
            'project_id' => $project->id,
            'screen_id' => $request->get('screen_id'),
            'order' => $request->get('order'),
            'has_transition_after' => true,
        ]);

        // Reorder other screens
        $this->projectScreenRepository->incrementsOrderForProjectScreensOfProjectAboveProjectScreenOrder(
            $newProjectScreen
        );

        $this->eventDispatcher->dispatch(new ProjectUpdated($project));
        $this->userActionService->addUserAction(
            new UserAction(
                'project-screen-created',
                [],
                $project->team_id,
                $project->id,
            )
        );

        return $this->sendJsonResponse(new Collection([$newProjectScreen]), JsonResponse::HTTP_CREATED);
    }
}
