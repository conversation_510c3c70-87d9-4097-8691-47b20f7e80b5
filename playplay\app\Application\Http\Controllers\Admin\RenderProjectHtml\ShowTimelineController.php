<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\RenderProjectHtml;

use App\Application\Http\Controllers\Admin\AbstractRenderController;
use App\Domain\Render\RenderProject\DTO\TimelineDTO;
use App\Domain\Render\RenderProject\Serializer\TimelineSerializer;
use App\Models\Renders\RenderProjectHtml;
use Illuminate\Contracts\View\Factory as ViewFactory;
use Illuminate\Contracts\View\View;

final class ShowTimelineController extends AbstractRenderController
{
    private TimelineSerializer $timelineSerializer;
    private ViewFactory $viewFactory;

    public function __construct(
        TimelineSerializer $timelineSerializer,
        ViewFactory $viewFactory,
    ) {
        $this->timelineSerializer = $timelineSerializer;
        $this->viewFactory = $viewFactory;
    }

    public function __invoke(RenderProjectHtml $renderProjectHtml): View
    {
        $this->authorize('canAccessRestrictedData', $renderProjectHtml->project->company);

        return $this->viewFactory->make(
            'admin.renders.show-timeline',
            [
                'projectId' => $renderProjectHtml->project->id,
                'projectTitle' => $renderProjectHtml->project->title,
                'companyName' => $renderProjectHtml->project->company->name,
                'teamName' => $renderProjectHtml->project->team->name,
                'renderProjectHtmlId' => $renderProjectHtml->id,
                'renderProjectHtmlStatus' => $renderProjectHtml->status,
                'timeline' => $this->timelineSerializer->serialize(new TimelineDTO($renderProjectHtml)),
            ]
        );
    }
}
