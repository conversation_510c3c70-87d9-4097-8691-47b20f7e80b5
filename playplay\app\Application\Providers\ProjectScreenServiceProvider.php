<?php

namespace App\Application\Providers;

use App\Domain\ProjectScreen\GoogleProjectScreenRepository;
use App\Domain\ProjectScreen\GoogleProjectScreenService;
use App\Domain\ProjectScreen\ProjectScreenDeleteRepository;
use App\Domain\ProjectScreen\ProjectScreenRenderMediaFetcher as ProjectScreenRenderMediaFetcherInterface;
use App\Domain\ProjectScreen\ProjectScreenRepository;
use App\Domain\ProjectScreen\ProjectScreenService;
use App\Infrastructure\ProjectScreen\ProjectScreenRenderMediaFetcher;
use App\Infrastructure\ProjectScreen\Repositories\EloquentGoogleProjectScreenRepository;
use App\Infrastructure\ProjectScreen\Repositories\EloquentProjectScreenDeleteRepository;
use App\Infrastructure\ProjectScreen\Repositories\EloquentProjectScreenRepository;
use Illuminate\Support\ServiceProvider;

class ProjectScreenServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(ProjectScreenRepository::class, EloquentProjectScreenRepository::class);
        $this->app->bind(ProjectScreenDeleteRepository::class, EloquentProjectScreenDeleteRepository::class);
        /**
         * TODO: Delete this once we are able to manage custom transitions with custom duration
         */
        $this->app->bind(GoogleProjectScreenRepository::class, EloquentGoogleProjectScreenRepository::class);

        /**
         * TODO: Delete this once we are able to manage custom transitions with custom duration
         */
        $this->app->bind(ProjectScreenService::class, GoogleProjectScreenService::class);

        $this->app->bind(
            ProjectScreenRenderMediaFetcherInterface::class,
            ProjectScreenRenderMediaFetcher::class
        );
    }
}
