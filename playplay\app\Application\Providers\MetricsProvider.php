<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Metrics\BoostedClusterSizeCalculator;
use App\Domain\Metrics\Calculator;
use App\Domain\Metrics\ClusterSizeCalculator;
use App\Domain\Metrics\MetricsReader;
use App\Domain\Metrics\MetricsRepository;
use App\Domain\Metrics\MetricsPublisher;
use App\Infrastructure\Metrics\EloquentMetricsRepository;
use App\Infrastructure\Metrics\GoogleMetricsPublisher;
use App\Infrastructure\Metrics\PrometheusMetricsPublisher;
use App\Infrastructure\Metrics\PrometheusMetricsReader;
use Illuminate\Contracts\Cache\Repository as CacheRepository;
use Illuminate\Foundation\Application;
use Illuminate\Support\ServiceProvider;
use Prometheus\CollectorRegistry;
use Prometheus\RegistryInterface;
use Prometheus\RendererInterface;
use Prometheus\RenderTextFormat;
use Prometheus\Storage\Adapter;
use Prometheus\Storage\InMemory;

final class MetricsProvider extends ServiceProvider
{
    public function register(): void
    {
        if ($this->isCliInterface()) {
            $this->app->bind(MetricsPublisher::class, GoogleMetricsPublisher::class);
        } else {
            $this->app->bind(MetricsPublisher::class, PrometheusMetricsPublisher::class);
        }

        //Bindings for prometheus usage
        $this->app->bind(RegistryInterface::class, CollectorRegistry::class);
        $this->app->bind(RendererInterface::class, RenderTextFormat::class);
        $this->app->singleton(Adapter::class, InMemory::class);

        $this->app->bind(MetricsReader::class, PrometheusMetricsReader::class);
        $this->app->bind(MetricsRepository::class, EloquentMetricsRepository::class);
        $this->app->bind(Calculator::class, function (Application $app) {
            return new BoostedClusterSizeCalculator(new ClusterSizeCalculator(), $app[CacheRepository::class]);
        });
    }

    private function isCliInterface(): bool
    {
        return php_sapi_name() === 'cli';
    }
}
