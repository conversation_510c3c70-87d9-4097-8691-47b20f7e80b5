<?php

declare(strict_types=1);

namespace App\Domain\Preview;

use App\Domain\Audio\FilterApplier as AudioFilterApplier;
use App\Domain\ProcessedMedia\Repository\ProcessedMediaRepository;
use App\Domain\Project\ProcessedMedia\RelationType;
use App\Domain\Project\ProjectRepository;
use App\Domain\RawMedia\RawMediaRepository;
use App\Domain\Render\RenderMedia\RenderMediaRepository;
use App\Models\ProcessedMedia;
use App\Models\Project;
use App\Models\RawMedia;
use App\Models\Snapshot;
use App\Services\ToQueueHtmlService;
use Illuminate\Contracts\Auth\Access\Gate;

class ProjectSnapshotFactory
{
    private ProcessedMediaRepository $processedMediaRepository;
    private SnapshotRepository $snapshotRepository;
    private ToQueueHtmlService $toQueueHtmlService;
    private Gate $gate;
    private AudioFilterApplier $audioFilterApplier;
    private RawMediaRepository $rawMediaRepository;
    private RenderMediaRepository $renderMediaRepository;
    private ProjectRepository $projectRepository;

    public function __construct(
        ProcessedMediaRepository $processedMediaRepository,
        SnapshotRepository $snapshotRepository,
        ToQueueHtmlService $toQueueHtmlService,
        Gate $gate,
        AudioFilterApplier $audioFilterApplier,
        RawMediaRepository $rawMediaRepository,
        RenderMediaRepository $renderMediaRepository,
        ProjectRepository $projectRepository
    ) {
        $this->processedMediaRepository = $processedMediaRepository;
        $this->snapshotRepository = $snapshotRepository;
        $this->toQueueHtmlService = $toQueueHtmlService;
        $this->gate = $gate;
        $this->audioFilterApplier = $audioFilterApplier;
        $this->rawMediaRepository = $rawMediaRepository;
        $this->renderMediaRepository = $renderMediaRepository;
        $this->projectRepository = $projectRepository;
    }

    public function generate(Project $project): Snapshot
    {
        $this->gate->authorize('create', [Snapshot::class, $project]);

        $snapshot = $this->snapshotRepository->createSnapshotForProject(
            $project,
            $this->toQueueHtmlService->serializeProject($project)
        );

        $this->snapshotRepository->attachRenderMedias(
            $snapshot,
            $this->getRenderMediaIdsLinked($project)
        );

        $snapshot = $this->snapshotRepository->updateStatusFromRenderMediasStatuses($snapshot);

        $audioRenderMedias = $this->processedMediaRepository->getByProjectIdAndRelationTypes(
            $project->id,
            [
                RelationType::VOICEOVER,
                RelationType::MUSIC,
            ],
            ['lastRender']
        )
            ->pluck('lastRender')
            ->all();

        $this->audioFilterApplier->applyTrims($audioRenderMedias);
        $project->timestamps = false;
        $this->projectRepository->update($project, ['snapshot_up_to_date' => true]);

        return $snapshot;
    }

    /**
     * @return int[]
     */
    private function getRenderMediaIdsLinked(Project $project): array
    {
        $processedMediaIds = [
            ...$this->processedMediaRepository->getIdsOfLogosOfAProject($project),
            ...$this->processedMediaRepository
                ->getAllIdsByProjectExceptProcessedMediaUsedInMediaParamsAndCutawayShots($project),
        ];

        $renderMediasIds = [];
        if ($processedMediaIds !== []) {
            $renderMediasIds = array_merge(
                $renderMediasIds,
                $this->renderMediaRepository
                    ->getMaxIdsByParentIdAndParentType($processedMediaIds, ProcessedMedia::DEPRECATED_CLASS)
            );
        }

        $rawMediaIds = $this->rawMediaRepository->getIdsOfCutawayShotsAndMediasUsedInProject($project);
        if ($rawMediaIds !== []) {
            $renderMediasIds = array_merge(
                $renderMediasIds,
                $this->renderMediaRepository
                    ->getMaxIdsByParentIdAndParentType($rawMediaIds, RawMedia::DEPRECATED_CLASS)
            );
        }

        return $renderMediasIds;
    }
}
