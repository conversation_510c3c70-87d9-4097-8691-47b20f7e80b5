<?php

namespace App\Application\Http\Requests\Api\Boost;

use Illuminate\Foundation\Http\FormRequest;

class BoostRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'seconds' => ['integer', 'min:1', 'max:2400'],
            'cluster_size' => ['integer', 'min:1', 'max:10'],
        ];
    }
}
