<?php

declare(strict_types=1);

namespace App\Domain\Churn;

use DateInterval;
use DateTimeImmutable;
use DateTimeInterface;

final class SubscriptionEndDateCalculator
{
    public function calculateFromDate(DateTimeInterface $date): DateTimeInterface
    {
        $today = new DateTimeImmutable();
        $endDate = (new DateTimeImmutable())->setTimestamp($date->getTimestamp());

        if ($endDate > $today) {
            return $endDate;
        }

        $dateDifference = $endDate->diff($today);

        if ($dateDifference->y > 0) {
            $endDate = $endDate->add(new DateInterval("P{$dateDifference->y}Y"));
        }

        if ($dateDifference->m > 0) {
            $monthsToAdd = $dateDifference->m + 1;
            $endDate = $endDate->add(new DateInterval("P{$monthsToAdd}M"));
        }

        if ($this->dateDifferenceIsLessThanAMonth($dateDifference)) {
            $endDate = $endDate->add(new DateInterval('P1M'));
        }

        if ($this->isToday($endDate, $today)) {
            $endDate = $endDate->add(new DateInterval('P1M'));
        }

        if ($endDate < $today) {
            $endDate = $endDate->add(new DateInterval('P1M'));
        }

        return $endDate;
    }

    private function dateDifferenceIsLessThanAMonth(DateInterval $dateDifference): bool
    {
        return $dateDifference->y === 0 && $dateDifference->m === 0;
    }

    private function isToday(DateTimeInterface $suggestedEndDate, DateTimeInterface $today): bool
    {
        return $suggestedEndDate->format('d-m-Y') === $today->format('d-m-Y');
    }
}
