<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\TimecodedElement;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\TimecodedElement\TimecodedElementSwitchPresetRequest;
use App\Domain\TimecodedElement\Repositories\TimecodedElementPresetRepository;
use App\Domain\TimecodedElement\Serializers\TimecodedElementsSerializer;
use App\Domain\TimecodedElement\TimecodedElementSwitchPresetService;
use App\Models\ProjectScreen;
use App\Models\TimecodedElement\TimecodedElement;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

final class TimecodedElementSwitchPresetController extends BaseController
{
    use AuthorizesRequests;

    private TimecodedElementSwitchPresetService $timecodedElementSwitchPresetService;
    private TimecodedElementPresetRepository $timecodedElementPresetRepository;
    private TimecodedElementsSerializer $timecodedElementsSerializer;

    public function __construct(
        TimecodedElementSwitchPresetService $timecodedElementSwitchPresetService,
        TimecodedElementPresetRepository $timecodedElementPresetRepository,
        TimecodedElementsSerializer $timecodedElementsSerializer
    ) {
        $this->timecodedElementSwitchPresetService = $timecodedElementSwitchPresetService;
        $this->timecodedElementPresetRepository = $timecodedElementPresetRepository;
        $this->timecodedElementsSerializer = $timecodedElementsSerializer;
    }

    /**
     * @throws AuthorizationException|ValidationException|ModelNotFoundException
     */
    public function __invoke(
        ProjectScreen $projectScreen,
        TimecodedElement $timecodedElement,
        TimecodedElementSwitchPresetRequest $request
    ): JsonResponse {
        $targetTimecodedElementPresetId = $request->get('timecoded_element_preset_id');
        $this->authorize('switch-preset', [
            $timecodedElement,
            $projectScreen,
            $targetTimecodedElementPresetId
        ]);

        $targetTimecodedElementPreset = $this->timecodedElementPresetRepository->getById(
            $targetTimecodedElementPresetId
        );
        if ($targetTimecodedElementPreset === null) {
            throw new ModelNotFoundException(
                "TimecodedElementPreset $targetTimecodedElementPresetId does not exist"
            );
        }

        if (!$this->timecodedElementSwitchPresetService->areCompatiblePresets(
            $timecodedElement->timecodedElementPreset,
            $targetTimecodedElementPreset
        )) {
            throw ValidationException::withMessages([
                'validation.timecoded_element_switch_presets_family_types_do_not_match'
            ]);
        }

        $timecodedElement = $this->timecodedElementSwitchPresetService->switchPreset(
            $timecodedElement,
            $targetTimecodedElementPreset
        );

        return $this->sendJsonResponse(
            new Collection($this->timecodedElementsSerializer->serialize([$timecodedElement])),
            Response::HTTP_OK
        );
    }
}
