<?php

declare(strict_types=1);

namespace App\Domain\Preview;

use App\Domain\RawMedia\RawMediaType;
use App\Domain\Render\RenderMedia\CropEnricher;
use App\Models\Renders\RenderMedia;
use Illuminate\Support\Collection;

/**
 * This class duplicate App\Domain\Render\RenderMedia\RenderMediaEnricher
 * in most part. We don't want to make an abstract class at the moment
 * because it's just an iteration on Snapshot refactoring
 * The only diff between theses two classes at the moment are the source of media url
 */
final class RenderMediaEnricher
{
    private CropEnricher $cropEnricher;

    public function __construct(CropEnricher $cropEnricher)
    {
        $this->cropEnricher = $cropEnricher;
    }

    public function enrichMediaData(array $media, RenderMedia $renderMedia): array
    {
        return match ($media['type']) {
            RawMediaType::AUDIO => $this->fillAudioMissingData($media, $renderMedia),
            RawMediaType::VIDEO => $this->fillVideoMissingData($media, $renderMedia),
            default => $this->fillDefaultMissingData($media, $renderMedia)
        };
    }

    public function enrichLogo(array $data, Collection $renderMedias): array
    {
        foreach ($data['screens'] as $index => $screen) {
            foreach ($screen['options']['logos'] ?? [] as $indexLogo => $logo) {
                if (null === $logo) {
                    continue;
                }

                $data['screens'][$index]['options']['logos'][$indexLogo]['url'] =
                    $renderMedias->get($logo['render_media_id'])->rendered_url ?? null;
            }
        }

        return $data;
    }

    public function enrichVoiceover(array $data, Collection $renderMedias): array
    {
        foreach ($data['voiceover']['items'] ?? [] as $index => $audio) {
            $data['voiceover']['items'][$index]['url'] =
                $renderMedias->get($audio['render_media_id'])->thumbnail_url ?? null;
        }

        return $data;
    }

    public function enrichMusic(array $data, Collection $renderMedias): array
    {
        foreach ($data['music']['items'] ?? [] as $index => $audio) {
            $data['music']['items'][$index]['url'] =
                $renderMedias->get($audio['render_media_id'])->thumbnail_url ?? null;
        }

        return $data;
    }

    private function fillAudioMissingData(array $media, RenderMedia $renderMedia): array
    {
        // For audios the right url is under thumbnail_url
        $media['url'] = $renderMedia->thumbnail_url;
        $media['peaks'] = $renderMedia->audio_peaks_data_url;

        return $media;
    }

    private function fillDefaultMissingData(array $media, RenderMedia $renderMedia): array
    {
        $isParamLogo = !array_key_exists('transformations', $media);
        if ($isParamLogo) {
            return $this->fillParamLogoMissingData($media, $renderMedia);
        }

        return $this->fillImageMissingData($media, $renderMedia);
    }

    private function fillImageMissingData(array $media, RenderMedia $renderMedia): array
    {
        $media = $this->cropEnricher->fillCrop($media, $renderMedia);
        return $this->fillUrlWithRawMediaLastRenderRenderedUrl($media, $renderMedia);
    }

    private function fillVideoMissingData(array $media, RenderMedia $renderMedia): array
    {
        $media = $this->cropEnricher->fillCrop($media, $renderMedia);
        return $this->fillUrlWithRawMediaLastRenderRenderedUrl($media, $renderMedia);
    }

    private function fillParamLogoMissingData(array $media, RenderMedia $renderMedia): array
    {
        return $this->fillUrlWithProcessedLastRenderRenderedUrl($media, $renderMedia);
    }

    private function fillUrlWithRawMediaLastRenderRenderedUrl(array $media, RenderMedia $renderMedia): array
    {
        if ($media['url'] === null) {
            $media['url'] = $renderMedia->parent->rawMedia->lastRender?->rendered_url;
        }

        return $media;
    }

    private function fillUrlWithProcessedLastRenderRenderedUrl(array $media, RenderMedia $renderMedia): array
    {
        if ($media['url'] === null) {
            $media['url'] = $renderMedia->rendered_url;
        }

        return $media;
    }
}
