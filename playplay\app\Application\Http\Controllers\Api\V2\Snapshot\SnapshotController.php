<?php

namespace App\Application\Http\Controllers\Api\V2\Snapshot;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Preview\SnapshotStoreRequest;
use App\Domain\Preview\SnapshotStrategy;
use App\Models\Snapshot;
use Illuminate\Contracts\Auth\Access\Gate;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class SnapshotController extends BaseController
{
    use AuthorizesRequests;

    private SnapshotStrategy $snapshotStrategy;
    private Gate $gate;

    public function __construct(SnapshotStrategy $snapshotStrategy, Gate $gate)
    {
        $this->snapshotStrategy = $snapshotStrategy;
        $this->gate = $gate;
    }

    public function show(Snapshot $snapshot): JsonResponse
    {
        $this->gate->authorize('view', $snapshot);

        return $this->sendJsonResponse(new Collection([$snapshot]), Response::HTTP_OK);
    }

    public function store(SnapshotStoreRequest $request): JsonResponse
    {
        $parentId = (int) $request->get('parent_id');
        $parentType = $request->get('parent_type');

        $snapshot = $this->snapshotStrategy->execute($parentType, $parentId);

        return $this->sendJsonResponse(new Collection([$snapshot]), Response::HTTP_CREATED);
    }
}
