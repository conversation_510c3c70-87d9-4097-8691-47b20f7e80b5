<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\V2\ShareableLink\ShareableLinkSettingsController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class ShareableLinkSettingsRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([], function (Router $router) {
            $this->mapCreate($router);
        });
    }

    private function mapCreate(Router $router): void
    {
        $router->post('/projects/{project}/shareable-link', [ShareableLinkSettingsController::class, 'getOrCreate'])
            ->name('projects.shareable-link');
        $router->put('/shareable-links/{shareable_link}/settings', [ShareableLinkSettingsController::class, 'update'])
            ->name('shareable-links.update');
    }
}
