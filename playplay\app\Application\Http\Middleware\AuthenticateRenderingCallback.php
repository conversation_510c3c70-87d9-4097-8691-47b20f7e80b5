<?php

namespace App\Application\Http\Middleware;

use Closure;
use Illuminate\Config\Repository as ConfigRepository;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;

class AuthenticateRenderingCallback
{
    private ConfigRepository $configRepository;

    public function __construct(ConfigRepository $configRepository)
    {
        $this->configRepository = $configRepository;
    }

    public function handle(Request $request, Closure $next)
    {
        $authorizationHeader = $request->header('Authorization', '');

        if ($authorizationHeader !== '') {
            [$authenticationType, $base64AuthorizationKey] = explode(' ', $authorizationHeader);

            if ($authenticationType === 'Basic') {
                $decodedAuthorizationKey = base64_decode($base64AuthorizationKey);
                [$username, $password] = explode(':', $decodedAuthorizationKey);

                if ($username === $this->configRepository->get('app.rendering.username')
                    && $password === $this->configRepository->get('app.rendering.password')
                ) {
                    return $next($request);
                }
            }
        }

        throw new UnauthorizedHttpException('unauthorized');
    }
}
