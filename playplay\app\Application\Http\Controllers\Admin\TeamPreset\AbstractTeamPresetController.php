<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\TeamPreset;

use App\Application\Http\Controllers\Admin\BaseController;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;

abstract class AbstractTeamPresetController extends BaseController
{
    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            $team = $request->route('team');
            $teamPreset = $request->route('teamPreset');

            if (!$team->presets()->where('id', $teamPreset->id)->exists()) {
                throw new UnauthorizedHttpException('Unauthorized');
            }

            return $next($request);
        });
    }
}
