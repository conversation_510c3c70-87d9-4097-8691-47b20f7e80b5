<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\Project;

use App\Models\ProjectScreenParam;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Factory;

final class RenderSubtitleStoreRequest extends FormRequest
{
    private const SPEECH_TO_TEXT_SUPPORTED_LANGUAGES = [
        'af-ZA',
        'am-ET',
        'ar-AE',
        'ar-BH',
        'ar-DZ',
        'ar-EG',
        'ar-IL',
        'ar-JO',
        'ar-KW',
        'ar-LB',
        'ar-MA',
        'ar-MR',
        'ar-OM',
        'ar-PS',
        'ar-QA',
        'ar-SA',
        'ar-TN',
        'az-AZ',
        'bg-BG',
        'bn-BD',
        'bn-IN',
        'bs-BA',
        'ca-ES',
        'cs-CZ',
        'da-DK',
        'de-ch',
        'de-DE',
        'el-GR',
        'en-AU',
        'en-CA',
        'en-GB',
        'en-GH',
        'en-HK',
        'en-IE',
        'en-IN',
        'en-KE',
        'en-NG',
        'en-NZ',
        'en-PH',
        'en-PK',
        'en-SG',
        'en-TZ',
        'en-US',
        'en-ZA',
        'es-AR',
        'es-BO',
        'es-CL',
        'es-CO',
        'es-CR',
        'es-DO',
        'es-ES',
        'es-GT',
        'es-HN',
        'es-MX',
        'es-NI',
        'es-PA',
        'es-PE',
        'es-PR',
        'es-PY',
        'es-SV',
        'es-US',
        'es-UY',
        'es-VE',
        'et-EE',
        'eu-ES',
        'fa-IR',
        'fi-FI',
        'fil-PH',
        'fr-BE',
        'fr-CA',
        'fr-CH',
        'fr-FR',
        'gl-ES',
        'gu-IN',
        'hi-IN',
        'hr-HR',
        'hu-HU',
        'hy-AM',
        'id-ID',
        'is-IS',
        'it-IT',
        'iw-IL',
        'ja-JP',
        'jv-ID',
        'ka-GE',
        'kk-KZ',
        'km-KH',
        'kn-IN',
        'ko-KR',
        'lo-LA',
        'lt-LT',
        'lv-LV',
        'ml-IN',
        'mn-MN',
        'mr-IN',
        'ms-MY',
        'my-MM',
        'ne-NP',
        'nl-BE',
        'nl-NL',
        'pa-Guru-IN',
        'pl-PL',
        'pt-BR',
        'pt-PT',
        'ro-RO',
        'ru-RU',
        'si-LK',
        'sk-SK',
        'sl-SI',
        'sr-RS',
        'su-ID',
        'sv-SE',
        'sw-KE',
        'sw-TZ',
        'ta-IN',
        'ta-LK',
        'ta-MY',
        'ta-SG',
        'te-IN',
        'th-TH',
        'tr-TR',
        'uk-UA',
        'ur-IN',
        'ur-PK',
        'uz-UZ',
        'vi-VN',
        'zh',
        'zh-TW',
        'zu-ZA',
    ];

    private const TRANSLATION_SUPPORTED_LANGUAGES = [
        'af',
        'am',
        'ar',
        'az',
        'be',
        'bg',
        'bn',
        'bs',
        'ca',
        'ceb',
        'co',
        'cs',
        'cy',
        'da',
        'de',
        'el',
        'en',
        'eo',
        'es',
        'et',
        'eu',
        'fa',
        'fi',
        'fil',
        'fr',
        'fy',
        'ga',
        'gd',
        'gl',
        'gu',
        'ha',
        'haw',
        'hi',
        'hmn',
        'hr',
        'ht',
        'hu',
        'hy',
        'id',
        'ig',
        'is',
        'it',
        'iw',
        'ja',
        'jv',
        'ka',
        'kk',
        'km',
        'kn',
        'ko',
        'ku',
        'ky',
        'la',
        'lb',
        'lo',
        'lt',
        'lv',
        'mg',
        'mi',
        'mk',
        'ml',
        'mn',
        'mr',
        'ms',
        'mt',
        'my',
        'ne',
        'nl',
        'no',
        'ny',
        'or',
        'pa',
        'pl',
        'ps',
        'pt',
        'ro',
        'ru',
        'rw',
        'sd',
        'si',
        'sk',
        'sl',
        'sm',
        'sn',
        'so',
        'sq',
        'sr',
        'st',
        'su',
        'sv',
        'sw',
        'ta',
        'te',
        'tg',
        'th',
        'tk',
        'tl',
        'tr',
        'tt',
        'ug',
        'uk',
        'ur',
        'uz',
        'vi',
        'xh',
        'yi',
        'yo',
        'zh',
        'zh-TW',
        'zu',
    ];

    private Factory $validationFactory;

    public function __construct(Factory $validationFactory)
    {
        $this->validationFactory = $validationFactory;
        $this->checkIsInSubtitleScreen();
        $this->checkMediaHasAudio();
        $this->checkMediaHasNoRenderSubtitle();
        $this->checkProjectScreenParamIsNotEmpty();
        parent::__construct();
    }

    public function rules(): array
    {
        $speechToTextSupportedLanguages = implode(',', self::SPEECH_TO_TEXT_SUPPORTED_LANGUAGES);
        $translationSupportedLanguages = implode(',', self::TRANSLATION_SUPPORTED_LANGUAGES);

        return [
            'lang' => ['bail', 'required', "in:{$speechToTextSupportedLanguages}"],
            'target_language' => ['sometimes', "in:{$translationSupportedLanguages}"],
            'project_screen_param_id' => [
                'bail',
                'required',
                'exists:project_screen_params,id',
                'isInSubtitleScreen',
                'hasAudio',
                'hasNoRenderSubtitle',
                'isNotEmpty',
            ],
        ];
    }

    private function checkIsInSubtitleScreen(): void
    {
        $this->validationFactory->extendImplicit(
            'isInSubtitleScreen',
            function ($attribute, $projectScreenParamId) {
                // TODO move into a repository
                /** @var ProjectScreenParam|null $projectScreenParam */
                $projectScreenParam = ProjectScreenParam::find($projectScreenParamId);

                return $projectScreenParam?->screen?->hasParamSubtitle();
            }
        );
    }

    private function checkMediaHasAudio(): void
    {
        $this->validationFactory->extendImplicit(
            'hasAudio',
            function ($attribute, $projectScreenParamId) {
                return optional(ProjectScreenParam::find($projectScreenParamId)->processedMedia)->rawMedia->hasAudio();
            }
        );
    }

    private function checkMediaHasNoRenderSubtitle(): void
    {
        $this->validationFactory->extendImplicit(
            'hasNoRenderSubtitle',
            function ($attribute, $projectScreenParamId) {
                return !optional(
                    ProjectScreenParam::find($projectScreenParamId)->processedMedia->lastRender->renderSubtitle()
                        ->isNotFinished()
                )->exists();
            }
        );
    }

    private function checkProjectScreenParamIsNotEmpty(): void
    {
        $this->validationFactory->extendImplicit(
            'isNotEmpty',
            function ($attribute, $projectScreenParamId) {
                return (bool) ProjectScreenParam::find($projectScreenParamId)->value_id;
            }
        );
    }
}
