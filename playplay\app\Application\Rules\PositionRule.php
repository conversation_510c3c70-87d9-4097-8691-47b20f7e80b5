<?php

namespace App\Application\Rules;

use Illuminate\Contracts\Validation\Rule;

class PositionRule implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('validation.position');
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed  $value
     *
     * @return bool
     */
    public function passes($attribute, $value)
    {
        return preg_match('/^\((\(\d+\,\d+\)\,){2}\(\d+\,\d+\)\)$/m', $value) === 1;
    }
}
