<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V3\Font;

use App\Application\Http\Controllers\Api\BaseController;
use App\Domain\Font\FontRepository;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

final class GetBasicFontsController extends BaseController
{
    private FontRepository $fontRepository;

    public function __construct(FontRepository $fontRepository)
    {
        $this->fontRepository = $fontRepository;
    }

    public function __invoke(): JsonResponse
    {
        return $this->sendJsonResponse($this->fontRepository->getBasicFonts(), Response::HTTP_OK);
    }
}
