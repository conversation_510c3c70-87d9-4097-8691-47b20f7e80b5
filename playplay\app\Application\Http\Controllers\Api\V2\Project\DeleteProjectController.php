<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Project;

use App\Application\Http\Controllers\Api\BaseController;
use App\Models\Project;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class DeleteProjectController extends BaseController
{
    use AuthorizesRequests;

    public function __invoke(Project $project): JsonResponse
    {
        $this->authorize('destroy', $project);

        $project->delete();

        return $this->sendJsonResponse(new Collection([]), Response::HTTP_NO_CONTENT);
    }
}
