<?php

namespace App\Application\Rules;

use App\Models\ProcessedMedia;
use App\Models\ScreenParams\BaseParam;
use Illuminate\Contracts\Validation\Rule;

class CheckParamAreValidRule implements Rule
{
    public function passes($attribute, $value)
    {
        $params = BaseParam::whereIn('id', (data_get($value, '*.param_id')))
            ->get()
            ->keyBy('id');
        foreach ($value as $valueParam) {
            $paramId = data_get($valueParam, 'param_id');
            if (!$paramId || !$param = $params->get($paramId)) {
                return false;
            }

            $value = data_get($valueParam, 'value', []);
            if (!$this->checkValueFormat($value, $param)) {
                return false;
            }
        }

        return true;
    }

    public function message()
    {
        return 'CheckParamAreValidRule is not valid';
    }

    private function checkValueFormat($value, BaseParam $param)
    {
        // Media param format validation
        switch ($param->type) {
            case BaseParam::TYPE_MEDIA:
            case BaseParam::TYPE_CUTAWAY_SHOT:
                foreach ($value as $mediaValue) {
                    if (!\array_key_exists('value', $mediaValue) || !\array_key_exists('options', $mediaValue)) {
                        return false;
                    }
                }
                break;
            case BaseParam::TYPE_LOGO:
                // null is used to delete a media
                if (!$value) {
                    return true;
                }

                $media = ProcessedMedia::find($value);

                return $media !== null;
            case BaseParam::TYPE_TEXTAREA:
                return is_null($value)
                    || (is_array($value) && data_get($value, 'ops') && collect($value)->pluck('insert')->count() > 0);
            default:
                return true;
        }

        return true;
    }
}
