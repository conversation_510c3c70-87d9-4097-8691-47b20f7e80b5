<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Screen;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\Screen\Version\ScreenVersionBulkUpdateRequest;
use App\Application\Http\Requests\Admin\Screen\Version\ScreenVersionIndexRequest;
use App\Domain\Screen\ScreenRepository;
use App\Models\Screen;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\View\Factory as ViewFactory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Redirector;

final class ScreenVersionController extends BaseController
{
    private ViewFactory $viewFactory;
    private ScreenRepository $screenRepository;
    private Redirector $redirectService;

    public function __construct(
        ScreenRepository $screenRepository,
        ViewFactory $viewFactory,
        Redirector $redirectService
    ) {
        $this->screenRepository = $screenRepository;
        $this->viewFactory = $viewFactory;
        $this->redirectService = $redirectService;
    }

    /**
     * @throws AuthorizationException
     */
    public function index(ScreenVersionIndexRequest $request): View
    {
        $this->authorize('incrementScreenVersion', Screen::class);

        $viewIsGeneric = (bool) $request->get('is_generic', true);
        $screensPaginator = $this->screenRepository->getPaginatedScreensOrderedById($viewIsGeneric, true);
        $screensPaginator->appends(['is_generic' => $viewIsGeneric]);

        return $this->viewFactory->make('admin.screens.versions', [
            'viewIsGeneric' => $viewIsGeneric,
            'screens' => $screensPaginator,
        ]);
    }

    /**
     * @throws AuthorizationException
     */
    public function bulkUpdate(ScreenVersionBulkUpdateRequest $request): RedirectResponse
    {
        $this->authorize('incrementScreenVersion', Screen::class);

        $screenIdsToUpdate = $request->get('screen_ids');
        $this->screenRepository->incrementVersionsByIds($screenIdsToUpdate);

        return $this->redirectService->back();
    }
}
