<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin;

use App\Application\Http\Mappers\UserTeam\UserAppRoleMapper;
use App\Application\Http\Requests\Admin\Team\TeamRequest;
use App\Domain\Common\Exceptions\EntityNotFoundException;
use App\Domain\Company\Repositories\CompanyRepository;
use App\Domain\Permissions\AppRoleRepository;
use App\Domain\Team\TeamFactory;
use App\Domain\UserTeam\Serializer\UserTeamAppRoleSerializer;
use App\Domain\UserTeam\UserTeamService;
use App\Models\Category;
use App\Models\Feature;
use App\Models\Project;
use App\Models\Team;
use App\Services\TeamService;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

final class TeamController extends BaseController
{
    private TeamFactory $teamFactory;
    private TeamService $teamService;
    private AppRoleRepository $appRoleRepository;
    private CompanyRepository $companyRepository;
    private UserAppRoleMapper $userAppRoleMapper;
    private UserTeamService $userTeamService;
    private UserTeamAppRoleSerializer $userTeamAppRoleSerializer;
    private Factory $viewFactory;

    public function __construct(
        TeamFactory $teamFactory,
        TeamService $teamService,
        AppRoleRepository $appRoleRepository,
        CompanyRepository $companyRepository,
        UserAppRoleMapper $userAppRoleMapper,
        UserTeamService $userTeamService,
        UserTeamAppRoleSerializer $userTeamAppRoleSerializer,
        Factory $viewFactory,
    ) {
        $this->teamFactory = $teamFactory;
        $this->teamService = $teamService;
        $this->appRoleRepository = $appRoleRepository;
        $this->companyRepository = $companyRepository;
        $this->userAppRoleMapper = $userAppRoleMapper;
        $this->userTeamService = $userTeamService;
        $this->userTeamAppRoleSerializer = $userTeamAppRoleSerializer;
        $this->viewFactory = $viewFactory;
    }

    public function create(Request $request)
    {
        try {
            $company = $this->companyRepository->getById((int) $request->get('company'));
        } catch (EntityNotFoundException) {
            return redirect(route('admin.companies.index'));
        }

        $this->authorize('canAccessRestrictedData', $company);
        $this->authorize('create', Team::class);

        return view('admin.teams.create', compact('company'));
    }

    public function destroy(Team $team): RedirectResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('delete', $team);

        $team->delete();

        return redirect()->route('admin.companies.show', $team->company_id);
    }

    public function disableAutoAddGenericScreens(Team $team): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('update', $team);

        $team->update(['auto_add_new_generic_screens' => false]);

        return new JsonResponse(['status' => 'ok'], Response::HTTP_OK);
    }

    public function disableAutoAddGenericTemplateCategories(Team $team): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('update', $team);

        $team->update(['auto_add_new_generic_template_categories' => false]);

        return new JsonResponse(['status' => 'ok'], Response::HTTP_OK);
    }

    public function duplicate(Team $team): RedirectResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('create', $team);

        $newTeam = $this->teamService->duplicateWithPresetsCategoriesAndFeatures($team);

        return redirect()->route('admin.teams.edit', $newTeam);
    }

    public function edit(Team $team): View
    {
        if ($team->company === null || $team->company->deleted_at !== null) {
            throw new NotFoundHttpException();
        }

        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('update', $team);

        $tabActive = "general";

        $allUsers = $team->company->users()->get()->pluck('name', 'id')->toArray();
        $users = $team->users;
        $categories = Category::all()->pluck('backoffice_name', 'id')->toArray();
        $features = Feature::for('team')->get();
        $roles = $this->appRoleRepository->getAllAvailableAppRolesWithAppPermissions();

        return $this->viewFactory->make(
            'admin.teams.edit',
            [
                'tabActive' => $tabActive,
                'team' => $team,
                'users' => $users,
                'allUsers' => $allUsers,
                'categories' => $categories,
                'features' => $features,
                'roles' => $roles,
                'serializedUserTeamAppRoles' => $this->userTeamAppRoleSerializer->serialize($team->userTeamAppRoles),
            ]
        );
    }

    public function enableAutoAddGenericScreens(Team $team): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('update', $team);

        $team->update(['auto_add_new_generic_screens' => true]);

        return new JsonResponse(['status' => 'ok'], Response::HTTP_OK);
    }

    public function enableAutoAddGenericTemplateCategories(Team $team): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('update', $team);

        $team->update(['auto_add_new_generic_template_categories' => true]);

        return new JsonResponse(['status' => 'ok'], Response::HTTP_OK);
    }

    public function filters(Request $request): JsonResponse
    {
        $term = $request->get('term');
        $companyId = $request->get('company_id');
        $teams = Team::select('id', 'name AS text')
            ->when($companyId, function (Builder $query) use ($companyId) {
                return $query->where('company_id', $companyId);
            })
            ->when($term, function (Builder $query) use ($term) {
                return $query->where('name', 'LIKE', "%{$term}%");
            })
            ->simplePaginate();

        return new JsonResponse(['success' => true, 'results' => $teams], JsonResponse::HTTP_OK);
    }

    public function store(TeamRequest $request): JsonResponse
    {
        $companyId = (int) $request->get('company_id');
        $company = $this->companyRepository->getById($companyId);

        $this->authorize('canAccessRestrictedData', $company);
        $this->authorize('create', Team::class);

        $team = $this->teamFactory->create(
            $request->get('name'),
            $companyId,
            $request->boolean('is_arabic', false)
        );

        return new JsonResponse(
            ['success' => true, 'redirect_uri' => route('admin.teams.edit', $team->id)],
            Response::HTTP_CREATED
        );
    }

    public function update(TeamRequest $request, Team $team): RedirectResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('update', $team);

        if ($request->has('company_id')) {
            DB::transaction(function () use ($team, $request) {
                $team->users()->toBase()->update([
                    'company_id' => $request->get('company_id'),
                ]);
                Project::whereTeamId($team->id)->update([
                    'company_id' => $request->get('company_id'),
                ]);
            });

            $team->company_id = $request->get('company_id');
        } else {
            $team->name = $request->get('name');
            $team->is_arabic = $request->get('is_arabic', false);

            $updatedTeamUsers = $request->get('users', []);
            $currentTeamUsers = $team->users;

            $detachedUsers = $currentTeamUsers->filter(function ($user) use ($updatedTeamUsers) {
                return !array_key_exists($user->id, $updatedTeamUsers);
            });

            $this->userTeamService->setUsersWithAppRolesToTeam($team, $this->userAppRoleMapper->fromRequest($request));

            foreach ($detachedUsers as $detachedUser) {
                if ($detachedUser->teams->count() === 0) {
                    $detachedUser->delete();
                }
            }

            $team->features()->sync(
                array_map(function ($value) {
                    return ['value' => $value === '' ? null : $value];
                }, $request->get('features', []))
            );
        }

        $team->save();

        return redirect()->back();
    }
}
