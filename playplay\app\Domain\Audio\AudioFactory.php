<?php

namespace App\Domain\Audio;

use App\Domain\Processing\RenderMediaFactory;
use App\Domain\Project\ProcessedMedia\RelationType;
use App\Models\ProcessedMedia;
use App\Models\Project;
use App\Services\ProcessedMedia\Factory as ProcessedMediaFactory;

class AudioFactory
{
    private ProcessedMediaFactory $processedMediaFactory;
    private RenderMediaFactory $renderMediaFactory;
    private ProjectSettingService $projectSettingService;
    private TimelineParameterService $timelineParameterService;

    public function __construct(
        ProjectSettingService $projectSettingService,
        ProcessedMediaFactory $processedMediaFactory,
        RenderMediaFactory $renderMediaFactory,
        TimelineParameterService $timelineParameterService
    ) {
        $this->projectSettingService = $projectSettingService;
        $this->processedMediaFactory = $processedMediaFactory;
        $this->renderMediaFactory = $renderMediaFactory;
        $this->timelineParameterService = $timelineParameterService;
    }

    public function create(
        RelationType $relationType,
        int $rawMediaId,
        string $source,
        Project $project,
        float $start,
        ?float $trimStart,
        ?float $trimEnd
    ): ProcessedMedia {
        $processedMedia = $this->processedMediaFactory->create(
            $rawMediaId,
            $source,
            $project->id,
            $relationType
        );

        $this->renderMediaFactory->create($processedMedia, [
            'trimStart' => $this->timelineParameterService->roundToZeroIfNeeded($trimStart),
            'trimEnd' => $this->timelineParameterService->roundToZeroIfNeeded($trimEnd),
            'start' => $this->timelineParameterService->roundToZeroIfNeeded($start),
        ]);

        if ($relationType === RelationType::MUSIC) {
            $this->projectSettingService->disableLoopMusicSettingIfNeeded($project);
        }

        return $processedMedia;
    }
}
