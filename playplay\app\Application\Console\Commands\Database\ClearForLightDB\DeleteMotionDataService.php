<?php

namespace App\Application\Console\Commands\Database\ClearForLightDB;

use App\Domain\Screen\ScreenCoverRepository;
use App\Domain\Screen\ScreenRepository;
use Illuminate\Database\DatabaseManager;

class DeleteMotionDataService
{
    private DatabaseManager $databaseManager;
    private ScreenRepository $screenRepository;
    private ScreenCoverRepository $screenCoverRepository;

    public function __construct(
        DatabaseManager $databaseManager,
        ScreenRepository $screenRepository,
        ScreenCoverRepository $screenCoverRepository
    ) {
        $this->databaseManager = $databaseManager;
        $this->screenRepository = $screenRepository;
        $this->screenCoverRepository = $screenCoverRepository;
    }

    public function deleteScreenAndTemplateDataThatIsNotGenericExceptThoseOf(int $customTemplateCategoryIdToKeep): void
    {
        $templateToKeepIdsQuery = <<< SQL
SELECT DISTINCT template_id FROM sub_categories_templates WHERE sub_category_id IN (
    SELECT id as sub_category_id FROM sub_categories
    INNER JOIN categories_sub_categories ON categories_sub_categories.sub_category_id = sub_categories.id
    WHERE category_id IN (
        SELECT id from categories WHERE is_generic is TRUE OR id = $customTemplateCategoryIdToKeep
    )
)
SQL;

        $templateMediasToKeepIdsQuery = <<< SQL
SELECT media_id FROM template_covers WHERE template_id IN ($templateToKeepIdsQuery)
SQL;

        $projectsFromTemplatesToKeepIdsQuery = <<< SQL
SELECT project_id FROM template_projects WHERE template_id IN ($templateToKeepIdsQuery)
SQL;

        $this->deleteProjectItemsThatAreNotLinkedToGenericTemplates($projectsFromTemplatesToKeepIdsQuery);
        $this->deleteAllMediasExceptFromGenericTemplatesAndScreens($templateMediasToKeepIdsQuery);
        $this->deleteTemplateProjectsThatAreNotLinkedToGenericTemplates($templateToKeepIdsQuery);
        $this->deleteProjectsThatAreNotLinkedToGenericTemplatesOrFreeTrialModel($projectsFromTemplatesToKeepIdsQuery);
        $this->deleteTemplateCoversThatAreNotGeneric($templateToKeepIdsQuery);
        $this->deleteTemplatesThatAreNotGeneric($templateToKeepIdsQuery);
        $this->deleteSubCategoriesTemplatesThatAreNotGenericExceptThoseOf($customTemplateCategoryIdToKeep);
        $this->deleteSubCategoriesThatAreNotGenericExceptThoseOf($customTemplateCategoryIdToKeep);
        $this->deleteCategoriesSubCategoriesThatAreNotGenericExceptThoseOf($customTemplateCategoryIdToKeep);
        $this->deleteCategoriesThatAreNotGenericExcept($customTemplateCategoryIdToKeep);
        $this->deleteSoftDeletedScreens();
    }

    public function deleteTimecodedElementDataThatIsNotGeneric(): void
    {
        $this->databaseManager->delete(
            <<<SQL
DELETE timecoded_elements_families, timecoded_element_presets, timecoded_element_preset_params
FROM timecoded_elements_families
INNER JOIN timecoded_element_presets
    ON timecoded_element_presets.timecoded_elements_family_id = timecoded_elements_families.id
INNER JOIN timecoded_element_preset_params
    ON timecoded_element_preset_params.timecoded_element_preset_id = timecoded_element_presets.id
WHERE timecoded_elements_families.is_generic = 0
SQL
        );
    }

    private function deleteAllMediasExceptFromGenericTemplatesAndScreens(string $templateMediasToKeepIdsQuery): void
    {
        $this->databaseManager->delete(
            "DELETE FROM medias
            WHERE
                id NOT IN ($templateMediasToKeepIdsQuery)
                AND id NOT IN (
                  SELECT
                         media_id
                  from screen_covers
                  WHERE screen_id IN (SELECT id from screens where deleted_at is null)
                )
            "
        );
    }

    private function deleteCategoriesSubCategoriesThatAreNotGenericExceptThoseOf(int $customTemplateCategoryIdToKeep): void
    {
        $this->databaseManager->delete(
            "DELETE FROM categories_sub_categories
                WHERE
                      category_id IN (
                        SELECT id from categories WHERE is_generic is FALSE AND id != $customTemplateCategoryIdToKeep
                      )
            "
        );
    }

    private function deleteCategoriesThatAreNotGenericExcept(int $customTemplateCategoryIdToKeep): void
    {
        $this->databaseManager->delete(
            "DELETE FROM categories WHERE is_generic IS FALSE AND id != $customTemplateCategoryIdToKeep"
        );
    }

    /**
     * We use this method when deletion is too long to be operated whith a "classic" SQL DELETE
     * We replicate the data, truncate the table and re-insert the data that wee need
     */
    private function deleteFromTable(string $table, string $dataToKeepFromTableQuery): void
    {
        $this->databaseManager->statement("CREATE TABLE {$table}_tmp LIKE $table");

        $insertSubQuery = <<< SQL
INSERT INTO {$table}_tmp
$dataToKeepFromTableQuery
SQL;

        $this->databaseManager->insert($insertSubQuery);

        $this->databaseManager->table($table)->truncate();

        $this->databaseManager->insert("INSERT INTO $table (SELECT * FROM {$table}_tmp)");

        $this->databaseManager->statement("DROP TABLE {$table}_tmp");
    }

    private function deleteProjectItemsThatAreNotLinkedToGenericTemplates(
        string $projectsFromTemplatesToKeepIdsQuery
    ): void {
        $fontsToKeepIdsQuery = <<< SQL
SELECT DISTINCT font_id FROM projects WHERE id IN ($projectsFromTemplatesToKeepIdsQuery) OR id = 612069
SQL;
        $regularRawMediaFont = <<< SQL
SELECT regular_id FROM fonts WHERE id IN ($fontsToKeepIdsQuery)
SQL;
        $boldRawMediaFont = <<< SQL
SELECT bold_id FROM fonts WHERE id IN ($fontsToKeepIdsQuery)
SQL;
        $italicRawMediaFont = <<< SQL
SELECT italic_id FROM fonts WHERE id IN ($fontsToKeepIdsQuery)
SQL;
        $projectRawMediaIdsToKeep = <<< SQL
SELECT
       raw_media_id
FROM project_raw_medias
WHERE
      project_id IN ($projectsFromTemplatesToKeepIdsQuery)
   OR project_id = 612069
SQL;
        $processedMediasToKeep = <<< SQL
SELECT * FROM processed_medias WHERE project_id IN ($projectsFromTemplatesToKeepIdsQuery) OR project_id = 612069
SQL;
        $rawMediaIdsFromProcessedMedias = <<< SQL
SELECT
       raw_media_id
FROM processed_medias
WHERE
      project_id IN ($projectsFromTemplatesToKeepIdsQuery)
   OR project_id = 612069
SQL;
        $optionMusicRawMediasIdsToKeep = <<< SQL
SELECT raw_media_id FROM musics WHERE music_list_id = 106
SQL;
        $rawMediasToKeep = <<< SQL
SELECT * FROM raw_medias WHERE id IN ($regularRawMediaFont) OR id IN ($boldRawMediaFont) OR id IN ($italicRawMediaFont)
                            OR id IN ($projectRawMediaIdsToKeep)
                            OR id IN ($rawMediaIdsFromProcessedMedias)
                            OR id IN ($optionMusicRawMediasIdsToKeep)
SQL;
        $rawMediasIdsToKeep = <<< SQL
SELECT id FROM raw_medias WHERE id IN ($regularRawMediaFont) OR id IN ($boldRawMediaFont) OR id IN ($italicRawMediaFont)
                             OR id IN ($projectRawMediaIdsToKeep)
                             OR id IN ($rawMediaIdsFromProcessedMedias)
                             OR id IN ($optionMusicRawMediasIdsToKeep)
SQL;
        $fontsToKeepQuery = <<< SQL
SELECT * FROM fonts WHERE id IN ($fontsToKeepIdsQuery)
SQL;
        $processedMediasIdsToKeep = <<< SQL
SELECT id FROM processed_medias WHERE project_id IN ($projectsFromTemplatesToKeepIdsQuery) OR project_id = 612069
SQL;
        $renderMediasToKeep = <<< SQL
SELECT * FROM render_medias WHERE parent_id IN ($processedMediasIdsToKeep)
UNION
SELECT * FROM render_medias WHERE parent_id IN ($rawMediasIdsToKeep)
SQL;
        $this->deleteFromTable('fonts', $fontsToKeepQuery);
        $this->deleteFromTable('raw_medias', $rawMediasToKeep);
        $this->deleteFromTable('processed_medias', $processedMediasToKeep);
        $this->deleteFromTable('render_medias', $renderMediasToKeep);
        $this->databaseManager->delete(
            "DELETE FROM project_raw_medias
                    WHERE
                          project_id NOT IN ($projectsFromTemplatesToKeepIdsQuery)
                      AND project_id != 612069
            "
        );
        $this->databaseManager->delete(
            <<<SQL
DELETE timecoded_elements, timecoded_element_preset_param_values
FROM timecoded_elements
INNER JOIN project_screens ON project_screens.id = timecoded_elements.project_screen_id
INNER JOIN timecoded_element_preset_param_values ON timecoded_element_preset_param_values.timecoded_element_id = timecoded_elements.id
WHERE project_screens.project_id NOT IN ($projectsFromTemplatesToKeepIdsQuery)
AND project_screens.project_id != 612069
SQL
        );

        $this->databaseManager->delete(
            "DELETE FROM project_screens
                    WHERE
                          project_id NOT IN ($projectsFromTemplatesToKeepIdsQuery)
                      AND project_id != 612069
            "
        );
        $this->databaseManager->delete(
            "DELETE FROM project_screen_params
                    WHERE
                          project_id NOT IN ($projectsFromTemplatesToKeepIdsQuery)
                      AND project_id != 612069
          "
        );
        // some projects have a template_id that matches a template that does not exist anymore
        $this->databaseManager->table('projects')->update(['template_id' => null]);
    }

    private function deleteProjectsThatAreNotLinkedToGenericTemplatesOrFreeTrialModel(
        string $projectsFromTemplatesToKeepIdsQuery
    ): void {
        $this->databaseManager->delete(
            "DELETE FROM projects WHERE id NOT IN ($projectsFromTemplatesToKeepIdsQuery) AND id != 612069"
        );
    }

    private function deleteSoftDeletedScreens(): void
    {
        $this->screenCoverRepository->deleteAllFromSoftDeletedScreens();
        $this->screenRepository->hardDeleteSoftDeletedScreens();
    }

    private function deleteSubCategoriesTemplatesThatAreNotGenericExceptThoseOf(int $customTemplateCategoryIdToKeep): void
    {
        $this->databaseManager->delete(
            "DELETE FROM sub_categories_templates WHERE sub_category_id IN (
            SELECT id as sub_category_id FROM sub_categories
            INNER JOIN categories_sub_categories ON categories_sub_categories.sub_category_id = sub_categories.id
            WHERE category_id IN (
                    SELECT id from categories WHERE is_generic is FALSE AND id != $customTemplateCategoryIdToKeep
                )
            )"
        );
    }

    private function deleteSubCategoriesThatAreNotGenericExceptThoseOf(int $customTemplateCategoryIdToKeep): void
    {
        $this->databaseManager->delete(
            "DELETE sub_categories FROM sub_categories
            INNER JOIN categories_sub_categories ON categories_sub_categories.sub_category_id = sub_categories.id
            WHERE category_id IN (
                SELECT id from categories WHERE is_generic is FALSE AND id != $customTemplateCategoryIdToKeep
            )"
        );
    }

    private function deleteTemplateCoversThatAreNotGeneric(string $templateToKeepIdsQuery): void
    {
        $this->databaseManager->delete(
            "DELETE FROM template_covers WHERE template_id NOT IN ($templateToKeepIdsQuery)"
        );
    }

    private function deleteTemplateProjectsThatAreNotLinkedToGenericTemplates(string $templateToKeepIdsQuery): void
    {
        $this->databaseManager->delete(
            "DELETE FROM template_projects WHERE template_id NOT IN ($templateToKeepIdsQuery)"
        );
    }

    private function deleteTemplatesThatAreNotGeneric(string $templateToKeepIdsQuery): void
    {
        $this->databaseManager->delete("DELETE FROM templates WHERE id NOT IN ($templateToKeepIdsQuery) AND id != 433");
    }
}
