<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Media;

use App\Application\Events\ProjectUpdated;
use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Project\ProcessedMedia\Store\ProcessedMediaAudioSplitRequest;
use App\Application\Http\Requests\Api\Project\ProcessedMedia\Store\ProcessedMediaAudioStoreRequest;
use App\Application\Http\Requests\Api\Project\ProcessedMedia\Store\ProcessedMediaAudioTrimRequest;
use App\Domain\Audio\AudioFactory;
use App\Domain\Audio\AudioTimelineSerializer;
use App\Domain\Audio\FilterApplier;
use App\Domain\ProcessedMedia\Repository\ProcessedMediaRepository;
use App\Domain\Project\ProcessedMedia\RelationType;
use App\Models\ProcessedMedia;
use App\Models\Project;
use App\Services\ProcessedMedia\AccessChecker;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class ProcessedMediaAudioController extends BaseController
{
    private Dispatcher $eventDispatcher;
    private AudioFactory $audioFactory;
    private AccessChecker $accessChecker;
    private ProcessedMediaRepository $processedMediaRepository;
    private FilterApplier $filterApplier;
    private AudioTimelineSerializer $audioTimelineSerializer;

    use AuthorizesRequests;

    public function __construct(
        Dispatcher $eventDispatcher,
        AccessChecker $accessChecker,
        ProcessedMediaRepository $processedMediaRepository,
        AudioFactory $audioFactory,
        FilterApplier $filterApplier,
        AudioTimelineSerializer $audioTimelineSerializer
    ) {
        $this->eventDispatcher = $eventDispatcher;
        $this->accessChecker = $accessChecker;
        $this->audioFactory = $audioFactory;
        $this->processedMediaRepository = $processedMediaRepository;
        $this->filterApplier = $filterApplier;
        $this->audioTimelineSerializer = $audioTimelineSerializer;
    }

    /**
     * @throws AuthorizationException
     */
    public function destroy(string $relationTypeKey, ProcessedMedia $processedMedia): JsonResponse
    {
        $this->authorize('destroyAudioProcessedMedia', [
            $processedMedia,
            RelationType::fromKey($relationTypeKey)
        ]);
        $this->processedMediaRepository->delete($processedMedia->id);

        $this->eventDispatcher->dispatch(new ProjectUpdated($processedMedia->project));

        return new JsonResponse([], Response::HTTP_NO_CONTENT);
    }

    /**
     * @throws AuthorizationException
     */
    public function split(
        ProcessedMediaAudioSplitRequest $request,
        string $relationTypeKey,
        ProcessedMedia $processedMedia
    ): JsonResponse {
        $this->accessChecker->checkIfCanUpdateProcessedMedia($processedMedia);
        $relationType = RelationType::fromKey($relationTypeKey);
        $splitProcessedMedia = $this->filterApplier->split(
            $processedMedia,
            $request->get('split_time')
        );
        $updatedProcessedMedia = $splitProcessedMedia->getUpdatedProcessedMedia();

        $this->eventDispatcher->dispatch(new ProjectUpdated($updatedProcessedMedia->project));

        return $this->sendJsonResponse(
            new Collection([
                [
                    'created' => $this->audioTimelineSerializer->serialize(
                        $relationType,
                        [$splitProcessedMedia->getCreatedProcessedMedia()]
                    )[0] ?? null,
                    'updated' => $this->audioTimelineSerializer->serialize(
                        $relationType,
                        [$updatedProcessedMedia]
                    )[0] ?? null,
                ],
            ]),
            Response::HTTP_CREATED
        );
    }

    /**
     * @throws AuthorizationException
     */
    public function store(string $relationTypeKey, ProcessedMediaAudioStoreRequest $request): JsonResponse
    {
        $rawMediaId = $request->get('raw_media_id');
        $projectId = $request->get('project_id');
        $source = $request->get('source');

        $this->accessChecker->checkIfCanCreateProcessedMediaFromRawMediaAndSourceAndProject(
            $rawMediaId,
            $source,
            $projectId
        );

        /** @var Project $project */
        $project = Project::query()->find($projectId);
        $relationType = RelationType::fromKey($relationTypeKey);
        $processedMedia = $this->audioFactory->create(
            $relationType,
            $rawMediaId,
            $source,
            $project,
            $request->get('start'),
            $request->get('trim_start'),
            $request->get('trim_end')
        );

        $this->eventDispatcher->dispatch(new ProjectUpdated($processedMedia->project));

        return $this->sendJsonResponse(
            new Collection(
                $this->audioTimelineSerializer->serialize(
                    $relationType,
                    [$processedMedia]
                )
            ),
            Response::HTTP_CREATED
        );
    }

    /**
     * @throws AuthorizationException
     */
    public function trim(
        ProcessedMediaAudioTrimRequest $request,
        string $relationTypeKey,
        ProcessedMedia $processedMedia
    ): JsonResponse {
        $this->accessChecker->checkIfCanUpdateProcessedMedia($processedMedia);
        $this->filterApplier->trim(
            $processedMedia,
            $request->get('trim_start'),
            $request->get('trim_end'),
            $request->get('start')
        );

        $this->eventDispatcher->dispatch(new ProjectUpdated($processedMedia->project));

        return $this->sendJsonResponse(
            new Collection(
                $this->audioTimelineSerializer->serialize(
                    RelationType::fromKey($relationTypeKey),
                    [$processedMedia]
                )
            ),
            Response::HTTP_OK
        );
    }
}
