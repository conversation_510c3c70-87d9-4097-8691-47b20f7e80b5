<?php

declare(strict_types=1);

namespace App\Domain\Billing;

use RuntimeException;

final class BillingException extends RuntimeException
{
    public function __construct(?string $message, int $code)
    {
        $jsonMessage = $message !== null ? json_decode($message) : '';
        $errorMessage = [
            'code' => $jsonMessage->error->code ?? '',
            'message' => $jsonMessage->error->message ?? '',
            'field' => $jsonMessage->error->param ?? '',
            'type' => $jsonMessage->error->type ?? '',
            'decline_code' => $jsonMessage->error->decline_code ?? '',
        ];
        parent::__construct(json_encode([$errorMessage]), $code);
    }
}
