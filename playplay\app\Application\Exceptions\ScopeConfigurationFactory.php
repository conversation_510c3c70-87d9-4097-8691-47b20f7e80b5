<?php

declare(strict_types=1);

namespace App\Application\Exceptions;

use App\Domain\Billing\BillingException;
use App\Domain\HealthCheck\HealthzException;
use App\Domain\Localization\UnableToGetCountryCodeException;
use App\Domain\Logs\ModelHistory\UnableToLogModelHistoryException;
use App\Domain\Planhat\PlanhatAPIException;
use App\Domain\Stock\StockException;
use App\Domain\User\Action\UnableToLogUserActionException;
use App\Domain\UserTeam\UnableToAddUserToTeamException;
use App\Models\User;
use Closure;
use Doctrine\DBAL\Driver\PDOException;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Config\Repository as ConfigRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Illuminate\Session\TokenMismatchException;
use Illuminate\Validation\UnauthorizedException;
use Illuminate\Validation\ValidationException;
use Predis\Connection\ConnectionException;
use Sentry\Severity;
use Sentry\State\Scope;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\NotAcceptableHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Throwable;

final class ScopeConfigurationFactory
{
    private ConfigRepository $config;

    public function __construct(ConfigRepository $config)
    {
        $this->config = $config;
    }

    public function create(Throwable $e, ?User $user): Closure
    {
        return function (Scope $scope) use ($e, $user): void {
            if ($user !== null) {
                $this->setUserToScopeIfExists($scope, $user);
            }

            $devTeam = $this->config->get('app.dev_team', '');
            if ($devTeam !== '') {
                $scope->setTag('dev_team', $devTeam);
            }

            if ($e instanceof ValidationException) {
                $scope->setExtra('validation_errors', $e->validator->failed());
                $scope->setLevel(Severity::info());
            } elseif ($this->isAnInfoLevelException($e)) {
                $scope->setLevel(Severity::info());
            } elseif ($this->isAWarningLevelException($e)) {
                $scope->setLevel(Severity::warning());
            } elseif ($e instanceof HealthzException) {
                $scope->setLevel(Severity::fatal());
            } else {
                $scope->setLevel(Severity::error());
            }
        };
    }

    private function isAWarningLevelException(Throwable $e): bool
    {
        return in_array(get_class($e), [
            StockException::class,
            ConnectionException::class,
            PDOException::class,
            ClientException::class,
            PlanhatAPIException::class,
            UnableToGetCountryCodeException::class,
            UnableToAddUserToTeamException::class,
        ]);
    }

    private function isAnInfoLevelException(Throwable $e): bool
    {
        return in_array(get_class($e), [
            UnauthorizedException::class,
            AuthorizationException::class,
            ModelNotFoundException::class,
            TokenMismatchException::class,
            NotFoundHttpException::class,
            AccessDeniedHttpException::class,
            NotAcceptableHttpException::class,
            BillingException::class,
            ThrottleRequestsException::class,
            UnableToLogUserActionException::class,
            UnableToLogModelHistoryException::class,
            UnprocessableEntityHttpException::class,
        ]);
    }

    private function setUserToScopeIfExists(Scope $scope, User $user): void
    {
        $scope->setUser([
            'id' => (string) $user->id,
            'email' => $user->email,
            'isAdmin' => $user->is_admin,
            'name' => $user->name,
            'username' => $user->name,
            'company_type' => $user->company->type ?? null,
            'company_name' => $user->company->name ?? null,
        ]);
    }
}
