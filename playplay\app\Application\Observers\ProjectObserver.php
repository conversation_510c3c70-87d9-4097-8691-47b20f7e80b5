<?php

namespace App\Application\Observers;

use App\Domain\User\Action\UserAction;
use App\Domain\User\Action\UserActionService;
use App\Models\Project;

class ProjectObserver
{
    private UserActionService $userActionService;

    public function __construct(UserActionService $userActionService)
    {
        $this->userActionService = $userActionService;
    }

    public function created(Project $project): void
    {
        if ($project->project_duplicated_id === null) {
            $this->userActionService->addUserAction(
                new UserAction(
                    'project-created',
                    [],
                    $project->team_id,
                    $project->id,
                )
            );
        } else {
            $this->userActionService->addUserAction(
                new UserAction(
                    'project-duplicated',
                    [],
                    $project->team_id,
                    $project->id,
                )
            );
        }
    }
}
