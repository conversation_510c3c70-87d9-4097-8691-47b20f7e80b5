<?php

namespace App\Application\Http\Requests\Api\Project\ProcessedMedia;

use App\Domain\Project\ProcessedMedia\RelationType;
use App\Models\ProcessedMedia;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Routing\Route;

abstract class ProcessedMediaAudioRequest extends FormRequest
{
    public function withValidator(Validator $validator): void
    {
        $validator->after(function (Validator $validator) {
            if (!$this->isValidProcessedMediaRelationType()) {
                $validator->addFailure('processed_media', 'is_valid_processed_media_relation_type');
            }
        });
    }

    private function isValidProcessedMediaRelationType(): bool
    {
        /** @var Route $route */
        $route = request()?->route();
        $relationType = RelationType::fromKey($route->parameter('relationTypeKey'));
        /** @var ProcessedMedia $processedMedia */
        $processedMedia = request()?->route('processedMedia');

        return $processedMedia->relation_type === $relationType;
    }
}
