<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Billing;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Billing\SubscriptionChurnUpdateRequest;
use App\Domain\Billing\CompanyChurnRepository;
use App\Models\Subscription;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

final class UpdateSubscriptionChurnController extends BaseController
{
    use AuthorizesRequests;

    private CompanyChurnRepository $companyChurnRepository;

    public function __construct(CompanyChurnRepository $companyChurnRepository)
    {
        $this->companyChurnRepository = $companyChurnRepository;
    }

    /**
     * @throws AuthorizationException
     */
    public function __invoke(
        SubscriptionChurnUpdateRequest $request,
        Subscription $subscription
    ): JsonResponse {
        $this->authorize('churn', $subscription);

        $this->companyChurnRepository->explainChurn(
            $subscription->churn,
            $request->input('reason'),
            $request->input('comment')
        );

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }
}
