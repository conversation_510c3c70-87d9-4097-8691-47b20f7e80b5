<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Logs\ModelHistory\ModelHistorizables;
use App\Domain\Logs\ModelHistory\ModelHistoryDispatcher;
use App\Domain\Logs\ModelHistory\ModelHistoryService;
use App\Infrastructure\Logs\ModelHistory\ModelHistorizables\BillingPlanHistorizable;
use App\Infrastructure\Logs\ModelHistory\ModelHistorizables\CompanyHistorizable;
use App\Infrastructure\Logs\ModelHistory\ModelHistorizables\ConfigHistorizable;
use App\Infrastructure\Logs\ModelHistory\ModelHistorizables\PlanHistorizable;
use App\Infrastructure\Logs\ModelHistory\ModelHistorizables\ProjectHistorizable;
use App\Infrastructure\Logs\ModelHistory\ModelHistorizables\ProjectScreenHistorizable;
use App\Infrastructure\Logs\ModelHistory\ModelHistorizables\SubscriptionHistorizable;
use App\Infrastructure\Logs\ModelHistory\ModelHistorizables\TeamHistorizable;
use App\Infrastructure\Logs\ModelHistory\ModelHistorizables\TeamPresetHistorizable;
use App\Infrastructure\Logs\ModelHistory\ModelHistorizables\UserHistorizable;
use App\Infrastructure\Logs\ModelHistory\PubSubModelHistoryDispatcher;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Support\ServiceProvider;

final class ModelHistoryServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(ModelHistoryDispatcher::class, PubSubModelHistoryDispatcher::class);
        $this->app->bind(ModelHistorizables::class, function ($app) {
            return new ModelHistorizables([
                $app->make(CompanyHistorizable::class),
                $app->make(BillingPlanHistorizable::class),
                $app->make(ConfigHistorizable::class),
                $app->make(PlanHistorizable::class),
                $app->make(ProjectHistorizable::class),
                $app->make(ProjectScreenHistorizable::class),
                $app->make(SubscriptionHistorizable::class),
                $app->make(TeamHistorizable::class),
                $app->make(TeamPresetHistorizable::class),
                $app->make(UserHistorizable::class),
            ]);
        });
        $this->app->bind(ModelHistoryService::class, function ($app) {
            return new ModelHistoryService(
                $app->make(ModelHistoryDispatcher::class),
                $app->make(Dispatcher::class)
            );
        });
    }
}
