<?php

namespace App\Domain\Audio;

use App\Models\ProcessedMedia;

final class SplitProcessedMedia
{
    private ProcessedMedia $createdProcessedMedia;
    private ProcessedMedia $updatedProcessedMedia;

    public function __construct(ProcessedMedia $createdProcessedMedia, ProcessedMedia $updatedProcessedMedia)
    {
        $this->createdProcessedMedia = $createdProcessedMedia;
        $this->updatedProcessedMedia = $updatedProcessedMedia;
    }

    public function getCreatedProcessedMedia(): ProcessedMedia
    {
        return $this->createdProcessedMedia;
    }

    public function getUpdatedProcessedMedia(): ProcessedMedia
    {
        return $this->updatedProcessedMedia;
    }
}
