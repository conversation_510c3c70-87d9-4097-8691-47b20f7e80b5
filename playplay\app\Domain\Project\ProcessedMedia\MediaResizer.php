<?php

declare(strict_types=1);

namespace App\Domain\Project\ProcessedMedia;

use App\Domain\ProcessedMedia\Repository\ProcessedMediaRepository;
use App\Domain\ProcessedMedia\Transformations\TransformationsFactoryInterface;
use App\Domain\ProjectScreen\Param\ProjectScreenParamRepository;
use App\Domain\RawMedia\RawMediaType;
use App\Models\ProcessedMedia;
use App\Models\Project;
use App\Models\ScreenParams\BaseParam;
use App\Services\ProcessedMedia\Resizer;
use Illuminate\Database\Eloquent\Collection;

class MediaResizer
{
    private ProcessedMediaRepository $processedMediaRepository;
    private Resizer $processedMediaResizer;
    private TransformationsFactoryInterface $transformationsFactory;
    private ProjectScreenParamRepository $projectScreenParamRepository;

    public function __construct(
        Resizer $processedMediaResizer,
        ProcessedMediaRepository $processedMediaRepository,
        ProjectScreenParamRepository $projectScreenParamRepository,
        TransformationsFactoryInterface $transformationsFactory
    ) {
        $this->processedMediaResizer = $processedMediaResizer;
        $this->projectScreenParamRepository = $projectScreenParamRepository;
        $this->processedMediaRepository = $processedMediaRepository;
        $this->transformationsFactory = $transformationsFactory;
    }

    public function resizeAllMedias(Project $project): bool
    {
        $processedMedias = $this->processedMediaRepository->getByProjectIdAndRawMediaTypes(
            $project->id,
            RawMediaType::getViewableMediaTypes()
        );

        $projectScreenParamValueIds = $this->projectScreenParamRepository->getValueIdsByProjectAndParamTypes(
            $project->id,
            [BaseParam::TYPE_MEDIA, BaseParam::TYPE_CUTAWAY_SHOT]
        )->toArray();

        $filteredProcessedMedias = $processedMedias->filter(
            static function (ProcessedMedia $processedMedia) use ($projectScreenParamValueIds) {
                return in_array($processedMedia->id, $projectScreenParamValueIds, true);
            }
        );

        return $this->dispatchResizerForEachProcessedMedia($filteredProcessedMedias, $project->normalized_format);
    }

    /**
     * @param ProcessedMedia[]|Collection $processedMedias
     */
    private function dispatchResizerForEachProcessedMedia(
        Collection $processedMedias,
        string $normalizedFormat
    ): bool {
        $allAutoResizeProcessed = true;

        foreach ($processedMedias as $processedMedia) {
            $transformations = $this->transformationsFactory->createFromProcessedMediaWithDestinationFormatForResize(
                $processedMedia,
                $normalizedFormat
            );

            $autoResizeStatus = $this->processedMediaResizer->resize(
                $processedMedia,
                $transformations
            );

            if ($autoResizeStatus === false) {
                $allAutoResizeProcessed = false;
            }
        }

        return $allAutoResizeProcessed;
    }
}
