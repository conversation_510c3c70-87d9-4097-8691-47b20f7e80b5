<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing;

use App\Application\Providers\Routing\Api\Media\RenderMediaRouteMapper;
use App\Application\Providers\Routing\Api\RenderingCallbacksRouteMapper;
use App\Application\Providers\Routing\Api\Translation\TranslationRouteMapper;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider;
use Illuminate\Support\Facades\Route;

final class ApiRenderingCallbacksRoutingProvider extends RouteServiceProvider
{
    private array $routeMappers;

    public function __construct(Application $application)
    {
        parent::__construct($application);

        $this->routeMappers = [
            new RenderingCallbacksRouteMapper(),
            new RenderMediaRouteMapper(),
            new TranslationRouteMapper(),
        ];
    }

    public function map(): void
    {
        Route::group([
            'middleware' => ['rendering-callbacks-auth'],
            'prefix' => 'api',
            'as' => 'api.',
        ], function () {
            foreach ($this->routeMappers as $routeMapper) {
                $routeMapper->map();
            }
        });
    }
}
