<?php

namespace App\Application\Http\Controllers\Api\V2\Media;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\RawMedia\FavoriteRawMedia\FavoriteRawMediaRequest;
use App\Application\Http\Requests\Api\RawMedia\FavoriteRawMedia\FavoriteRawMediaStoreRequest;
use App\Domain\FavoriteRawMedia\FavoriteRawMediaRepository;
use App\Domain\FavoriteRawMedia\FavoriteRawMediaService;
use App\Models\FavoriteRawMedia;
use App\Models\RawMedia;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class FavoriteRawMediaController extends BaseController
{
    use AuthorizesRequests;

    private FavoriteRawMediaRepository $favoriteRawMediaRepository;
    private FavoriteRawMediaService $favoriteRawMediaService;
    private Guard $auth;

    public function __construct(
        Guard $auth,
        FavoriteRawMediaService $favoriteRawMediaService,
        FavoriteRawMediaRepository $favoriteRawMediaRepository
    ) {
        $this->auth = $auth;
        $this->favoriteRawMediaService = $favoriteRawMediaService;
        $this->favoriteRawMediaRepository = $favoriteRawMediaRepository;
    }

    public function destroy(RawMedia $rawMedia): JsonResponse
    {
        $favoriteRawMedia = $this->favoriteRawMediaRepository->getOneByIdAndUserId($rawMedia->id, $this->auth->id());
        $this->authorize('destroy', $favoriteRawMedia);

        $this->favoriteRawMediaRepository->deleteOneByIdAndUserId($rawMedia->id, $this->auth->id());

        return $this->sendJsonResponse(new Collection([]), Response::HTTP_NO_CONTENT);
    }

    public function index(FavoriteRawMediaRequest $request): JsonResponse
    {
        $favoriteRawMedias = $this->favoriteRawMediaService
            ->retrieveFavorites($this->auth->id(), (int) $request->get('page', 1));

        return $this->sendJsonResponse(new Collection([$favoriteRawMedias]), Response::HTTP_OK);
    }

    public function store(FavoriteRawMediaStoreRequest $request): JsonResponse
    {
        $rawMedia = RawMedia::find($request->get('raw_media_id'));
        $this->authorize('create', [FavoriteRawMedia::class, $rawMedia]);

        $this->favoriteRawMediaRepository->updateOrInsert($this->auth->id(), $rawMedia);

        return $this->sendJsonResponse(new Collection([$rawMedia]), Response::HTTP_CREATED);
    }
}
