<?php

namespace App\Application\Http\Requests\Api\RenderProject;

use App\Models\Renders\ARender;
use Illuminate\Foundation\Http\FormRequest;

class UpdateRenderProjectHtmlRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'status' => ['required', 'in:' . ARender::STATUS_ABORTED],
        ];
    }
}
