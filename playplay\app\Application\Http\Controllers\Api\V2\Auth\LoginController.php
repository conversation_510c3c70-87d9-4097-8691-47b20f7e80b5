<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Auth;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Auth\LoginRequest;
use Illuminate\Contracts\Cache\Repository as Cache;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Symfony\Component\HttpKernel\Exception\NotAcceptableHttpException;

/**
 * Class LoginController
 *
 * @package    App\Http\Controllers\Api\V2\Auth
 * @deprecated Since 2021-05-20 use App\Http\Controllers\Api\V3\Auth\LoginController instead
 */
final class LoginController extends BaseController
{
    private Cache $cache;

    public function __construct(Cache $cache)
    {
        $this->cache = $cache;
    }

    /**
     * @param LoginRequest $request
     *
     * @return JsonResponse
     * @deprecated
     */
    public function login(LoginRequest $request): JsonResponse
    {
        $credentials = $request->only('email', 'password');
        $remember = $request->input('remember', false);

        // Try to login the user with the given credentials
        if (Auth::attempt($credentials, $remember)) {
            $token = (string) Str::uuid();
            $this->cache->forever($token, Auth::id());
            return $this->sendJsonResponse(new Collection([compact('token')]), JsonResponse::HTTP_OK);
        }

        throw new NotAcceptableHttpException('password.not_acceptable');
    }
}
