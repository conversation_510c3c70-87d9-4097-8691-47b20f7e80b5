<?php

namespace App\Domain\Logs\ModelHistory;

use JsonException;

/**
 * This object represents the contract between the data that
 * will be sent to be logged and the data to be saved in BigQuery
 * Therefore any change in the properties of this class should
 * be mirrored in BigQuery's respective table
 */
final class ModelHistory
{
    private string $requestId;
    private string $actionName;
    private int $modelId;
    private string $modelName;
    private array $modelAttributes;
    private int $dateTime;

    public function __construct(
        string $requestId,
        string $actionName,
        int $modelId,
        string $modelName,
        array $modelAttributes,
        int $dateTime
    ) {
        $this->requestId = $requestId;
        $this->actionName = $actionName;
        $this->modelId = $modelId;
        $this->modelName = $modelName;
        $this->modelAttributes = $modelAttributes;
        $this->dateTime = $dateTime;
    }

    public function id(): string
    {
        return $this->modelName . ':' . $this->modelId . ':' . $this->actionName;
    }

    /**
     * @throws JsonException
     */
    public function toArray(): array
    {
        return [
            'request_id' => $this->requestId,
            'action' => $this->actionName,
            'model_id' => $this->modelId,
            'model_name' => $this->modelName,
            'attributes' => json_encode($this->modelAttributes, JSON_THROW_ON_ERROR),
            'datetime' => $this->dateTime,
        ];
    }
}
