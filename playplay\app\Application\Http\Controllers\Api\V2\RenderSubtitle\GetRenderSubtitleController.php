<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\RenderSubtitle;

use App\Application\Http\Controllers\Api\BaseController;
use App\Domain\Render\RenderSubtitle\RenderSubtitleSerializer;
use App\Models\Project;
use App\Models\ProjectScreenParam;
use App\Models\Renders\RenderSubtitle;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class GetRenderSubtitleController extends BaseController
{
    use AuthorizesRequests;

    private RenderSubtitleSerializer $serializer;

    public function __construct(RenderSubtitleSerializer $serializer)
    {
        $this->serializer = $serializer;
    }

    public function __invoke(
        Project $project,
        ProjectScreenParam $projectScreenParam,
        RenderSubtitle $renderSubtitle
    ): JsonResponse {
        $this->authorize('view', $renderSubtitle);

        return $this->sendJsonResponse(
            new Collection([$this->serializer->serialize($renderSubtitle)]),
            Response::HTTP_OK
        );
    }
}
