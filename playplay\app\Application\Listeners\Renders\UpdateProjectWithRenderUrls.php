<?php

declare(strict_types=1);

namespace App\Application\Listeners\Renders;

use App\Application\Events\Renders\RenderProjectUpdated;

final class UpdateProjectWithRenderUrls
{
    public function handle(RenderProjectUpdated $event): void
    {
        $renderProject = $event->getRenderProject();

        if ($renderProject->rendered_url !== null || $renderProject->thumbnail_url !== null) {
            $renderProject->project->update([
                'last_rendered_url' => $renderProject->rendered_url,
                'last_thumbnail_url' => $renderProject->thumbnail_url,
            ]);
        }
    }
}
