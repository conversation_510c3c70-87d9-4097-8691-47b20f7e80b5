<?php

declare(strict_types=1);

namespace App\Domain\CutawayShot;

use App\Domain\Processing\RenderMediaFactory;
use App\Models\ProcessedMedia;
use App\Services\ProcessedMedia\Factory as ProcessedMediaFactory;

class CutawayShotService
{
    private ProcessedMediaFactory $processedMediaFactory;
    private RenderMediaFactory $renderMediaFactory;

    public function __construct(
        ProcessedMediaFactory $processedMediaFactory,
        RenderMediaFactory $renderMediaFactory
    ) {
        $this->processedMediaFactory = $processedMediaFactory;
        $this->renderMediaFactory = $renderMediaFactory;
    }

    public function create(
        int $projectId,
        int $rawMediaId,
        string $source,
        CutawayShotImageRenderMediaData | CutawayShotVideoRenderMediaData $cutawayShotRenderMediaData
    ): ProcessedMedia {
        $processedMedia = $this->processedMediaFactory->create(
            $rawMediaId,
            $source,
            $projectId
        );

        $this->renderMediaFactory->create(
            $processedMedia,
            $cutawayShotRenderMediaData->jsonSerialize()
        );

        return $processedMedia;
    }
}
