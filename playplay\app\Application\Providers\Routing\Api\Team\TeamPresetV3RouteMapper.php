<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Team;

use App\Application\Http\Controllers\Api\V3\TeamPreset\TeamPresetController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class TeamPresetV3RouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'as' => 'teams.presets.',
            'prefix' => '/teams/{team}/presets'
        ], static function (Router $router) {
            $router->get('', [TeamPresetController::class, 'show'])->name('show');
            $router->put('/fonts', [TeamPresetController::class, 'updateFonts'])->name('fonts.update');
        });
    }
}
