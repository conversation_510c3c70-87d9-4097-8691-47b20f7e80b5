<?php

namespace App\Domain\Metrics;

use Illuminate\Contracts\Cache\Repository as CacheRepository;

class BoostedClusterSizeCalculator implements Calculator
{
    private Calculator $calculator;
    private CacheRepository $cacheRepository;

    public function __construct(Calculator $calculator, CacheRepository $cacheRepository)
    {
        $this->calculator = $calculator;
        $this->cacheRepository = $cacheRepository;
    }

    public function calculateProcessingHtmlClusterSize(int $activeV3UsersCount, int $totalDurationInHtmlRender): int
    {
        return $this->calculator->calculateProcessingHtmlClusterSize(
            $activeV3UsersCount,
            $totalDurationInHtmlRender
        ) + $this->getBoostFromCache();
    }

    public function calculateRenderingClusterSize(int $activeStudioUsersCount, int $totalDurationInRender): int
    {
        return $this->calculator->calculateRenderingClusterSize($activeStudioUsersCount, $totalDurationInRender) +
            $this->getBoostFromCache();
    }

    public function getClusterSizePerUser(float $activeUsersCount): int
    {
        return $this->calculator->getClusterSizePerUser($activeUsersCount);
    }

    private function getBoostFromCache(): int
    {
        return (int) $this->cacheRepository->get('boost');
    }
}
