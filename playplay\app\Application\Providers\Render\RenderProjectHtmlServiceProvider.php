<?php

declare(strict_types=1);

namespace App\Application\Providers\Render;

use App\Domain\Render\RenderProject\RenderProjectHtmlFactory as RenderProjectHtmlFactoryInterface;
use App\Domain\Render\RenderProject\RenderProjectHtmlRenderMediaService as RenderProjectHtmlRenderMediaServiceInterface;
use App\Domain\Render\RenderProject\RenderProjectHtmlRepository as RenderProjectHtmlRepositoryInterface;
use App\Domain\Render\RenderProject\RenderProjectSender as RenderProjectSenderInterface;
use App\Domain\Render\RenderProject\RenderProjectService as RenderProjectServiceInterface;
use App\Domain\Render\RenderStorageService as RenderStorageServiceInterface;
use App\Infrastructure\Render\GcsRenderStorageService;
use App\Infrastructure\Render\RenderProject\EloquentRenderProjectHtmlRepository;
use App\Infrastructure\Render\RenderProject\MailerRenderProjectSender;
use App\Infrastructure\Render\RenderProject\RenderProjectHtmlFactory;
use App\Infrastructure\Render\RenderProject\RenderProjectHtmlRenderMediaService;
use App\Infrastructure\Render\RenderProject\RenderProjectService;
use Illuminate\Support\ServiceProvider;

class RenderProjectHtmlServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(RenderProjectHtmlRepositoryInterface::class, EloquentRenderProjectHtmlRepository::class);
        $this->app->bind(RenderProjectHtmlFactoryInterface::class, RenderProjectHtmlFactory::class);
        $this->app->bind(RenderProjectServiceInterface::class, RenderProjectService::class);
        $this->app->bind(RenderProjectSenderInterface::class, MailerRenderProjectSender::class);
        $this->app->bind(RenderStorageServiceInterface::class, GcsRenderStorageService::class);
        $this->app->bind(
            RenderProjectHtmlRenderMediaServiceInterface::class,
            RenderProjectHtmlRenderMediaService::class
        );
    }
}
