<?php

namespace App\Application\Policies;

use App\Domain\Template\TemplatePolicy as DomainTemplatePolicy;
use App\Models\Template;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class TemplatePolicy extends DefaultPolicy
{
    use HandlesAuthorization;

    private DomainTemplatePolicy $domainTemplatePolicy;

    public function __construct(DomainTemplatePolicy $domainTemplatePolicy)
    {
        $this->domainTemplatePolicy = $domainTemplatePolicy;
    }

    public function before(User $authUser)
    {
        if ($authUser->can('manage-templates')) {
            return true;
        }
    }

    public function update(User $user, Template $template): bool
    {
        return $this->domainTemplatePolicy->allowsUpdate($user, $template);
    }

    public function destroy(User $user, Template $template): bool
    {
        return $this->domainTemplatePolicy->allowsDestroy($user, $template);
    }
}
