<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing;

use App\Application\Http\Controllers\Api\Project\ManageProjectController;
use App\Application\Http\Controllers\Api\V2\User\UserBrowserSupportController;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class ApiProtectedFromExpiredUsersRoutingProvider extends RouteServiceProvider
{
    public function map(): void
    {
        Route::group([
            'middleware' => ['api', 'auth', 'expired-users'],
            'prefix' => 'api',
            'as' => 'api.',
        ], function () {
            $this->mapProjectsRoutes();
            $this->mapUsersMeRoutes();
        });
    }

    private function mapProjectsRoutes(): void
    {
        Route::group([
            'prefix' => '/projects',
            'as' => 'projects.',
        ], static function (Router $router) {
            $router->put('/{project}/privatize', [ManageProjectController::class, 'privatize'])->name('privatize');
            $router->put('/{project}/share-with-team', [ManageProjectController::class, 'shareWithTeam'])
                ->name('share-with-team');
        });
    }

    private function mapUsersMeRoutes(): void
    {
        Route::group(
            [
                'prefix' => '/users/me',
                'as' => 'users.me.',
            ],
            static function (Router $router) {
                $router->post('/browser-support', [UserBrowserSupportController::class, 'store'])
                    ->name('browser-support');
            }
        );
    }
}
