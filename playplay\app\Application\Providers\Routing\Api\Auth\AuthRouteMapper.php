<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Auth;

use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class AuthRouteMapper implements RouteMapper
{
    /** @var RouteMapper[] */
    private array $routeMappers;

    public function __construct()
    {
        // Declare all route mappers
        $this->routeMappers = [
            new LoginRouteMapper(),
            new LoginSSORouteMapper(),
            new ResetPasswordRouteMapper(),
            new SsoCallbackLoginRouteMapper(),
        ];
    }

    public function map(): void
    {
        Route::group(['namespace' => 'Auth'], function () {
            foreach ($this->routeMappers as $routeMapper) {
                $routeMapper->map();
            }
        });
    }
}
