<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Team;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\Team\TimecodedElement\ToggleTimecodedElementsFamilyAutoAddNewPresetsRequest;
use App\Domain\TimecodedElement\Repositories\TeamTimecodedElementsFamilyRepository;
use App\Models\Team;
use App\Models\TimecodedElement\TimecodedElementsFamily;
use Illuminate\Http\JsonResponse;

final class ToggleTimecodedElementsFamilyAutoAddNewPresetsController extends BaseController
{
    private TeamTimecodedElementsFamilyRepository $teamTimecodedElementsFamilyRepository;

    public function __construct(
        TeamTimecodedElementsFamilyRepository $teamTimecodedElementsFamilyRepository,
    ) {
        $this->teamTimecodedElementsFamilyRepository = $teamTimecodedElementsFamilyRepository;
    }

    public function __invoke(
        Team $team,
        TimecodedElementsFamily $timecodedElementsFamily,
        ToggleTimecodedElementsFamilyAutoAddNewPresetsRequest $request
    ): JsonResponse {
        $this->authorize('update', $team);

        $this->teamTimecodedElementsFamilyRepository->updateAutoAddNewPresets(
            $timecodedElementsFamily->id,
            $team->id,
            (bool) $request->get('is_enabled')
        );

        return new JsonResponse();
    }
}
