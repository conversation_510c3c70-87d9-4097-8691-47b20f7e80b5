<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\Option;

use Illuminate\Foundation\Http\FormRequest;

final class StoreOptionRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'label' => ['required', 'string', 'max:45'],
            'value' => ['required', 'isValidOptionValue'],
            'thumbnail' => ['required', 'url', 'max:255', 'isValidThumbnailFileExtension'],
            'option_list_id' => ['required', 'exists:screen_parameters_option_lists,id'],
        ];
    }
}
