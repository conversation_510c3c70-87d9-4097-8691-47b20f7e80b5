<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\CustomPage;

use App\Application\Http\Controllers\Api\BaseController;
use App\Domain\Common\Exceptions\EntityNotFoundException;
use App\Domain\CustomPage\CustomPagePublicAccessPolicy;
use App\Domain\CustomPage\CustomPageRepository;
use App\Domain\User\Action\UserAction;
use App\Domain\User\Action\UserActionService;
use App\Infrastructure\CustomPage\Serializers\CustomPagePublicSerializer;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

final class GetCustomPagePublicController extends BaseController
{
    use AuthorizesRequests;

    private CustomPagePublicSerializer $customPagePublicSerializer;

    private CustomPagePublicAccessPolicy $publicAccessPolicy;

    private CustomPageRepository $customPageRepository;

    private UserActionService $userActionService;

    public function __construct(
        CustomPagePublicSerializer $customPagePublicSerializer,
        CustomPagePublicAccessPolicy $publicAccessPolicy,
        CustomPageRepository $customPageRepository,
        UserActionService $userActionService,
    ) {
        $this->customPagePublicSerializer = $customPagePublicSerializer;
        $this->publicAccessPolicy = $publicAccessPolicy;
        $this->customPageRepository = $customPageRepository;
        $this->userActionService = $userActionService;
    }

    public function __invoke(string $token): JsonResponse
    {
        try {
            $customPage = $this->customPageRepository->getByToken($token);
            if ($this->publicAccessPolicy->publicAccess($customPage) === false) {
                throw new AuthorizationException();
            }
        } catch (EntityNotFoundException | AuthorizationException) {
            throw new NotFoundHttpException('Custom page not found');
        }

        $this->userActionService->addUserAction(
            new UserAction('custom-page-accessed', [], null, $customPage->project_id)
        );

        return $this->sendJsonResponse(
            new Collection([$this->customPagePublicSerializer->serialize($customPage)]),
            Response::HTTP_OK
        );
    }
}
