<?php

namespace App\Domain\ProcessedMedia\Transformations;

use App\Models\ProcessedMedia;

interface TransformationsFactoryInterface
{
    public function create(
        int $projectId,
        int $paramId,
        ?array $crop,
        ?string $keepSize,
        ?float $trimStart = null,
        ?float $trimEnd = null
    ): Transformations;

    public function createFromProcessedMediaWithDestinationFormatForResize(
        ProcessedMedia $processedMedia,
        string $destinationFormat
    ): Transformations;
}
