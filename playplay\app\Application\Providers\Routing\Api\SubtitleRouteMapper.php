<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\V2\RenderSubtitle\CreateRenderSubtitleController;
use App\Application\Http\Controllers\Api\V2\RenderSubtitle\GetRenderSubtitleController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class SubtitleRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/render-subtitles',
            'as' => 'render-subtitles.',
        ], static function (Router $router) {
            $router->get('/{render_subtitle}', GetRenderSubtitleController::class)->name('show');
            $router->post('', CreateRenderSubtitleController::class)->name('store');
        });
    }
}
