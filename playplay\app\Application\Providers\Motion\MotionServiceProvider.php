<?php

declare(strict_types=1);

namespace App\Application\Providers\Motion;

use App\Domain\Motion\Repository\MotionConfigDataRepository as MotionConfigDataRepositoryInterface;
use App\Infrastructure\Motion\Repository\MotionConfigDataRepository;
use Illuminate\Contracts\Config\Repository as ConfigRepository;
use Illuminate\Foundation\Application;
use Illuminate\Support\ServiceProvider;

final class MotionServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(MotionConfigDataRepositoryInterface::class, function (Application $app) {
            return new MotionConfigDataRepository($app->get(ConfigRepository::class));
        });
    }
}
