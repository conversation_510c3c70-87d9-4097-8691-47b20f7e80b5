<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\Media\MediaController;
use App\Application\Http\Controllers\Api\Render\RenderJobController;
use App\Application\Http\Controllers\Api\V2\Media\MediaRenderJobCallbackController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class RenderingCallbacksRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([], static function (Router $router) {
            $router
                ->post('renders/job/{job}', [RenderJobController::class, 'callback'])
                ->name('job.callback');
            $router
                ->post('renders/job/{renderJob}/callback-media-workflow', MediaRenderJobCallbackController::class)
                ->name('job.callback-media-workflow');
            $router
                ->post('medias/{media}/callback', [MediaController::class, 'callback'])
                ->name('medias.callback');
            $router
                ->post(
                    'medias/{media}/callback-video-thumbnail',
                    [MediaController::class, 'callbackVideoThumbnail']
                )
                ->name('medias.callbackThumbnail');
        });
    }
}
