<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Admin;

use App\Application\Http\Controllers\Admin\Team\OptionController;
use App\Application\Http\Controllers\Admin\Team\OptionListController;
use App\Application\Http\Controllers\Admin\Team\ScreenController;
use App\Application\Http\Controllers\Admin\Team\TeamScreenController;
use App\Application\Http\Controllers\Admin\Team\TemplateController;
use App\Application\Http\Controllers\Admin\Team\TimecodedElementController;
use App\Application\Http\Controllers\Admin\Team\ToggleTimecodedElementPresetController;
use App\Application\Http\Controllers\Admin\Team\ToggleTimecodedElementsFamilyAutoAddNewPresetsController;
use App\Application\Http\Controllers\Admin\TeamController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class TeamRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/teams',
            'as' => 'teams.'
        ], function (Router $router) {
            $router->post('/', [TeamController::class, 'store'])->name('store');
            $router->get('/create', [TeamController::class, 'create'])->name('create');

            Route::group([
                'prefix' => '/{team}',
            ], function (Router $router) {
                $this->mapAutoAddGenericScreens();
                $this->mapAutoAddGenericTemplateCategories();
                $this->mapOptions();
                $this->mapOptionLists();
                $this->mapTeamScreens();
                $this->mapTemplates();
                $this->mapTimecodedElements();

                $router->put('/', [TeamController::class, 'update'])->name('update');
                $router->delete('/', [TeamController::class, 'destroy'])->name('destroy');
                $router->get('/edit', [TeamController::class, 'edit'])->name('edit');
                $router->get('/duplicate', [TeamController::class, 'duplicate'])->name('duplicate');
            });
        });

        Route::get('teams-filters', [TeamController::class, 'filters'])->name('teams.filters');
    }

    private function mapOptionLists(): void
    {
        Route::group([
            'prefix' => '/option-lists',
            'as' => 'option-lists.',
        ], static function (Router $router) {
            $router->get('/', [OptionListController::class, 'index'])
                ->name('index');
            $router->put('/{optionList}', [OptionListController::class, 'link'])
                ->name('link');
            $router->put('/{optionList}/unlink', [OptionListController::class, 'unlink'])
                ->name('unlink');
        });
    }

    private function mapOptions(): void
    {
        Route::group([
            'prefix' => '/options/{option}',
            'as' => 'options.',
        ], static function (Router $router) {
            $router->put('/enable', [OptionController::class, 'enable'])
                ->name('enable');
            $router->put('/disable', [OptionController::class, 'disable'])
                ->name('disable');
        });
    }

    private function mapTeamScreens(): void
    {
        Route::group([
            'prefix' => '/screens',
            'as' => 'screens.',
        ], static function (Router $router) {
            $router->get('/edit', [ScreenController::class, 'index'])
                ->name('index');
            $router->put('/{screen}/enable', [TeamScreenController::class, 'enable'])
                ->name('enable');
            $router->put('/{screen}/disable', [TeamScreenController::class, 'disable'])
                ->name('disable');
        });
    }

    private function mapTemplates(): void
    {
        Route::group([
            'prefix' => '/templates',
            'as' => 'templates.',
        ], static function (Router $router) {
            $router->get('/', [TemplateController::class, 'index'])
                ->name('index');
            $router->put('/{category}/enable', [TemplateController::class, 'enable'])
                ->name('enable');
            $router->put('/{category}/disable', [TemplateController::class, 'disable'])
                ->name('disable');
        });
    }

    private function mapAutoAddGenericScreens(): void
    {
        Route::put('/enable-auto-add-generic-screens', [TeamController::class, 'enableAutoAddGenericScreens'])
            ->name('enable-auto-add-new-generic-screens');
        Route::put('/disable-auto-add-generic-screens', [TeamController::class, 'disableAutoAddGenericScreens'])
            ->name('disable-auto-add-new-generic-screens');
    }

    private function mapAutoAddGenericTemplateCategories(): void
    {
        Route::put(
            '/enable-auto-add-generic-template-categories',
            [TeamController::class, 'enableAutoAddGenericTemplateCategories']
        )->name('enable-auto-add-new-generic-template-categories');

        Route::put(
            '/disable-auto-add-generic-template-categories',
            [TeamController::class, 'disableAutoAddGenericTemplateCategories']
        )->name('disable-auto-add-new-generic-template-categories');
    }

    private function mapTimecodedElements(): void
    {
        Route::put(
            '/timecoded-element-presets/{timecodedElementPreset}',
            ToggleTimecodedElementPresetController::class
        )->name('timecoded-element-presets.toggle');

        Route::put(
            '/timecoded-elements-families/{timecodedElementsFamily}/toggle-auto-add-new-presets',
            ToggleTimecodedElementsFamilyAutoAddNewPresetsController::class
        )->name('timecoded-element-families.toggle-add-new-presets');

        Route::group([
            'prefix' => '/timecoded-elements',
            'as' => 'timecoded-elements.',
        ], static function (Router $router) {
            $router->get('/', [TimecodedElementController::class, 'edit'])
                ->name('edit');
            $router->post('/', [TimecodedElementController::class, 'store'])
                ->name('store');
            $router->delete('/{timecodedElementsFamilyId}', [TimecodedElementController::class, 'destroy'])
                ->name('destroy');
        });
    }
}
