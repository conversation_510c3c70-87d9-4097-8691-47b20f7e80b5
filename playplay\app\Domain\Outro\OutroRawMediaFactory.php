<?php

declare(strict_types=1);

namespace App\Domain\Outro;

use App\Domain\RawMedia\RawMediaService;
use App\Domain\RawMedia\RawMediaSource;
use App\Domain\RawMedia\RawMediaType;
use App\Models\RawMedia;
use App\Services\FileManager;
use Illuminate\Http\Testing\MimeType;

class OutroRawMediaFactory
{
    private FileManager $fileManager;
    private RawMediaService $rawMediaService;

    public function __construct(FileManager $fileManager, RawMediaService $rawMediaService)
    {
        $this->fileManager = $fileManager;
        $this->rawMediaService = $rawMediaService;
    }

    public function createRawMediaForOutroFile(string $filePath, string $fileName, int $teamPresetId): RawMedia
    {
        $md5 = md5($filePath);
        $url = $this->fileManager->copyGcsFile($filePath, "team-presets/{$teamPresetId}/outros/{$md5}");
        $contentType = MimeType::from(parse_url($url, PHP_URL_PATH));
        $type = str_contains($contentType, 'video/') ? RawMediaType::VIDEO : RawMediaType::IMAGE;

        return $this->rawMediaService->findOrCreateRawMedia([
            'source' => RawMediaSource::upload(),
            'name' => $fileName,
            'type' => new RawMediaType($type),
            'content_type' => MimeType::from(parse_url($url, PHP_URL_PATH)),
            'md5' => $md5,
            'url' => $url,
        ]);
    }
}
