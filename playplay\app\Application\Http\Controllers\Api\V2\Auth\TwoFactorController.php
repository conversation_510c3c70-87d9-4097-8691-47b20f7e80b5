<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Auth;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Auth\TwoFactorAuthCodeRequest;
use App\Application\Mail\TwoFactorAuthCode;
use App\Domain\TwoFactorAuth\CodeGenerator;
use App\Domain\User\UserRepository;
use Illuminate\Contracts\Mail\Mailer;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class TwoFactorController extends BaseController
{
    private Mailer $mailer;
    private UserRepository $userRepository;
    private CodeGenerator $codeGenerator;

    public function __construct(
        Mailer $mailer,
        CodeGenerator $codeGenerator,
        UserRepository $userRepository
    ) {
        $this->mailer = $mailer;
        $this->codeGenerator = $codeGenerator;
        $this->userRepository = $userRepository;
    }

    public function __invoke(TwoFactorAuthCodeRequest $request): JsonResponse
    {
        if (!$this->userRepository->userMustProvide2FA($request->input('email'))) {
            return $this->sendJsonResponse(new Collection([]), Response::HTTP_FORBIDDEN);
        }

        $user = $this->userRepository->getUserFromEmail($request->input('email'));
        $code = $this->codeGenerator->generate($user);

        // Todo : Refactor to use event/listener
        $this->mailer->to($user)->send(new TwoFactorAuthCode($user, $code));

        return $this->sendJsonResponse(new Collection([[]]), Response::HTTP_OK);
    }
}
