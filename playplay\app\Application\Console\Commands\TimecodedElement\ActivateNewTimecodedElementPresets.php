<?php

declare(strict_types=1);

namespace App\Application\Console\Commands\TimecodedElement;

use App\Domain\TimecodedElement\Repositories\TeamTimecodedElementPresetScheduledActivationRepository;
use App\Domain\TimecodedElement\TeamTimecodedElementPresetScheduledActivationService;
use App\Models\TimecodedElement\TeamTimecodedElementPresetScheduledActivation;
use Illuminate\Console\Command;

final class ActivateNewTimecodedElementPresets extends Command
{
    protected $signature = 'timecoded-elements:activate-presets';
    protected $description = 'Check scheduled activations of TimecodedElementPresets for Teams and handle them';

    private TeamTimecodedElementPresetScheduledActivationRepository $scheduledActivationRepository;
    private TeamTimecodedElementPresetScheduledActivationService $scheduledActivationService;

    public function __construct(
        TeamTimecodedElementPresetScheduledActivationRepository $scheduledActivationRepository,
        TeamTimecodedElementPresetScheduledActivationService $scheduledActivationService
    ) {
        parent::__construct();
        $this->scheduledActivationRepository = $scheduledActivationRepository;
        $this->scheduledActivationService = $scheduledActivationService;
    }

    public function handle(): int
    {
        $pendingScheduledActivations = $this->scheduledActivationRepository->getAllToProcess();

        $pendingScheduledActivations
            ->each(function (TeamTimecodedElementPresetScheduledActivation $pendingScheduledActivation) {
                $this->scheduledActivationService->handle($pendingScheduledActivation);
            });

        return 0;
    }
}
