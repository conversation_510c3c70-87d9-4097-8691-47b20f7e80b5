<?php

namespace App\Application\Events;

use App\Models\RenderError;
use Illuminate\Queue\SerializesModels;

class RenderInError
{
    use SerializesModels;

    private RenderError $renderError;

    public function __construct(RenderError $renderError)
    {
        $this->renderError = $renderError;
    }

    public function getRenderError(): RenderError
    {
        return $this->renderError;
    }
}
