<?php

namespace App\Application\Console\Commands\Users;

use App\Domain\Localization\SupportedLanguages;
use App\Models\Company;
use App\Services\SignUpService;
use Faker\Generator;
use Illuminate\Console\Command;
use Illuminate\Log\Logger;
use Illuminate\Support\Facades\App;
use RuntimeException;

final class GenerateFreeTrialUser extends Command
{
    private const PASSWORD = 'password';

    private SignUpService $signUpService;
    private Generator $faker;
    private Logger $logger;

    public function __construct(SignUpService $signUpService, Generator $faker, Logger $logger)
    {
        parent::__construct();
        $this->signUpService = $signUpService;
        $this->faker = $faker;
        $this->logger = $logger;
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:free-trial';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Generate a new free-trial user.";

    public function handle(): int
    {
        if (App::environment() === 'production') {
            $this->logger->warning('This cannot be run in production');
            return 1;
        }

        $userData = $this->freeTrialUserData();
        $user = $this->signUpService->createAccount($userData, [])[0];

        Company::where('id', $user->company->id)->update(['two_fa_enabled' => 0]);

        $this->info("SUCCESS: User created => Email: {$user->email} | Password: " . self::PASSWORD);

        return 0;
    }

    private function freeTrialUserData(): array
    {
        return [
            'email' => $this->faker->unique()->email,
            'first_name' => $this->faker->firstName,
            'last_name' => $this->faker->lastName,
            'terms' => 1,
            'password' => self::PASSWORD,
            'language' => SupportedLanguages::EN,
        ];
    }
}
