<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\Screen\Category;

use Illuminate\Foundation\Http\FormRequest;

final class ScreenCategoryReorderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'screen_category_ids.*' => ['required', 'exists:screen_categories,id'],
        ];
    }
}
