<?php

namespace App\Application\Http\Controllers\Api\V2\Billing;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Billing\BillingUpdateRequest;
use App\Domain\Billing\BillingException;
use App\Domain\User\Action\UserAction;
use App\Domain\User\Action\UserActionService;
use App\Infrastructure\Billing\StripeSubscriptionService;
use App\Models\Company;
use App\Models\User;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Stripe\Customer;
use Stripe\Exception\ApiErrorException;
use Stripe\Exception\CardException;
use Stripe\Exception\InvalidRequestException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class BillingController extends BaseController
{
    use AuthorizesRequests;

    private Guard $auth;
    private UserActionService $userActionService;
    private StripeSubscriptionService $stripeSubscriptionService;

    public function __construct(
        StripeSubscriptionService $stripeSubscriptionService,
        Guard $auth,
        UserActionService $userActionService
    ) {
        $this->stripeSubscriptionService = $stripeSubscriptionService;
        $this->auth = $auth;
        $this->userActionService = $userActionService;
    }

    /**
     * @throws AuthorizationException|ApiErrorException
     */
    public function index(Guard $auth): JsonResponse
    {
        /** @var User $authUser */
        $authUser = $auth->user();
        if (!$authUser->has_subscriptions) {
            throw new AuthorizationException();
        }

        $company = $authUser->company;
        $this->authorize('canAccessRestrictedData', $company);
        $customer = $this->stripeSubscriptionService->getCustomer($company);

        return $this->sendJsonResponse(
            new Collection([$this->getStripeResult($company, $customer)]),
            Response::HTTP_OK
        );
    }

    /**
     * @throws AuthorizationException
     */
    public function update(BillingUpdateRequest $request): JsonResponse
    {

        /** @var User $authenticatedUser */
        $authenticatedUser = $this->auth->user();
        if (!$authenticatedUser->has_subscriptions) {
            throw new AuthorizationException();
        }

        try {
            $company = $authenticatedUser->company;

            if ($request->has('billing_info')) {
                $customer = $this->stripeSubscriptionService->updateBillingInfo(
                    $company,
                    $request->only([
                        'billing_info.country',
                        'billing_info.company_name',
                        'billing_info.address_line1',
                        'billing_info.address_line2',
                        'billing_info.address_postal_code',
                        'billing_info.address_state',
                        'billing_info.address_city',
                        'billing_info.vat',
                    ])
                );
                $this->userActionService->addUserAction(new UserAction('billing-info-updated'));
            }

            if ($request->has('stripe_token')) {
                $customer = $this->stripeSubscriptionService->createOrUpdateCustomer(
                    $authenticatedUser,
                    $company,
                    $request->get('stripe_token')
                );
                $this->userActionService->addUserAction(new UserAction('payment-method-updated'));
            }

            if (!isset($customer)) {
                throw new NotFoundHttpException('Stripe Customer not found');
            }

            return $this->sendJsonResponse(
                new Collection([$this->getStripeResult($company, $customer)]),
                Response::HTTP_OK
            );
        } catch (CardException | InvalidRequestException $exception) {
            throw new BillingException($exception->getHttpBody(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (ApiErrorException $exception) {
            throw new BillingException($exception->getHttpBody(), $exception->getHttpStatus());
        }
    }

    /**
     * @throws ApiErrorException
     */
    private function getStripeResult(Company $company, Customer $customer): array
    {
        $currentSubscription = $company->getCurrentSubscription();
        $stripeSubscription = null;
        if ($currentSubscription !== null) {
            $stripeSubscription =
                $this->stripeSubscriptionService->getSubscriptionVueWithStripeData($currentSubscription);
        }

        return [
            'current_subscription' => $stripeSubscription,
            'billing_info' => [
                'company_name' => data_get($customer, 'name'),
                'country' => data_get($customer, 'address.country'),
                'address_line1' => data_get($customer, 'address.line1'),
                'address_line2' => data_get($customer, 'address.line2'),
                'address_postal_code' => data_get($customer, 'address.postal_code'),
                'address_state' => data_get($customer, 'address.state'),
                'address_city' => data_get($customer, 'address.city'),
                'vat' => data_get($customer, 'tax_ids.data.0.value'),
            ],
            'invoices' => $this->stripeSubscriptionService->getInvoicesForCustomer($customer),
            'payment_method' => $this->stripeSubscriptionService->getPaymentMethod($customer),
            'billing_plan' => $company->lastSubscription === null
                ? []
                : $this->stripeSubscriptionService->getBillingPlan($company->lastSubscription->billingPlan),
        ];
    }
}
