<?php

declare(strict_types=1);

namespace App\Domain\Metrics;

final class Metric
{
    private static array $descriptionByName = [
        'active_users' => 'Number of active users.',
        'total_duration_in_render' => 'Total duration in render queue.',
        'cluster_size' => 'The cluster size.',
        'messages_in_media_processing_queue' => 'The number of messages in media processing queue.',
        'messages_in_media_processing_dl_queue' => 'The number of messages in media processing dead letter queue.',
        'messages_in_video_rendering_queue' => 'The number of messages in video rendering queue.',
        'messages_in_video_rendering_dl_queue' => 'The number of messages in video rendering dead letter queue.',
    ];

    private static array $metricNameByQueueName = [
        'queue_processing' => 'messages_in_media_processing_queue',
        'queue_processing_dlq' => 'messages_in_media_processing_dl_queue',
        'queue_rendering' => 'messages_in_video_rendering_queue',
        'queue_rendering_dlq' => 'messages_in_video_rendering_dl_queue',
    ];

    public static function activeUsers(int $activeUsers): self
    {
        return self::createFrom('active_users', $activeUsers);
    }

    public static function totalDurationInRender(int $renderTotalDuration): self
    {
        return self::createFrom('total_duration_in_render', $renderTotalDuration);
    }

    public static function clusterSize(int $clusterSize): self
    {
        return self::createFrom('cluster_size', $clusterSize);
    }

    public static function createFrom(string $name, int $value): self
    {
        return new self(self::$metricNameByQueueName[$name] ?? $name, self::$descriptionByName[$name] ?? '', $value);
    }

    private string $name;
    private int $value;
    private string $description;

    private function __construct(string $name, string $description, int $value)
    {
        $this->name = $name;
        $this->value = $value;
        $this->description = $description;
    }

    public function name(): string
    {
        return $this->name;
    }

    public function description(): string
    {
        return $this->description;
    }

    public function value(): int
    {
        return $this->value;
    }
}
