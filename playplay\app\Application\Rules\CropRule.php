<?php

declare(strict_types=1);

namespace App\Application\Rules;

use Illuminate\Contracts\Validation\Rule;

class CropRule implements Rule
{
    private const TYPE_KEYS = ['absolute', 'relative'];
    private const ALPHABETICAL_SORTED_REQUIRED_KEYS = [
        'height',
        'width',
        'x',
        'y',
    ];

    public function message(): string
    {
        return trans('validation.crop');
    }

    /**
     * @param string $attribute
     * @param mixed  $value
     *
     * @return bool
     */
    public function passes($attribute, $value): bool
    {
        if (!is_array($value)) {
            return false;
        }

        if ($value === []) {
            return false;
        }

        if (($value['absolute'] ?? null) === null && ($value['relative'] ?? null) === null) {
            return $this->checkIfAllValuesAreNumeric($value);
        } else {
            return $this->checkCrop($value);
        }
    }

    private function checkCrop(array $value): bool
    {
        foreach (self::TYPE_KEYS as $type) {
            if (array_key_exists($type, $value) === false) {
                return false;
            }

            if (!$this->checkIfAllValuesAreNumeric($value[$type])) {
                return false;
            }
        }

        return true;
    }

    private function checkIfAllValuesAreNumeric(array $properties): bool
    {
        $numericValues = array_keys(array_filter($properties, fn($value) => is_numeric($value)));
        sort($numericValues);

        return array_intersect_key(self::ALPHABETICAL_SORTED_REQUIRED_KEYS, $numericValues)
            === self::ALPHABETICAL_SORTED_REQUIRED_KEYS;
    }
}
