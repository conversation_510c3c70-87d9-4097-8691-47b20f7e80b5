<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\V2\Screens\ScreensController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class ScreenRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'as' => 'project.screens.',
        ], static function (Router $router) {
            $router->get('/projects/{project}/screens', [ScreensController::class, 'index'])->name('index');
        });
    }
}
