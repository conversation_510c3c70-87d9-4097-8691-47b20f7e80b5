<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\TimecodedElement;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\TimecodedElement\ReorderTimecodedElementsFamilyPresetsRequest;
use App\Domain\TimecodedElement\Repositories\TimecodedElementPresetRepository;
use Illuminate\Http\JsonResponse;

final class ReorderTimecodedElementsFamilyPresetsController extends BaseController
{
    private TimecodedElementPresetRepository $timecodedElementPresetRepository;

    public function __construct(
        TimecodedElementPresetRepository $timecodedElementPresetRepository,
    ) {
        $this->timecodedElementPresetRepository = $timecodedElementPresetRepository;
    }

    public function __invoke(ReorderTimecodedElementsFamilyPresetsRequest $request): JsonResponse
    {
        $this->timecodedElementPresetRepository->updateOrders(
            $request->get('preset_orders')
        );

        return new JsonResponse();
    }
}
