<?php

declare(strict_types=1);

namespace App\Domain\Project\Sanitizer;

use App\Models\Project;

final class ProjectVoiceoverSanitizer implements ProjectElementsSanitizer
{
    public function sanitize(Project $project): bool
    {
        if ($this->isVoiceoverAllowed($project)) {
            return false;
        }

        return $project->voiceovers()->delete() > 0;
    }

    private function isVoiceoverAllowed(Project $project): bool
    {
        return $project->company->getHasVoiceoverAttribute();
    }
}
