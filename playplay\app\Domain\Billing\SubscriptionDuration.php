<?php

declare(strict_types=1);

namespace App\Domain\Billing;

use DateInterval;
use DateTimeImmutable;
use DateTimeInterface;

final class SubscriptionDuration
{
    public readonly string $endDate;

    public readonly string $duration;

    public function __construct(DateTimeInterface $startDate, DateTimeInterface $endDate)
    {
        $this->endDate = $endDate->format('d/m/Y');
        $this->duration = $this->formatDuration($startDate, $endDate);
    }

    private function formatDuration(DateTimeInterface $startDate, DateTimeInterface $endDate): string
    {
        $startDateMidnight = (new DateTimeImmutable())
            ->setTimestamp($startDate->getTimestamp())
            ->setTime(0, 0);

        $endDatePlusOneDayAtMidnight = (new DateTimeImmutable())
            ->setTimestamp($endDate->getTimestamp())
            ->add(new DateInterval('P1D'))
            ->setTime(0, 0);

        $duration = $endDatePlusOneDayAtMidnight->diff($startDateMidnight);
        $months = $duration->m + ($duration->y * 12);

        return $months === 1 ? '1 month' : "{$months} months";
    }
}
