<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Admin;

use App\Application\Http\Controllers\Admin\BillingPlanController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class BillingPlanRouteMapper implements RouteMapper
{

    public function map(): void
    {
        Route::resource('billing-plans', BillingPlanController::class)
            ->except('delete', 'destroy', 'edit');
    }
}
