<?php

namespace App\Application\Rules;

use Illuminate\Contracts\Validation\Rule;

class InManyRule implements Rule
{
    private const SEPARATOR = ',';

    public function message(): string
    {
        return trans('validation.in_many');
    }

    public function passes($attribute, $value, array $parameters = []): bool
    {
        $array = explode(self::SEPARATOR, $value);
        foreach ($array as $element) {
            if (!in_array($element, $parameters, true)) {
                return false;
            }
        }

        return true;
    }
}
