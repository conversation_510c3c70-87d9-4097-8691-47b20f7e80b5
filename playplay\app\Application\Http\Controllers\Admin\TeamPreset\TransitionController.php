<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\TeamPreset;

use App\Application\Http\Requests\Admin\TeamPresets\TeamPresetTransitionUpdateRequest;
use App\Models\Team;
use App\Models\TeamPreset;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Redirector;

final class TransitionController extends AbstractTeamPresetController
{
    private Redirector $redirector;

    public function __construct(Redirector $redirector)
    {
        parent::__construct();
        $this->redirector = $redirector;
    }

    public function update(
        Team $team,
        TeamPreset $teamPreset,
        TeamPresetTransitionUpdateRequest $request
    ): RedirectResponse {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('update', $teamPreset);

        $teamPreset->transitions = $request->get('transitions');

        $teamPreset->save();

        return $this->redirector->back();
    }
}
