<?php

namespace App\Domain\Font;

use App\Domain\Common\Exceptions\EntityNotFoundException;
use App\Models\Font;
use Illuminate\Support\Collection;

interface FontRepository
{
    public function getBasicFonts(): Collection;

    /** @throws EntityNotFoundException */
    public function getById(int $fontId): Font;

    public function getFonts(array $fontIds): Collection;

    public function update(int $fontId, array $data): void;

    public function deleteFonts(array $fontIds): void;
}
