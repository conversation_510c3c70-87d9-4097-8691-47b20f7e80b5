<?php

namespace App\Application\Providers;

use App\Domain\CorporateLibrary\CorporateLibraryItemRepository;
use App\Infrastructure\CorporateLibrary\EloquentCorporateLibraryItemRepository;
use Illuminate\Support\ServiceProvider;

class CorporateLibraryProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(CorporateLibraryItemRepository::class, EloquentCorporateLibraryItemRepository::class);
    }
}
