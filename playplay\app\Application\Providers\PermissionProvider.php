<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Permissions\AppRoleAppPermissionRepository;
use App\Domain\Permissions\AppRoleRepository;
use App\Infrastructure\Permissions\EloquentAppRoleRepository;
use App\Infrastructure\Permissions\EloquentAppRoleAppPermissionRepository;
use Illuminate\Support\ServiceProvider;

final class PermissionProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->bind(AppRoleRepository::class, EloquentAppRoleRepository::class);
        $this->app->bind(AppRoleAppPermissionRepository::class, EloquentAppRoleAppPermissionRepository::class);
    }
}
