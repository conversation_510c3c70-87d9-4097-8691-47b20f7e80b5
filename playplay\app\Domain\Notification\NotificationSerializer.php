<?php

declare(strict_types=1);

namespace App\Domain\Notification;

use App\Models\Notification;

class NotificationSerializer
{
    public function serialize(Notification $notification): array
    {
        return [
            'id' => $notification->id,
            'type' => $notification->type,
            'content' => json_decode($notification->content, true, 512, JSON_THROW_ON_ERROR),
            'username' => $notification->username,
            'url' => $notification->url,
            'viewed' => $notification->viewed,
            'created_at' => $notification->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
