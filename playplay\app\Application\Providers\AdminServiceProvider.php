<?php

declare(strict_types=1);

namespace App\Application\Providers;

use Illuminate\Pagination\Paginator;
use Illuminate\Support\ServiceProvider;
use Spatie\Menu\Laravel\MenuServiceProvider;
use Spatie\Permission\PermissionServiceProvider;
use UxWeb\SweetAlert\SweetAlertServiceProvider;

final class AdminServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->app->register(AdminViewHelpersServiceProvider::class);
        $this->app->register(SweetAlertServiceProvider::class);
        $this->app->register(MenuServiceProvider::class);
        $this->app->register(PermissionServiceProvider::class);

        Paginator::useBootstrap();
    }
}
