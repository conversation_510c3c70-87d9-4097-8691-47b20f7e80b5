<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\ProcessedMedia;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\ProcessedMedia\Store\ProcessedMediaVideoTransformationsStoreRequest;
use App\Application\Http\Requests\Api\ProcessedMedia\Update\ProcessedMediaVideoTransformationsUpdateRequest;
use App\Infrastructure\ProcessedMedia\Transformations\TransformationsFactory;
use App\Models\ProcessedMedia;
use App\Services\ProcessedMedia\AccessChecker;
use App\Services\ProcessedMedia\Factory;
use App\Services\Processing\RenderMedia\VideoService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

final class VideoController extends BaseController
{
    use AuthorizesRequests;

    private Factory $processedMediaFactory;
    private AccessChecker $accessChecker;
    private VideoService $videoService;
    private TransformationsFactory $transformationsFactory;

    public function __construct(
        AccessChecker $accessChecker,
        Factory $processedMediaFactory,
        VideoService $videoService,
        TransformationsFactory $transformationsFactory
    ) {
        $this->accessChecker = $accessChecker;
        $this->processedMediaFactory = $processedMediaFactory;
        $this->videoService = $videoService;
        $this->transformationsFactory = $transformationsFactory;
    }

    public function storeTransformations(
        ProcessedMediaVideoTransformationsStoreRequest $request
    ): JsonResponse {
        $rawMediaId = $request->get('raw_media_id');
        $source = $request->get('source');
        $projectId = $request->get('project_id');

        $this->accessChecker->checkIfCanCreateProcessedMediaFromRawMediaAndSourceAndProject(
            $rawMediaId,
            $source,
            $projectId
        );

        $processedMedia = $this->processedMediaFactory->create(
            $rawMediaId,
            $source,
            $projectId
        );

        $transformations = $this->transformationsFactory->create(
            $projectId,
            $request->get('param_id'),
            $request->get('crop'),
            $request->get('keepSize'),
            $request->get('trimStart'),
            $request->get('trimEnd')
        );

        $this->videoService->applyTransformations($processedMedia, $transformations);

        return $this->sendJsonResponse(new Collection([$processedMedia]), Response::HTTP_CREATED);
    }

    public function updateTransformations(
        ProcessedMedia $processedMedia,
        ProcessedMediaVideoTransformationsUpdateRequest $request
    ): JsonResponse {
        $this->authorize('update', $processedMedia);

        if (!$processedMedia->rawMedia->isVideo()) {
            throw new BadRequestHttpException();
        }

        $transformations = $this->transformationsFactory->create(
            $processedMedia->project_id,
            $request->get('param_id'),
            $request->get('crop'),
            $request->get('keepSize'),
            $request->get('trimStart'),
            $request->get('trimEnd'),
        );

        $this->videoService->applyTransformations(
            $processedMedia,
            $transformations
        );

        return $this->sendJsonResponse(new Collection([$processedMedia]), Response::HTTP_OK);
    }
}
