<?php

declare(strict_types=1);

namespace App\Application\Mail;

use App\Models\Renders\RenderProjectHtml;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

final class FirstGeneratedProjectEmail extends Mailable
{
    use Queueable, SerializesModels;

    private RenderProjectHtml $render;

    public function __construct(RenderProjectHtml $render)
    {
        $this->render = $render;

        $subject = "[First Video] {$render->project->user->name} de {$render->project->company->name}";
        $this->subject($subject);
    }

    public function build(): FirstGeneratedProjectEmail
    {
        return $this->view('emails.first-generated-render-email', [
            'renderProject' => $this->render,
        ]);
    }
}
