<?php

namespace App\Domain\Feature;

use App\Domain\Plan\PlanCollection;
use App\Models\Feature;

class FeatureViewModel
{
    private readonly int $id;

    private readonly string $displayedName;

    private readonly string $type;

    private readonly ?int $value;

    private readonly bool $isEnterpriseFeature;

    private readonly bool $isDisabled;

    private readonly array $featureValuesForPlans;

    public function __construct(
        PlanCollection $plans,
        Feature $feature,
        ?int $value
    ) {
        $this->id = $feature->id;
        $this->type = $feature->type;
        $this->displayedName = $feature->displayed_name;
        $this->value = $value;
        $this->isEnterpriseFeature = $plans->isEnterpriseFeature($feature);
        $this->featureValuesForPlans = $plans->buildFeatureValueForPlans($feature->name);
        $this->isDisabled = $feature->is_disabled_in_backoffice;
    }

    public function getDisplayedName(): string
    {
        return $this->displayedName;
    }

    public function getFeatureDOMAttributes(): array
    {
        $dataPlanIds = [
            'data-initial-value' => $this->getValue(),
        ];

        foreach ($this->featureValuesForPlans as $planId => $planValue) {
            $dataPlanIds["data-plan-$planId"] = $planValue;
        }

        return $dataPlanIds;
    }

    /**
     * The name of the bootstrap's radio input (1st parameter) can't be named `features`
     * Due to how bootstrap from radio elements work. Bootstrap form will ignore the 3rd parameter which is the value
     * of `checked` if the input name (1st parameter) exists as an attribute on the $model we are working with,
     * in that case the form will compare the default value (2nd parameter) with $model->{input_name} to see whether
     * the input should be checked or not (In the case of the company model the checked value will compare the
     * default value (0|1) with $company->features which is a collection and thus results in 'checked' always false)
     */
    public function getFormName(): string
    {
        return "feature_items[{$this->id}]";
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getValue(): ?int
    {
        return $this->value;
    }

    public function isEnterpriseFeature(): bool
    {
        return $this->isEnterpriseFeature;
    }

    public function isDisabled(): bool
    {
        return $this->isDisabled;
    }
}
