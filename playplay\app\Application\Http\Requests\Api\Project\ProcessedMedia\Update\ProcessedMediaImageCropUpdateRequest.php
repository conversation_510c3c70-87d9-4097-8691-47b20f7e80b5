<?php

namespace App\Application\Http\Requests\Api\Project\ProcessedMedia\Update;

use Illuminate\Foundation\Http\FormRequest;

class ProcessedMediaImageCropUpdateRequest extends FormRequest
{
    public static function getRules()
    {
        return [
            'param_id' => ['required', 'numeric', 'exists:layout_params,id', 'paramIsInProject'],
            'crop' => ['required', 'crop'],
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return static::getRules();
    }
}
