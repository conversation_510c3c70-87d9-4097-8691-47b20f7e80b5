<?php

namespace App\Application\Providers;

use App\Application\Rules\CheckAudioLevelsRule;
use App\Application\Rules\CheckColorsIsCorrectRule;
use App\Application\Rules\CheckOrderIsCorrectRule;
use App\Application\Rules\CheckParamAreValidRule;
use App\Application\Rules\CheckParamInScreenRule;
use App\Application\Rules\CheckPasswordRule;
use App\Application\Rules\CheckSettingsRule;
use App\Application\Rules\CheckValueIsCorrectRule;
use App\Application\Rules\CountryISO3166Alpha2Rule;
use App\Application\Rules\CropRule;
use App\Application\Rules\CutawayShotItemDurationRule;
use App\Application\Rules\CutawayShotTimelineItemsDurationRule;
use App\Application\Rules\CutawayShotTimelineRule;
use App\Application\Rules\GtRule;
use App\Application\Rules\HexColorRule;
use App\Application\Rules\HexColorWithoutSharpRule;
use App\Application\Rules\InManyRule;
use App\Application\Rules\IsAudioRule;
use App\Application\Rules\IsGifRule;
use App\Application\Rules\IsImageRule;
use App\Application\Rules\IsValidOptionValueRule;
use App\Application\Rules\IsValidThumbnailFileExtensionRule;
use App\Application\Rules\IsValidTimecodedElementsFamilyTypeRule;
use App\Application\Rules\IsVideoRule;
use App\Application\Rules\MaxDifferenceRule;
use App\Application\Rules\NameRule;
use App\Application\Rules\ParamIsInProjectRule;
use App\Application\Rules\PositionRule;
use App\Application\Rules\RGBColorRule;
use App\Application\Rules\UniqueActiveRule;
use App\Application\Rules\UniqueFreeTrialActiveRule;
use App\Application\Rules\UniqueInactiveRule;
use Illuminate\Contracts\Validation\Factory;
use Illuminate\Support\ServiceProvider;

class CustomFormValidatorServiceProvider extends ServiceProvider
{
    public function boot(Factory $validator): void
    {
        $validator->extend('rgbColor', RGBColorRule::class . '@passes');
        $validator->extend('position', PositionRule::class . '@passes');
        $validator->extend('isImage', IsImageRule::class . '@passes');
        $validator->extend('isVideo', IsVideoRule::class . '@passes');
        $validator->extend('isGif', IsGifRule::class . '@passes');
        $validator->extend('isAudio', IsAudioRule::class . '@passes');
        $validator->extend('paramIsInProject', ParamIsInProjectRule::class . '@passes');
        $validator->extend('uniqueUserActive', UniqueActiveRule::class . '@passes');
        $validator->extend('uniqueUserInactive', UniqueInactiveRule::class . '@passes');
        $validator->extend('uniqueFreeTrialActive', UniqueFreeTrialActiveRule::class . '@passes');
        $validator->extend('greater_than', function ($attribute, $value, $parameters, $validator) {
            return (new GtRule())->passes($attribute, $value, $parameters, $validator);
        });
        $validator->extend('maxDifference', function ($attribute, $value, $parameters, $validator) {
            return (new MaxDifferenceRule())->passes($attribute, $value, $parameters, $validator);
        });
        $validator->extend('name', NameRule::class . '@passes');
        $validator->extend('hexColor', HexColorRule::class . '@passes');
        $validator->extend('hexColorWithoutSharp', HexColorWithoutSharpRule::class . '@passes');
        $validator->extend('country_iso3166_alpha2', CountryISO3166Alpha2Rule::class . '@passes');
        $validator->extend('check_password_rule', CheckPasswordRule::class . '@passes');
        $validator->extend('in_many', function ($attribute, $value, $parameters) {
            return (new InManyRule())->passes($attribute, $value, $parameters);
        });
        $validator->extend('crop', function ($attribute, $value) {
            return (new CropRule())->passes($attribute, $value);
        });
        $validator->extend('isValidThumbnailFileExtension', IsValidThumbnailFileExtensionRule::class . '@passes');
        $validator->extend('isValidOptionValue', IsValidOptionValueRule::class . '@passes');
        $validator->extend('isValidCutawayShotTimeline', CutawayShotTimelineRule::class . '@passes');
        $validator->extend('isValidCutawayShotItemDuration', function ($attribute, $value, $parameters, $validator) {
            return $this->app->make(CutawayShotItemDurationRule::class)
                ->passes(
                    $attribute,
                    $value,
                    $parameters,
                    $validator
                );
        });
        $validator->extend(
            'areValidCutawayShotTimelineItemsDuration',
            function ($attribute, $value, $parameters, $validator) {
                return $this->app->make(CutawayShotTimelineItemsDurationRule::class)
                    ->passes(
                        $attribute,
                        $value,
                        $parameters,
                        $validator
                    );
            }
        );
        $validator->extend(
            'isValidTimecodedElementsFamilyType',
            function ($attribute, $value, $parameters, $validator) {
                return $this->app->make(IsValidTimecodedElementsFamilyTypeRule::class)
                    ->passes(
                        $attribute,
                        $value,
                        $parameters,
                        $validator
                    );
            }
        );
        $validator->extend('checkColors', CheckColorsIsCorrectRule::class . '@passes');
        $validator->extend('orderIsCorrect', CheckOrderIsCorrectRule::class . '@passes');
        $validator->extend('checkParamAreValid', CheckParamAreValidRule::class . '@passes');
        $validator->extend('isInScreen', CheckParamInScreenRule::class . '@passes');
        $validator->extend('valueIsCorrect', CheckValueIsCorrectRule::class . '@passes');
        $validator->extend('checkAudioLevels', CheckAudioLevelsRule::class . '@passes');
        $validator->extend('checkSettings', CheckSettingsRule::class . '@passes');
    }
}
