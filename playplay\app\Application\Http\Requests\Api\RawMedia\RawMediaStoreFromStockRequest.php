<?php

namespace App\Application\Http\Requests\Api\RawMedia;

use App\Domain\Project\RawMedia\RelationType;
use App\Domain\RawMedia\RawMediaSource;
use App\Services\Stock\AStock;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RawMediaStoreFromStockRequest extends FormRequest
{
    public static function getRules(): array
    {
        return [
            'type' => [
                'required',
                'in:' . implode(',', [
                    AStock::TYPE_IMAGE,
                    AStock::TYPE_VIDEO,
                    AStock::TYPE_GIF,
                ]),
            ],
            'source' => [
                'required',
                'in:' . implode(',', RawMediaSource::getAvailableStockSources()),
            ],
            'file_url' => ['sometimes', 'url'],
            'project_id' => ['required', 'exists:projects,id'],
            'relation_type_key' => [
                'nullable',
                Rule::in(RelationType::getAllKeys()),
            ],
            'stock_id' => ['required'],
            'height' => ['sometimes', 'numeric'],
            'download_size' => ['sometimes', 'required', 'string'],
            'credit' => ['sometimes'],
        ];
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return static::getRules();
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'relation_type_key' => $this->route('relationTypeKey'),
        ]);
    }
}
