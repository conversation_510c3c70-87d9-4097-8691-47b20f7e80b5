<?php

namespace App\Application\Http\Requests\Api\Project;

use App\Domain\Project\ProjectFormat;
use App\Models\Project;
use Illuminate\Foundation\Http\FormRequest;

class ProjectIndexRequest extends FormRequest
{
    public static function rules(): array
    {
        return [
            'page' => ['sometimes', 'integer', 'min:1'],
            'per_page' => ['sometimes', 'integer', 'min:1', 'max:100'],
            'user_id' => ['sometimes', 'integer'],
            'team_id' => ['sometimes', 'integer'],
            'format' => ['sometimes', 'string', 'in:' . implode(',', ProjectFormat::ALL_FORMATS)],
            'updated_since' => ['sometimes', 'date'],
            'status' => ['sometimes', 'array', 'in:' . implode(',', [Project::STATUS_DOWNLOADED, Project::STATUS_GENERATED, Project::STATUS_DRAFT])],
        ];
    }

    public function authorize(): bool
    {
        return true;
    }
}
