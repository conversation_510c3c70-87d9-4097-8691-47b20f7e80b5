<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Auth;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Auth\SsoCallbackLoginRequest;
use App\Domain\Sso\InvalidCodeException;
use App\Domain\Sso\LoginService;
use App\Domain\Sso\ServerErrorException;
use Illuminate\Contracts\Cache\Repository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Psr\Log\LoggerInterface;
use Ramsey\Uuid\UuidFactoryInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotAcceptableHttpException;

final class SsoCallbackLoginController extends BaseController
{
    private LoginService $loginService;
    private UuidFactoryInterface $uuidFactory;
    private Repository $cacheRepository;
    private LoggerInterface $logger;
    private Redirector $redirector;

    public function __construct(
        LoginService $loginService,
        UuidFactoryInterface $uuidFactory,
        Repository $cacheRepository,
        LoggerInterface $logger,
        Redirector $redirector
    ) {
        $this->loginService = $loginService;
        $this->uuidFactory = $uuidFactory;
        $this->cacheRepository = $cacheRepository;
        $this->logger = $logger;
        $this->redirector = $redirector;
    }

    public function callbackLogin(SsoCallbackLoginRequest $request): RedirectResponse
    {
        $this->loginUser($request);

        return $this->redirector->to('app/home');
    }

    public function getLoginSsoMobileToken(SsoCallbackLoginRequest $request): JsonResponse
    {
        $this->loginUser($request);

        $token = $this->uuidFactory->uuid4()->toString();
        $this->cacheRepository->forever($token, Auth::id());

        return $this->sendJsonResponse(new Collection([compact('token')]), Response::HTTP_OK);
    }

    private function loginUser(SsoCallbackLoginRequest $request): void
    {
        $code = $request->get('code');

        try {
            $user = $this->loginService->getUserFromCode($code);
            Auth::login($user);
        } catch (ServerErrorException $e) {
            $this->logger->error('An error occurred when trying to retrieve WorkOs profile', [
                'code' => $code,
                'message' => $e->getMessage(),
            ]);

            throw new NotAcceptableHttpException('email.sso_technical_error');
        } catch (InvalidCodeException $e) {
            throw new NotAcceptableHttpException('password.not_acceptable');
        }
    }
}
