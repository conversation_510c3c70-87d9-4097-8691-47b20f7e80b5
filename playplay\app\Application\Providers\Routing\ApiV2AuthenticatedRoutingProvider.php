<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing;

use App\Application\Providers\Routing\Api\Billing\BillingRouteMapper;
use App\Application\Providers\Routing\Api\Billing\SubscriptionRouteMapper;
use App\Application\Providers\Routing\Api\CompanyRouteMapper;
use App\Application\Providers\Routing\Api\DownloadProjectRouteMapper;
use App\Application\Providers\Routing\Api\GettyAuthRouteMapper;
use App\Application\Providers\Routing\Api\IntercomRouteMapper;
use App\Application\Providers\Routing\Api\LibraryItemRouteMapper;
use App\Application\Providers\Routing\Api\Media\FavoriteRawMediaRouteMapper;
use App\Application\Providers\Routing\Api\Project\ProjectAuthRouteMapper;
use App\Application\Providers\Routing\Api\Project\ProjectFolderRouteMapper;
use App\Application\Providers\Routing\Api\ProjectScreen\ProjectScreensReorderRouteMapper;
use App\Application\Providers\Routing\Api\Team\TeamFolderRouteMapper;
use App\Application\Providers\Routing\Api\TemplateRouteMapper;
use App\Application\Providers\Routing\Api\User\UserRouteMapper;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider;
use Illuminate\Support\Facades\Route;

final class ApiV2AuthenticatedRoutingProvider extends RouteServiceProvider
{
    /** @var RouteMapper[] */
    private array $routeMappers;

    public function __construct(Application $app)
    {
        parent::__construct($app);
        $this->routeMappers = [
            new BillingRouteMapper(),
            new CompanyRouteMapper(),
            new DownloadProjectRouteMapper(),
            new FavoriteRawMediaRouteMapper(),
            new GettyAuthRouteMapper(),
            new IntercomRouteMapper(),
            new LibraryItemRouteMapper(),
            new ProjectAuthRouteMapper(),
            new ProjectFolderRouteMapper(),
            new ProjectScreensReorderRouteMapper(),
            new SubscriptionRouteMapper(),
            new TeamFolderRouteMapper(),
            new TemplateRouteMapper(),
            new UserRouteMapper(),
        ];
    }

    public function map(): void
    {
        Route::group([
            'middleware' => ['api', 'auth'],
            'prefix' => 'api/v2',
            'as' => 'api.v2.',
        ], function () {
            foreach ($this->routeMappers as $routeMapper) {
                $routeMapper->map();
            }
        });
    }
}
