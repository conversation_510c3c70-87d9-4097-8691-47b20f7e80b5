<?php

namespace App\Application\Http\Requests\Api\RenderProject;

use App\Models\Project;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Factory;

class DownloadRenderProjectHtmlRequest extends FormRequest
{
    public function __construct(Factory $validationFactory)
    {
        $this->addNthInRenderStory($validationFactory);
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'type' => ['in:direct,email'],
            'nth' => ['sometimes', 'integer', 'min:0', 'nth_in_render_story'],
        ];
    }

    private function addNthInRenderStory(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'nth_in_render_story',
            function ($attribute, $value) {
                /** @var Project $project */
                $project = request()->route('project');
                $renderStory = $project->lastRenderProjectHtmlProcessed->renderStories->first();

                return isset($renderStory->ordered_chunk_urls[$value]);
            }
        );
    }
}
