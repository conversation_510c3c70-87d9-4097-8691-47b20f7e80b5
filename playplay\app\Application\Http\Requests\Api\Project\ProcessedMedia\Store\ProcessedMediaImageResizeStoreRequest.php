<?php

namespace App\Application\Http\Requests\Api\Project\ProcessedMedia\Store;

use App\Domain\RawMedia\RawMediaSource;
use App\Models\ProcessedMedia;
use App\Models\ScreenParams\BaseParam;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Factory;

class ProcessedMediaImageResizeStoreRequest extends FormRequest
{
    public static function getRules()
    {
        return [
            'param_id' => [
                'sometimes',
                'required',
                'numeric',
                'exists:layout_params,id',
                'paramIsInProject',
                'paramIsTypeLogo',
            ],
            'raw_media_id' => ['required', 'exists:raw_medias,id', 'isImage'],
            'project_id' => ['nullable', 'exists:projects,id'],
            'source' => [
                'required',
                'in:' . implode(',', [
                    RawMediaSource::UPLOAD,
                    ProcessedMedia::SOURCE_FAVORITES,
                    ProcessedMedia::SOURCE_RECENTLY_USED,
                    ProcessedMedia::SOURCE_LIBRARY,
                ]),
            ],
        ];
    }

    public function __construct()
    {
        $validationFactory = app(Factory::class);
        $this->checkParamIsTypeLogo($validationFactory);
        parent::__construct();
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function checkParamIsTypeLogo(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'paramIsTypeLogo',
            function ($attribute, $value, $parameters) {
                return !$value || optional(BaseParam::find($value))->type === BaseParam::TYPE_LOGO;
            }
        );
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return static::getRules();
    }
}
