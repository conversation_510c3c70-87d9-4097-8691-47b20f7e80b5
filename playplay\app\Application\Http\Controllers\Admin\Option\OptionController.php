<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Option;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\Option\CreateOptionRequest;
use App\Application\Http\Requests\Admin\Option\StoreOptionRequest;
use App\Application\Http\Requests\Admin\Option\UpdateOptionRequest;
use App\Domain\Screen\Parameters\OptionListRepository;
use App\Domain\Screen\Parameters\OptionRepository;
use App\Domain\TeamScreen\CacheTeamScreenService;
use App\Models\Screen\Parameters\Option;
use App\Models\Screen\Parameters\OptionList;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use JsonException;

final class OptionController extends BaseController
{
    private OptionListRepository $optionListRepository;
    private CacheTeamScreenService $cacheTeamScreenService;
    private OptionRepository $optionRepository;

    public function __construct(
        CacheTeamScreenService $cacheTeamScreenService,
        OptionListRepository $optionListRepository,
        OptionRepository $optionRepository
    ) {
        $this->authorizeResource(Option::class);
        $this->cacheTeamScreenService = $cacheTeamScreenService;
        $this->optionListRepository = $optionListRepository;
        $this->optionRepository = $optionRepository;
    }

    public function create(CreateOptionRequest $createOptionRequest): View
    {
        $option = (new Option())->optionList()
            ->associate($createOptionRequest->get('option_list_id'));

        return view('admin.options.options.create', [
            'option' => $option,
        ]);
    }

    public function showDangerZone(Option $option): View
    {
        return view('admin.options.options.danger-zone', [
            'option' => $option,
        ]);
    }

    public function destroy(Option $option): JsonResponse
    {
        $this->optionRepository->delete($option);
        $this->optionListRepository->reorderOptionsOf($option->optionList);
        $this->cacheTeamScreenService->forgetCacheForAllTeamsHavingOptionId($option->id);

        return new JsonResponse([
            'success' => true,
            'redirect' => route('admin.option-lists.edit', $option->optionList->id),
        ]);
    }

    public function edit(Option $option): View
    {
        return view('admin.options.options.edit', ['option' => $option]);
    }

    /** @throws JsonException */
    public function store(StoreOptionRequest $request): RedirectResponse
    {
        /** @var OptionList $optionList */
        $optionList = OptionList::find($request->get('option_list_id'));

        $value = $optionList->value_type === OptionList::FILES_BY_FORMAT_TYPE ? json_encode(
            $request->get('value'),
            JSON_THROW_ON_ERROR
        ) : $request->get('value');

        $option = Option::create([
            'label' => $request->get('label'),
            'value' => $value,
            'thumbnail' => $request->get('thumbnail'),
            'option_list_id' => $optionList->id,
            'order' => $optionList->options->count()
        ]);

        return redirect()->route('admin.options.edit', $option);
    }

    public function update(Option $option, UpdateOptionRequest $request): RedirectResponse
    {
        $dataToUpdate = [
            'label' => $request->input('label'),
        ];

        if ($request->filled('value')) {
            $dataToUpdate['value'] = $this->getValueToUpdate($option, $request->input('value'));
        }

        if ($request->filled('thumbnail')) {
            $dataToUpdate['thumbnail'] = $request->input('thumbnail');
        }

        $option->update($dataToUpdate);

        $this->cacheTeamScreenService->forgetCacheForAllTeamsHavingOptionId($option->id);

        return redirect()->route('admin.options.edit', $option);
    }

    private function getExpectedValue(array $givenValue, array $actualValue): array
    {
        if (!isset($givenValue['horizontal']) || $givenValue['horizontal'] === '') {
            $givenValue['horizontal'] = $actualValue['horizontal'];
        }

        if (!isset($givenValue['vertical']) || $givenValue['vertical'] === '') {
            $givenValue['vertical'] = $actualValue['vertical'];
        }

        if (!isset($givenValue['square']) || $givenValue['square'] === '') {
            $givenValue['square'] = $actualValue['square'];
        }

        return $givenValue;
    }

    private function getValueToUpdate(Option $option, string|array $givenValue): string
    {
        if ($option->optionList->value_type !== OptionList::FILES_BY_FORMAT_TYPE) {
            return $givenValue;
        }

        $expectedValue = $this->getExpectedValue(
            $givenValue,
            json_decode($option->value, true, 512, JSON_THROW_ON_ERROR)
        );

        return json_encode($expectedValue, JSON_THROW_ON_ERROR);
    }
}
