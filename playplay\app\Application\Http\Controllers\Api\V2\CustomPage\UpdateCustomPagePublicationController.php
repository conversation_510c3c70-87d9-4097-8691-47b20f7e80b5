<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\CustomPage;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\CustomPage\UpdateCustomPagePublicationRequest;
use App\Domain\CustomPage\CustomPagePublicationUpdater;
use App\Infrastructure\CustomPage\Serializers\CustomPagePublicationSerializer;
use App\Models\CustomPage;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

final class UpdateCustomPagePublicationController extends BaseController
{
    use AuthorizesRequests;

    private CustomPagePublicationSerializer $settingsSerializer;

    private CustomPagePublicationUpdater $publicationUpdater;

    public function __construct(
        CustomPagePublicationSerializer $settingsSerializer,
        CustomPagePublicationUpdater $publicationUpdater,
    ) {
        $this->settingsSerializer = $settingsSerializer;
        $this->publicationUpdater = $publicationUpdater;
    }

    public function __invoke(
        CustomPage $customPage,
        UpdateCustomPagePublicationRequest $request
    ): JsonResponse {
        $this->authorize('update', [CustomPage::class, $customPage]);
        if ($customPage->project->trashed() === true) {
            throw new NotFoundHttpException('Project not found for this custom page.');
        }

        if ($request->boolean('published') === true) {
            $this->publicationUpdater->publish($customPage);
        } else {
            $this->publicationUpdater->unpublish($customPage);
        }

        return $this->sendJsonResponse(
            new Collection([$this->settingsSerializer->serialize($customPage)]),
            Response::HTTP_OK
        );
    }
}
