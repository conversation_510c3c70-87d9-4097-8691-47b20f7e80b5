<?php

namespace App\Application\Policies;

use App\Models\Folder;
use App\Models\Team;
use App\Models\User;

class FolderPolicy
{
    public function create(User $user, Team $team): bool
    {
        return $user->teams()->where('team_id', $team->id)->exists();
    }

    public function destroy(User $user, Team $team, Folder $folder): bool
    {
        return $this->update($user, $team, $folder);
    }

    public function index(User $user, Team $team): bool
    {
        return $user->can('view', $team);
    }

    public function move(User $user, Team $team, ?int $folderId): bool
    {
        if (!$user->can('view', $team)) {
            return false;
        }

        $folder = Folder::find($folderId);

        return $folder === null || $folder->team_id = $team->id;
    }

    public function update(User $user, Team $team, Folder $folder): bool
    {
        if (!$user->can('view', $team)) {
            return false;
        }

        return $folder->team_id === $team->id;
    }
}
