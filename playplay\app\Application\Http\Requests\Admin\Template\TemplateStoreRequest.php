<?php

namespace App\Application\Http\Requests\Admin\Template;

use App\Models\Project;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Factory;

final class TemplateStoreRequest extends FormRequest
{
    public function __construct(Factory $validationFactory)
    {
        parent::__construct();
        // Check if the project is in a format that exists
        $this->checkIsInExistingFormat($validationFactory);
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    public function messages(): array
    {
        return [
            // Name validator messages
            'name.required' => 'Name is mandatory',
            'name.max' => 'Maximun length is 120 characters',
            'backoffice_name.required' => 'Backoffice name is mandatory',
            'backoffice_name.max' => 'Maximun length is 120 characters',
            'image_path.image' => 'Must be an Image',
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'max:120'],
            'backoffice_name' => ['required', 'max:120'],
            'has_only_assigned_screens' => ['required', 'boolean'],
            'covers.image.*' => ['url'],
            'covers.video.*' => ['url'],
            'projects' => ['array', 'areInExistingFormat'],
            'projects.*' => ['sometimes', 'exists:projects,id'],
        ];
    }

    private function checkIsInExistingFormat(Factory $validationFactory): void
    {
        $validationFactory->extendImplicit(
            'areInExistingFormat',
            function ($attribute, $value) {
                if (!$value) {
                    return true;
                }

                $projects = Project::whereIn('id', array_values($value))->pluck('id', 'format')->toArray();
                $value = array_filter($value, function ($item) {
                    return $item !== null && $item !== [] && $item !== '';
                });

                return $projects == $value;
            }
        );
    }
}
