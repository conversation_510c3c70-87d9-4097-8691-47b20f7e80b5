<?php

namespace App\Application\Rules;

use Illuminate\Contracts\Validation\Rule;

class CheckAudioLevelsRule implements Rule
{
    private const AUDIO_LEVELS = ['media_level', 'music_level', 'voiceover_level'];
    private const MAX_AUDIO_LEVEL = 100;
    private const MIN_AUDIO_LEVEL = 0;

    public function passes($attribute, $value): bool
    {
        return $value === null || (self::audioLevelsAreSetAndInRange($value));
    }

    public function message(): string
    {
        return 'CheckAudioLevelsRule is invalid';
    }

    private static function audioLevelsAreSetAndInRange(array $value): bool
    {
        foreach (self::AUDIO_LEVELS as $audioLevelName) {
            if (!array_key_exists($audioLevelName, $value)
                || ($value[$audioLevelName] > self::MAX_AUDIO_LEVEL
                    || $value[$audioLevelName] < self::MIN_AUDIO_LEVEL)
            ) {
                return false;
            }
        }

        return true;
    }
}
