<?php

declare(strict_types=1);

namespace App\Domain\Outro;

use App\Domain\Render\RenderMedia\Crop\CropFactory;
use App\Models\ProcessedMedia;
use App\Models\RawMedia;
use App\Services\ProcessedMedia\Factory as ProcessedMediaFactory;
use App\Services\Processing\RenderMediaService;
use Illuminate\Config\Repository as ConfigRepository;

class OutroRenderMediaFactory
{
    private RenderMediaService $renderService;
    private ProcessedMediaFactory $processedMediaFactory;
    private ConfigRepository $configRepository;

    public function __construct(
        RenderMediaService $renderService,
        ProcessedMediaFactory $processedMediaFactory,
        ConfigRepository $configRepository
    ) {
        $this->renderService = $renderService;
        $this->processedMediaFactory = $processedMediaFactory;
        $this->configRepository = $configRepository;
    }

    public function createProcessedMediasAndRenders(RawMedia $rawMedia, string $format): ProcessedMedia
    {
        $processedMedia = $this->processedMediaFactory->create($rawMedia->id, $rawMedia->source?->value, null);
        [$width, $height] = array_values($this->configRepository->get("app.playplay.formats.{$format}"));
        $this->renderService->outro($processedMedia, [
            'crop' => CropFactory::createAnchoredCropFromArray([
                'width' => $width,
                'height' => $height,
            ])->toArray(),
        ]);

        return $processedMedia;
    }
}
