<?php

namespace App\Application\Console\Commands\Users;

use App\Domain\User\Onboarding\NotificationService;
use App\Domain\User\UserRepository;
use Illuminate\Console\Command;

final class SendOnboardingEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:send-onboarding-email';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send notification to users that gained access to the app as of today';

    private NotificationService $userOnboardingNotificationService;
    private UserRepository $userRepository;

    public function __construct(
        NotificationService $userOnboardingNotificationService,
        UserRepository $userRepository
    ) {
        parent::__construct();
        $this->userRepository = $userRepository;
        $this->userOnboardingNotificationService = $userOnboardingNotificationService;
    }

    public function handle(): int
    {
        $users = $this->userRepository->getOnboardingUsers();

        foreach ($users as $user) {
            $this->userOnboardingNotificationService->sendOnboardingNotification($user);
        }

        return 0;
    }
}
