<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\TransferMedia;

use App\Application\Events\MediaUploadedForProjectFromTransferMedia;
use App\Application\Http\Requests\Api\TransferMedia\GetCdnUrlOfMediaToBeTransferredRequest;
use App\Domain\Workflow\Config\MediaWorkflow;
use App\Domain\Project\ProjectRepository;
use App\Domain\Project\RawMedia\RelationType;
use App\Domain\RawMedia\RawMediaRepository;
use App\Domain\TransferMedia\MediaToBeTransferred;
use App\Domain\TransferMedia\TransferMediaService;
use App\Services\Processing\RenderMediaService;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

final class GetCdnUrlOrRawMediaOfMediaToBeTransferredController extends Controller
{
    private TransferMediaService $transferMediaService;
    private RenderMediaService $renderService;
    private ProjectRepository $projectRepository;
    private RawMediaRepository $mediaRepository;
    private Dispatcher $eventDispatcher;
    private MediaWorkflow $mediaWorkflow;

    public function __construct(
        TransferMediaService $transferMediaService,
        RenderMediaService $renderService,
        ProjectRepository $projectRepository,
        RawMediaRepository $mediaRepository,
        Dispatcher $eventDispatcher,
        MediaWorkflow $mediaWorkflow
    ) {
        $this->transferMediaService = $transferMediaService;
        $this->renderService = $renderService;
        $this->projectRepository = $projectRepository;
        $this->mediaRepository = $mediaRepository;
        $this->eventDispatcher = $eventDispatcher;
        $this->mediaWorkflow = $mediaWorkflow;
    }

    /**
     * @throws ValidationException
     */
    public function __invoke(GetCdnUrlOfMediaToBeTransferredRequest $request): JsonResponse
    {
        $mediaToBeUploaded = MediaToBeTransferred::from($request);
        $rawMedia = $this->mediaRepository->getOneByMd5($mediaToBeUploaded->hash);

        if ($rawMedia === null) {
            return new JsonResponse(
                [
                    'raw_media' => null,
                    'upload_url' => $this->transferMediaService->getCdnUploadUrl($mediaToBeUploaded),
                ],
                Response::HTTP_OK
            );
        }

        // start of TODO Move this logic to domain service
        if (!$rawMedia->hasValidLastRender()) {
            $this->renderService->createRenderMediaFor($rawMedia);
        }

        $this->mediaWorkflow->start($rawMedia);

        $project = $this->projectRepository->findOneByUuid($mediaToBeUploaded->projectUuid);
        $relationType = RelationType::LIBRARY;

        if (!$this->mediaRepository->isRawMediaAlreadyLinkedToAProject($rawMedia, $project, $relationType)) {
            $this->eventDispatcher->dispatch(
                new MediaUploadedForProjectFromTransferMedia($rawMedia->refresh(), $project->id)
            );
        }

        $this->mediaRepository->linkRawMediaToProject($rawMedia, $project, $relationType);
        $this->mediaRepository->linkRawMediaToUser($rawMedia, $project->user);
        // end of TODO

        return new JsonResponse(
            [
                'raw_media' => $rawMedia->refresh()->toVue(),
                'upload_url' => null,
            ],
            Response::HTTP_ALREADY_REPORTED
        );
    }
}
