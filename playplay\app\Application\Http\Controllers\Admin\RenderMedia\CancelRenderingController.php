<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\RenderMedia;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Domain\Render\RenderMedia\RenderMediaService;
use App\Models\Renders\RenderMedia;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Redirector;

final class CancelRenderingController extends BaseController
{
    private Redirector $redirector;
    private RenderMediaService $renderMediaService;

    public function __construct(Redirector $redirector, RenderMediaService $renderMediaService)
    {
        $this->redirector = $redirector;
        $this->renderMediaService = $renderMediaService;
    }

    public function __invoke(RenderMedia $renderMedia): RedirectResponse
    {
        if ($renderMedia->isParentProcessedMedia()) {
            $this->authorize('canAccessRestrictedData', $renderMedia->parent->project->company);
        }

        $this->renderMediaService->cancel($renderMedia);

        return $this->redirector->route('admin.renderMedias.show', $renderMedia->id);
    }
}
