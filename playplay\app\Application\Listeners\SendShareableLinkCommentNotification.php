<?php

declare(strict_types=1);

namespace App\Application\Listeners;

use App\Application\Events\ShareableLinkCommentCreated;
use App\Domain\Notification\NotificationContent;
use App\Domain\Notification\NotificationService;
use App\Domain\Notification\NotificationToBeSent;
use App\Models\Notification;

final class SendShareableLinkCommentNotification
{
    private NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    public function handle(ShareableLinkCommentCreated $event): void
    {
        $comment = $event->getComment();
        $shareableLink = $comment->shareableLink;

        if ($comment->user_id !== $shareableLink->creator_id) {
            $this->notificationService->createAndSend(
                new NotificationToBeSent(
                    Notification::SHARELINK_COMMENT_CREATED,
                    NotificationContent::createForSharelinkCommentCreated($shareableLink->title),
                    $comment->username_to_vue,
                    $shareableLink->url,
                    $shareableLink->creator_id,
                )
            );
        }
    }
}
