<?php

declare(strict_types=1);

namespace App\Application\Exceptions;

use Illuminate\Foundation\Http\FormRequest;
use InvalidArgumentException;

final class InvalidFormRequestMappingException extends InvalidArgumentException
{
    public function __construct(
        FormRequest $formRequest,
        string $valueObjectClassName,
    ) {
        parent::__construct("$valueObjectClassName failed to map from " . get_class($formRequest));
    }
}
