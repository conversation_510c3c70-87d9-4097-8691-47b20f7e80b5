<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Admin;

use App\Application\Http\Controllers\Admin\Company\CompanyAuthPolicyController;
use App\Application\Http\Controllers\Admin\Company\CompanyCreateController;
use App\Application\Http\Controllers\Admin\Company\CompanyDeletionController;
use App\Application\Http\Controllers\Admin\Company\CompanyListingController;
use App\Application\Http\Controllers\Admin\Company\CompanyTeamController;
use App\Application\Http\Controllers\Admin\Company\CompanyUpdateController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class CompanyRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/companies',
            'as' => 'companies.',
        ], function (Router $router) {
            $this->mapListing($router);
            $this->mapCreate($router);
            $this->mapTeam($router);
            $this->mapUpdate($router);
            $this->mapDelete($router);
            $this->mapAuthPolicy($router);
        });
    }

    private function mapAuthPolicy(Router $router): void
    {
        $router->put('/{company}/enable-sso', [CompanyAuthPolicyController::class, 'enableSso'])
            ->name('enable-sso');
        $router->put('/{company}/enable-2fa', [CompanyAuthPolicyController::class, 'enable2FA'])
            ->name('enable-2fa');
        $router->put('/{company}/password-rule', [CompanyAuthPolicyController::class, 'updatePasswordRule'])
            ->name('update-password-rule');
    }

    private function mapCreate(Router $router): void
    {
        $router->get('/create', [CompanyCreateController::class, 'create'])->name('create');
        $router->post('/', [CompanyCreateController::class, 'store'])->name('store');
    }

    private function mapDelete(Router $router): void
    {
        $router->get('/{company}/danger-zone', [CompanyDeletionController::class, 'showDangerZone'])
            ->name('danger-zone');
        $router->delete('/{company}', [CompanyDeletionController::class, 'destroy'])->name('destroy');
    }

    private function mapListing(Router $router): void
    {
        $router->get('/', [CompanyListingController::class, 'index'])->name('index');
        $router->get('/companies-filters', [CompanyListingController::class, 'filters'])->name('filters');
    }

    private function mapTeam(Router $router): void
    {
        $router->put('/{company}/teams-order', [CompanyTeamController::class, 'updateOrder'])
            ->name('update-teams-order');
    }

    private function mapUpdate(Router $router): void
    {
        $router->get('/{company}', [CompanyUpdateController::class, 'show'])->name('show');
        $router->put('/{company}', [CompanyUpdateController::class, 'update'])->name('update');
        $router->put('/{company}/unlimited-users', [CompanyUpdateController::class, 'updateUnlimitedUsers'])
            ->name('updateUnlimitedUsers');
    }
}
