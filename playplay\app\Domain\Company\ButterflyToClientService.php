<?php

declare(strict_types=1);

namespace App\Domain\Company;

use App\Domain\Billing\BillingException;
use App\Domain\Billing\InvoiceDTO;
use App\Domain\Billing\InvoiceRepository;
use App\Domain\Company\Repositories\CompanyRepository;
use App\Models\Company;

class ButterflyToClientService
{
    private CompanyRepository $companyRepository;

    private CompanyHistoryService $companyHistoryService;

    private InvoiceRepository $invoiceRepository;

    public function __construct(
        CompanyRepository $companyRepository,
        CompanyHistoryService $companyHistoryService,
        InvoiceRepository $invoiceRepository
    ) {
        $this->companyRepository = $companyRepository;
        $this->companyHistoryService = $companyHistoryService;
        $this->invoiceRepository = $invoiceRepository;
    }

    /**
     * @throws BillingException
     */
    public function migrateButterflyCompaniesThatShouldBeOfTypeClient(): ?UpdatedButterflyCompaniesDTO
    {
        $butterflyCompanies = $this->companyRepository
            ->getActiveButterflyCompaniesBeingClientForThePast3Months();
        $payingButterflyCompanies = $butterflyCompanies->filter(
            fn(Company $company) => $this->hasCompanyPaidForTheLastThreeMonths($company)
        );

        if ($payingButterflyCompanies->count() === 0) {
            return null;
        }

        $companiesIds = $payingButterflyCompanies->pluck('id')->toArray();
        $this->companyRepository->updateTypeAsClient($companiesIds);
        $updatedCompanies = $this->companyRepository->getManyByIds($companiesIds);
        $this->companyHistoryService->bulkUpsert($updatedCompanies);

        return new UpdatedButterflyCompaniesDTO(
            $payingButterflyCompanies->all(),
            $butterflyCompanies->diff($payingButterflyCompanies)->values()->all()
        );
    }

    private function hasCompanyPaidForTheLastThreeMonths(Company $company): bool
    {
        // Note: the company not having a customer id doesn't mean that they don't pay,
        // but rather that they could be paying by invoice or direct bank transfer,
        // either way, it is very uncommon to see butterfly companies pay like that,
        // so we can just say that they don't pay, and a CS will check manually later.
        if ($company->stripe_customer_id === null) {
            return false;
        }

        $invoices = $this->invoiceRepository->getLastThreeForCustomer($company->stripe_customer_id);
        if ($invoices->count() < 3) {
            return false;
        }

        return $invoices->every(fn(InvoiceDTO $invoice) => $invoice->status === 'paid');
    }
}
