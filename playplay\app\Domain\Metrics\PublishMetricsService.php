<?php

declare(strict_types=1);

namespace App\Domain\Metrics;

use App\Domain\Queueing\QueueMetadataService;

class PublishMetricsService
{
    private Calculator $calculator;
    private MetricsRepository $metricsRepository;
    private MetricsPublisher $publisher;
    private iterable $queuesMetadataServices;

    public function __construct(
        Calculator $calculator,
        MetricsRepository $metricsRepository,
        MetricsPublisher $publisher,
        iterable $queuesMetadataServices
    ) {
        $this->calculator = $calculator;
        $this->metricsRepository = $metricsRepository;
        $this->publisher = $publisher;
        $this->queuesMetadataServices = $queuesMetadataServices;
    }

    public function publish(): void
    {
        $activeUsersCount = $this->metricsRepository->getActiveUser();
        $totalDurationRenderScreen = $this->metricsRepository->getTotalDurationRenderScreen();

        // Publish all metrics
        $this->publisher->publishMetric(Metric::activeUsers($activeUsersCount));
        $this->publisher->publishMetric(Metric::totalDurationInRender($totalDurationRenderScreen));

        $this->publishRenderingClusterMetrics($totalDurationRenderScreen, $activeUsersCount);
        $this->publishQueueMessagesMetrics();
    }

    private function publishRenderingClusterMetrics(int $totalDurationRenderScreen, int $activeUsersCount): void
    {
        $clusterSize = $this->calculator->calculateProcessingHtmlClusterSize(
            $activeUsersCount,
            $totalDurationRenderScreen
        );

        $this->publisher->publishMediaProcessingMetric(Metric::clusterSize($clusterSize));
        $this->publisher->publishVideoRenderingMetric(Metric::clusterSize($clusterSize));
    }

    private function publishQueueMessagesMetrics(): void
    {
        /** @var QueueMetadataService $queueMetadataService */
        foreach ($this->queuesMetadataServices as $queueMetadataService) {
            $this->publisher->publishMetric(
                Metric::createFrom(
                    $queueMetadataService->getQueueName(),
                    $queueMetadataService->getApproximateNumberOfMessages()
                )
            );
        }
    }
}
