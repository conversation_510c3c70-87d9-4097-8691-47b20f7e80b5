<?php

namespace App\Application\Rules;

use App\Models\Project;
use App\Models\ScreenParams\BaseParam;
use Illuminate\Contracts\Validation\Rule;

class ParamIsInProjectRule implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('validation.param_is_in_project');
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed  $value
     *
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $project = request()->route('project')
            ? request()->route('project')
            : Project::find(
                request()->get('project_id')
            );
        if (!$project) {
            return true;
        }

        $projectScreenIds = $project->projectScreens->pluck('screen_id')->toArray();

        return BaseParam::where('id', $value)
            ->with([
                'screenLayout' => function ($q) use ($projectScreenIds) {
                    $q->whereIn('screen_id', $projectScreenIds);
                },
            ])
            ->exists();
    }
}
