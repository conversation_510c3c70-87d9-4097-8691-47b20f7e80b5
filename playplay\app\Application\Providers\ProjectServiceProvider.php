<?php

namespace App\Application\Providers;

use App\Domain\Project\ProjectFilterRepository;
use App\Domain\Project\ProjectRepository;
use App\Domain\Project\Sanitizer\ProjectMusicsSanitizer;
use App\Domain\Project\Sanitizer\ProjectSanitizer;
use App\Domain\Project\Sanitizer\ProjectScreensSanitizer;
use App\Domain\Project\Sanitizer\ProjectVoiceoverSanitizer;
use App\Infrastructure\Project\EloquentProjectFilterRepository;
use App\Infrastructure\Project\EloquentProjectRepository;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Support\ServiceProvider;

final class ProjectServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(ProjectFilterRepository::class, EloquentProjectFilterRepository::class);
        $this->app->bind(ProjectRepository::class, EloquentProjectRepository::class);
        $this->app->bind(ProjectSanitizer::class, function (Application $app) {
            return new ProjectSanitizer(
                [
                    $app->get(ProjectScreensSanitizer::class),
                    $app->get(ProjectMusicsSanitizer::class),
                    $app->get(ProjectVoiceoverSanitizer::class),
                ],
                $app->get(Dispatcher::class)
            );
        });
    }
}
