<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Application\Observers\ModelHistoryObserver;
use App\Application\Observers\ProjectObserver;
use App\Application\Observers\RenderMediaObserver;
use App\Application\Observers\RenderProjectHtmlObserver;
use App\Application\Observers\CustomPageObserver;
use App\Models\BillingPlan;
use App\Models\Company;
use App\Models\Config;
use App\Models\Plan;
use App\Models\ProcessedMedia;
use App\Models\Project;
use App\Models\ProjectScreen;
use App\Models\RawMedia;
use App\Models\Renders\RenderMedia;
use App\Models\Renders\RenderProjectHtml;
use App\Models\Subscription;
use App\Models\Team;
use App\Models\TeamPreset;
use App\Models\User;
use App\Models\CustomPage;
use Barryvdh\Debugbar\ServiceProvider as LaravelDebugBarServiceProvider;
use Illuminate\Contracts\Config\Repository as ConfigRepository;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use Illuminate\View\Factory as ViewFactory;

final class AppServiceProvider extends ServiceProvider
{
    private ConfigRepository $configRepository;

    public function __construct(Application $app)
    {
        parent::__construct($app);
        $this->configRepository = $app->get(ConfigRepository::class);
    }

    /**
     * Bootstrap any application services.
     *
     * @param ViewFactory $view
     */
    public function boot(ViewFactory $view): void
    {
        $this->addViewCreators($view);

        $this->addObservers();

        if ($this->configRepository->get('app.env') !== 'local') {
            URL::forceScheme('https');
        }

        /**
         * @deprecated
         * @see  ProcessedMedia::DEPRECATED_CLASS
         * @todo Remove this part when ProcessedMedia & rawMedia is fully migrate
         *      That part keep the value for parent_type ProjectMedia & RawMedia in database
         */
        Relation::morphMap([
            'App\Models\ProjectMedia' => ProcessedMedia::class,
            'App\Models\RawMediaV2' => RawMedia::class,
        ]);
    }

    public function register()
    {
        if ($this->app->environment() === 'local') {
            $this->app->register(LaravelDebugBarServiceProvider::class);
            $this->app->register(TelescopeServiceProvider::class);
        }
    }

    private function addObservers(): void
    {
        RenderMedia::observe(RenderMediaObserver::class);
        RenderProjectHtml::observe(RenderProjectHtmlObserver::class);

        // Model History Logs
        Company::observe(ModelHistoryObserver::class);
        BillingPlan::observe(ModelHistoryObserver::class);
        Config::observe(ModelHistoryObserver::class);
        Plan::observe(ModelHistoryObserver::class);
        Project::observe([ModelHistoryObserver::class, ProjectObserver::class]);
        CustomPage::observe([CustomPageObserver::class]);
        ProjectScreen::observe(ModelHistoryObserver::class);
        Subscription::observe(ModelHistoryObserver::class);
        Team::observe(ModelHistoryObserver::class);
        TeamPreset::observe(ModelHistoryObserver::class);
        User::observe(ModelHistoryObserver::class);
    }

    private function addViewCreators(ViewFactory $view)
    {
        $view->creator([
            'admin::layouts.partials.navigation.mainheader',
            'admin::layouts.partials.navigation.sidebar',
        ], function (View $view) {
            return $view->with('environmentUrls', $this->getEnvironmentUrls());
        });

        $view->creator([
            'admin::layouts.partials.html.head',
            'admin::layouts.partials.navigation.mainheader',
        ], function (View $view) {
            return $view->with('currentEnvironment', $this->getCurrentEnvironment());
        });
    }

    /**
     * @return string[]
     */
    private function getAvailableEnvironments(): array
    {
        return $this->configRepository->get('app.playplay.envs');
    }

    private function getCurrentEnvironment(): string
    {
        $url = $this->configRepository->get('app.url');

        return $this->getAvailableEnvironments()[$url] ?? 'local';
    }

    private function getEnvironmentUrls(): array
    {
        $currentEnvironment = $this->getCurrentEnvironment();
        $path = request()->path();

        $urls = [];
        foreach (array_flip($this->getAvailableEnvironments()) as $environment => $rootUrl) {
            if ($environment !== $currentEnvironment) {
                $urls[$environment] = trim($rootUrl, '/') . '/' . $path;
            }
        }

        return $urls;
    }
}
