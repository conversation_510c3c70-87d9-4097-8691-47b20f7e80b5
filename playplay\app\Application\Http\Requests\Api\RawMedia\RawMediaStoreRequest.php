<?php

namespace App\Application\Http\Requests\Api\RawMedia;

use App\Domain\Project\RawMedia\RelationType;
use App\Domain\RawMedia\RawMediaSource;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RawMediaStoreRequest extends FormRequest
{
    public static function getRules(): array
    {
        return [
            'name' => ['required'],
            'content_type' => ['required'],
            'hash' => ['required'],
            'relation_type_key' => [
                'nullable',
                Rule::in(RelationType::getAllKeys()),
            ],
            'source' => ['required', Rule::in(RawMediaSource::UPLOAD)],
            'file_url' => ['required'],
            'project_id' => ['sometimes', 'required', 'exists:projects,id'],
        ];
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return static::getRules();
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'relation_type_key' => $this->route('relationTypeKey'),
        ]);
    }
}
