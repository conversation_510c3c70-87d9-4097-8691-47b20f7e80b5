<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\Company\GetCompanyController;
use App\Application\Http\Controllers\Api\Company\GetCompanyMetricsController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class CompanyRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => 'companies/',
            'as' => 'companies.',
        ], static function (Router $router) {
            $router->get('/{company}', GetCompanyController::class)->name('show');
            $router->get('/{company}/metrics', GetCompanyMetricsController::class)->name('metrics');
        });
    }
}
