<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Project;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Project\ProjectStoreFromTemplateRequest;
use App\Domain\Company\Repositories\CompanyRepository;
use App\Domain\Project\ProjectFactory;
use App\Models\Template;
use App\Models\User;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class CreateFromTemplateProjectController extends BaseController
{
    use AuthorizesRequests;

    private Guard $guard;
    private ProjectFactory $projectFactory;
    private CompanyRepository $companyRepository;

    public function __construct(
        Guard $guard,
        ProjectFactory $projectFactory,
        CompanyRepository $companyRepository
    ) {
        $this->guard = $guard;
        $this->projectFactory = $projectFactory;
        $this->companyRepository = $companyRepository;
    }

    public function __invoke(ProjectStoreFromTemplateRequest $request): JsonResponse
    {
        /** @var User $user */
        $user = $this->guard->user();

        /** @var Template $template */
        $template = Template::find($request->get('template_id'));

        $presetId = $request->get('preset_id');
        $company = $this->companyRepository->getCompanyByPresetId($presetId);
        $this->authorize('canAccessRestrictedData', $company);

        $format = $request->get('format');
        $project = $this->projectFactory->createFromTemplate($user, $template, $format, $presetId);

        return $this->sendJsonResponse(new Collection([$project]), Response::HTTP_CREATED);
    }
}
