<?php

namespace App\Application\Providers;

use App\Application\Console\Commands\Database\InactiveUsersAndCompaniesAnonymizer;
use App\Domain\Anonymizer\AnonymizableInterface;
use App\Domain\Anonymizer\CompanyDataAnonymizer;
use App\Domain\Anonymizer\DataAnonymizer;
use App\Infrastructure\Anonymizer\CompanyAnonymizer;
use App\Infrastructure\Anonymizer\DeprecatedRawMediaAnonymizer;
use App\Infrastructure\Anonymizer\EmailInvitationAnonymizer;
use App\Infrastructure\Anonymizer\MediaAnonymizer;
use App\Infrastructure\Anonymizer\PasswordResetAnonymizer;
use App\Infrastructure\Anonymizer\ProjectAnonymizer;
use App\Infrastructure\Anonymizer\Randomizer\DatabaseRandomizer;
use App\Infrastructure\Anonymizer\RawMediaAnonymizer;
use App\Infrastructure\Anonymizer\RenderJob\RenderProjectHtmlBuilder;
use App\Infrastructure\Anonymizer\RenderJob\RenderScreenHtmlBuilder;
use App\Infrastructure\Anonymizer\RenderJob\RenderStoryRenderProjectBuilder;
use App\Infrastructure\Anonymizer\RenderJobAnonymizer;
use App\Infrastructure\Anonymizer\RenderMediaAnonymizer;
use App\Infrastructure\Anonymizer\RenderProjectHtmlAnonymizer;
use App\Infrastructure\Anonymizer\RenderScreenHtmlAnonymizer;
use App\Infrastructure\Anonymizer\RenderStoryAnonymizer;
use App\Infrastructure\Anonymizer\SnapshotAnonymizer;
use App\Infrastructure\Anonymizer\SubscriptionAnonymizer;
use App\Infrastructure\Anonymizer\TeamAnonymizer;
use App\Infrastructure\Anonymizer\User\InactiveUsers;
use App\Infrastructure\Anonymizer\User\UsersOutsidePlayPlay;
use App\Infrastructure\Anonymizer\UserAnonymizer;
use App\Models\Company;
use App\Models\DeprecatedRawMedia;
use App\Models\EmailInvitation;
use App\Models\Media;
use App\Models\PasswordReset;
use App\Models\Project;
use App\Models\RawMedia;
use App\Models\RenderJob;
use App\Models\Renders\RenderMedia;
use App\Models\Renders\RenderProjectHtml;
use App\Models\Renders\RenderScreenHtml;
use App\Models\Renders\RenderStory;
use App\Models\Snapshot;
use App\Models\Subscription;
use App\Models\Team;
use App\Models\User;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Support\ServiceProvider;

class DataAnonymizerServiceProvider extends ServiceProvider
{
    /**
     * @return void
     */
    public function register(): void
    {
        $this->addTagForRenderJobParentBuilder();
        $this->app->bind('CompanyAnonymizer', function () {
            return new CompanyAnonymizer(new DatabaseRandomizer(), new Company());
        });
        $this->app->bind('UsersOutsidePlayPlayAnonymizer', function () {
            return new UserAnonymizer(new UsersOutsidePlayPlay(), new DatabaseRandomizer(), new User());
        });
        $this->app->bind('EmailInvitationAnonymizer', function () {
            return new EmailInvitationAnonymizer(new DatabaseRandomizer(), new EmailInvitation());
        });
        $this->app->bind('PasswordResetAnonymizer', function () {
            return new PasswordResetAnonymizer(new DatabaseRandomizer(), new PasswordReset());
        });
        $this->app->bind('SubscriptionAnonymizer', function () {
            return new SubscriptionAnonymizer(new DatabaseRandomizer(), new Subscription());
        });
        $this->app->bind('DeprecatedRawMediaAnonymizer', function () {
            return new DeprecatedRawMediaAnonymizer(new DatabaseRandomizer(), new DeprecatedRawMedia());
        });
        $this->app->bind('RawMediaAnonymizer', function () {
            return new RawMediaAnonymizer(new DatabaseRandomizer(), new RawMedia());
        });
        $this->app->bind('RenderJobAnonymizer', function (Application $app) {
            return new RenderJobAnonymizer($app->tagged('renderJobsParentBuilders'), new RenderJob());
        });
        $this->app->bind('RenderMediaAnonymizer', function () {
            return new RenderMediaAnonymizer(new DatabaseRandomizer(), new RenderMedia());
        });
        $this->app->bind('MediaAnonymizer', function () {
            return new MediaAnonymizer(new DatabaseRandomizer(), new Media());
        });
        $this->app->bind('RenderProjectHtmlAnonymizer', function () {
            return new RenderProjectHtmlAnonymizer(new DatabaseRandomizer(), new RenderProjectHtml());
        });
        $this->app->bind('RenderScreenHtmlAnonymizer', function () {
            return new RenderScreenHtmlAnonymizer(new RenderScreenHtml());
        });
        $this->app->bind('RenderStoryAnonymizer', function () {
            return new RenderStoryAnonymizer(new DatabaseRandomizer(), new RenderStory());
        });
        $this->app->bind('SnapshotAnonymizer', function () {
            return new SnapshotAnonymizer(new Snapshot());
        });
        $this->app->bind('TeamAnonymizer', function () {
            return new TeamAnonymizer(new DatabaseRandomizer(), new Team());
        });
        $this->app->bind('ProjectAnonymizer', function () {
            return new ProjectAnonymizer(new DatabaseRandomizer(), new Project());
        });
        $this->app->tag(
            [
                'CompanyAnonymizer',
                'UsersOutsidePlayPlayAnonymizer',
                'EmailInvitationAnonymizer',
                'PasswordResetAnonymizer',
                'SubscriptionAnonymizer',
                'DeprecatedRawMediaAnonymizer',
                'RawMediaAnonymizer',
                'RenderJobAnonymizer',
                'RenderMediaAnonymizer',
                'RenderProjectHtmlAnonymizer',
                'RenderScreenHtmlAnonymizer',
                'RenderStoryAnonymizer',
                'SnapshotAnonymizer',
                'MediaAnonymizer',
                'TeamAnonymizer',
                'ProjectAnonymizer',
            ],
            'outsidePlayPlayAnonymisers'
        );

        $this->app->bind(DataAnonymizer::class, function (Application $app) {
            return new DataAnonymizer($app->tagged('outsidePlayPlayAnonymisers'));
        });

        $this->app->when(InactiveUsersAndCompaniesAnonymizer::class)
            ->needs(AnonymizableInterface::class)
            ->give(function () {
                return new UserAnonymizer(new InactiveUsers(), new DatabaseRandomizer(), new User());
            });

        $this->app->bind(CompanyDataAnonymizer::class, function (Application $app) {
            return new CompanyDataAnonymizer($app->tagged('outsidePlayPlayAnonymisers'));
        });
    }

    private function addTagForRenderJobParentBuilder(): void
    {
        $this->app->bind('RenderProjectHtmlBuilder', function () {
            return new RenderProjectHtmlBuilder();
        });
        $this->app->bind('RenderScreenHtmlBuilder', function () {
            return new RenderScreenHtmlBuilder();
        });
        $this->app->bind('RenderStoryRenderProjectBuilder', function () {
            return new RenderStoryRenderProjectBuilder();
        });
        $this->app->tag(
            [
                'RenderProjectHtmlBuilder',
                'RenderScreenHtmlBuilder',
                'RenderStoryRenderProjectBuilder',
            ],
            'renderJobsParentBuilders'
        );
    }
}
