<?php

declare(strict_types=1);

namespace App\Application\Listeners\Renders;

use App\Application\Events\Renders\RenderMediaProcessed;
use App\Domain\Preview\SnapshotEnricher;
use App\Domain\Preview\SnapshotRepository;
use App\Domain\Preview\SnapshotStatus;
use App\Models\Snapshot;

final class UpdateSnapshotOnRenderMediaProcessed
{
    private SnapshotRepository $snapshotRepository;
    private SnapshotEnricher $snapshotEnricher;

    public function __construct(SnapshotRepository $snapshotRepository, SnapshotEnricher $snapshotEnricher)
    {
        $this->snapshotRepository = $snapshotRepository;
        $this->snapshotEnricher = $snapshotEnricher;
    }

    public function handle(RenderMediaProcessed $event): void
    {
        $event->getRenderMedia()->snapshots()->isNotFinished()
            ->each(function (Snapshot $snapshot) {
                if ($this->snapshotRepository->hasAllRenderMediasProcessed($snapshot)) {
                    $this->snapshotRepository->updateStatusAndData(
                        $snapshot,
                        SnapshotStatus::PROCESSED,
                        $this->snapshotEnricher->enrichWithRenderMedias($snapshot)
                    );
                }
            });
    }
}
