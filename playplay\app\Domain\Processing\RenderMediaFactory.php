<?php

declare(strict_types=1);

namespace App\Domain\Processing;

use App\Domain\Render\RenderMedia\LegacyRenderMediaDataFactory;
use App\Domain\Render\RenderMedia\RenderMediaRepository;
use App\Domain\Render\RenderMedia\RenderMediaStatus;
use App\Models\ProcessedMedia;
use App\Models\Renders\RenderMedia;

class RenderMediaFactory
{
    private RenderMediaRepository $renderMediaRepository;

    public function __construct(RenderMediaRepository $renderMediaRepository)
    {
        $this->renderMediaRepository = $renderMediaRepository;
    }

    public function create(ProcessedMedia $processedMedia, array $data): RenderMedia
    {
        return $this->renderMediaRepository->create(
            $processedMedia,
            LegacyRenderMediaDataFactory::createProcessedMediaData($data),
            $this->getRenderMediaStatus($processedMedia)
        );
    }

    /**
     * @TODO should be moved in a RenderMediaManager or directly into RenderMedia model
     */
    private function getRenderMediaStatus(ProcessedMedia $processedMedia): RenderMediaStatus
    {
        $isCanceled = in_array(
            (string) $processedMedia->rawMedia->status,
            [
                RenderMediaStatus::CANCELED,
                RenderMediaStatus::ERRORED,
            ],
            true
        );

        return $isCanceled ? RenderMediaStatus::canceled() : RenderMediaStatus::toProcess();
    }
}
