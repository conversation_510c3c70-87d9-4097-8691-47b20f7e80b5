<?php

declare(strict_types=1);

namespace App\Domain\Preview;

use App\Models\Snapshot;

final class SnapshotService
{
    public function duplicate(Snapshot $snapshot, Previewable $parent): Snapshot
    {
        $duplicatedSnapshot = $snapshot->replicate();
        $duplicatedSnapshot->parent_id = $parent->getId();
        $duplicatedSnapshot->duplicated_snapshot_id = $snapshot->id;
        $duplicatedSnapshot->save();

        return $duplicatedSnapshot;
    }
}
