<?php

declare(strict_types=1);

namespace App\Application\Console\Commands\Company;

use App\Application\Mail\CompaniesMarkedAsClient;
use App\Domain\Company\ButterflyToClientService;
use App\Models\Company;
use Illuminate\Console\Command;
use Illuminate\Contracts\Mail\Mailer;
use Psr\Log\LoggerInterface;

final class ButterflyToClientCommand extends Command
{
    protected $signature = 'companies:butterfly-to-client';

    protected $description = 'Finds all butterfly companies that have been paying for the
    last 3 months and have active users, and updates their type as client';

    private Mailer $mailer;

    private LoggerInterface $logger;

    private ButterflyToClientService $butterflyToClientService;

    public function __construct(
        Mailer $mailer,
        LoggerInterface $logger,
        ButterflyToClientService $butterflyToClientService
    ) {
        parent::__construct();
        $this->mailer = $mailer;
        $this->logger = $logger;
        $this->butterflyToClientService = $butterflyToClientService;
    }

    public function handle(): int
    {
        $butterflyCompanies = $this->butterflyToClientService->migrateButterflyCompaniesThatShouldBeOfTypeClient();

        $totalCompaniesMigrated = $butterflyCompanies === null ? 0 : count($butterflyCompanies->payingCompanies);
        if ($totalCompaniesMigrated === 0) {
            $this->logAndDisplayOnConsole('No companies were updated today!');

            return 0;
        }

        $formattedCompaniesIds = implode(
            ', ',
            array_map(static fn(Company $company) => $company->id, $butterflyCompanies->payingCompanies)
        );
        $this->logAndDisplayOnConsole(
            "Marked {$totalCompaniesMigrated} companies as client. IDs of updated companies: [{$formattedCompaniesIds}]"
        );
        $this->mailer->to('<EMAIL>')->send(new CompaniesMarkedAsClient($butterflyCompanies));

        return 0;
    }

    private function logAndDisplayOnConsole(string $message): void
    {
        $this->info($message);
        $this->logger->info($message);
    }
}
