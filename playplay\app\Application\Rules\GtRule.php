<?php

namespace App\Application\Rules;

use Illuminate\Contracts\Validation\Rule;

class GtRule implements Rule
{
    /**
     * Create a new rule instance.
     */
    public function __construct()
    {
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('validation.gt');
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed  $value
     * @param array  $parameters
     *
     * @return bool
     */
    public function passes($attribute, $value, $parameters = [], $validator = null)
    {
        if (count($parameters) !== 1 && !is_numeric($value)) {
            return false;
        }

        $data = $validator->getData();
        $comparedToValue = isset($data[$parameters[0]]) ? $data[$parameters[0]] : null;

        if (!is_null($comparedToValue) && (is_numeric($value) && is_numeric($comparedToValue))) {
            return $value > $comparedToValue;
        }

        return false;
    }
}
