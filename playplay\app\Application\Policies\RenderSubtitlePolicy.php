<?php

namespace App\Application\Policies;

use App\Models\ProjectScreenParam;
use App\Models\Renders\RenderSubtitle;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class RenderSubtitlePolicy extends DefaultPolicy
{
    use HandlesAuthorization;

    public function create(User $user, ProjectScreenParam $projectScreenParam): bool
    {
        if (!$user->company->hasSpeechToTextFeature()) {
            return false;
        }

        $project = $projectScreenParam->project;

        return $user->teams->pluck('id')->contains($project->team_id);
    }

    public function view(User $user, RenderSubtitle $renderSubtitle): bool
    {
        $projectScreenParam = $renderSubtitle->projectScreenParam;
        if ($projectScreenParam === null) {
            return false;
        }

        $project = $projectScreenParam->project;

        return $user->teams->pluck('id')->contains($project->team_id);
    }
}
