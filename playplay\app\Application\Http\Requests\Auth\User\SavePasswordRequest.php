<?php

namespace App\Application\Http\Requests\Auth\User;

use Illuminate\Foundation\Http\FormRequest;

class SavePasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'token' => 'required',
            'email' => ['required', 'email'],
            'password' => ['required', 'confirmed', 'check_password_rule'],
            'terms' => 'required',
        ];
    }
}
