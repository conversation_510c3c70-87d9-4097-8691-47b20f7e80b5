<?php

namespace App\Application\Http\Controllers\Admin;

use App\Application\Http\Requests\Admin\Project\ProjectUpdateRequest;
use App\Domain\Project\ProjectFilterRepository;
use App\Domain\Screen\ScreenRepository;
use App\Models\Project;
use App\Models\User;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

class ProjectController extends BaseController
{
    private ScreenRepository $screenRepository;

    private ProjectFilterRepository $projectFilterRepository;
    private Factory $viewFactory;

    public function __construct(
        ScreenRepository $screenRepository,
        ProjectFilterRepository $projectFilterRepository,
        Factory $viewFactory,
    ) {
        $this->screenRepository = $screenRepository;
        $this->projectFilterRepository = $projectFilterRepository;
        $this->viewFactory = $viewFactory;
    }

    public function index(Request $request): View|Response
    {
        $this->authorize('list', Project::class);

        $projectsFilterData = $request->all([
            'user_id',
            'csm_id',
            'project_id',
            'company_id',
            'company_type',
            'team_id',
            'status',
            'screen_id',
            'export',
        ]);

        if ($request->boolean('export')) {
            return response()->csv(
                $this->projectFilterRepository->getAllProjectsByFilters($projectsFilterData)
            );
        }

        return $this->viewFactory->make(
            'admin.projects.index',
            array_merge(
                $this->getProjectsTableFilterElementsData(),
                ['data' => $projectsFilterData],
                $this->getProjectsTableData($projectsFilterData, $request->input('per_page', 50))
            )
        );
    }

    public function renders(Project $project): JsonResponse
    {
        $renders = $project->renders()
            ->orderByDesc('id')
            ->get();

        return new JsonResponse(['success' => true, compact('renders')], SymfonyResponse::HTTP_OK);
    }

    public function restore(ProjectUpdateRequest $request, Project $project): JsonResponse|RedirectResponse
    {
        $this->authorize('restore', Project::class);

        $isActive = filter_var($request->get('is_active'), FILTER_VALIDATE_BOOLEAN);
        if ($isActive && $project->deleted_at) {
            $project->restore();
        }

        if ($request->ajax()) {
            return new JsonResponse(
                ['success' => true, 'redirect_uri' => route('admin.projects.index')],
                SymfonyResponse::HTTP_OK
            );
        }

        return redirect('admin.projects.index');
    }

    private function getProjectsTableFilterElementsData(): array
    {
        $screens = $this->screenRepository->getAll()->pluck('backoffice_name', 'id')->toArray();

        return [
            'screens' => $screens,
            'csmUsers' => User::retrieveCsmUsers(),
        ];
    }

    private function getProjectsTableData(array $projectsFilterData, int $perPage): array
    {
        return [
            'columns' => [
                'id',
                'title',
                'version',
                'status',
                'user',
                'company',
                'company_type',
                'team',
                'csm',
                'template',
                'updated_at',
                'project_duration',
                'last_render',
                'render_time',
                'actions',
            ],
            'projects' => $this->projectFilterRepository->getPaginatedProjectsByFilters(
                $projectsFilterData,
                $perPage
            ),
        ];
    }
}
