<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin;

use App\Application\Events\AdminCreatedUser;
use App\Application\Http\Mappers\UserTeam\TeamAppRoleMapper;
use App\Application\Http\Requests\Admin\User\UserStoreRequest;
use App\Application\Http\Requests\Admin\User\UserUpdateRequest;
use App\Domain\Permissions\AppRoleRepository;
use App\Domain\UserTeam\UserTeamService;
use App\Infrastructure\Company\QueryBuilder\CompanyQueryBuilder;
use App\Infrastructure\User\QueryBuilder\UserQueryBuilder;
use App\Models\Company;
use App\Models\Permissions\AppRole;
use App\Models\User;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Spatie\Permission\Models\Role;

final class UserController extends BaseController
{
    private UserQueryBuilder $userQueryBuilder;
    private CompanyQueryBuilder $companyQueryBuilder;
    private Dispatcher $eventDispatcher;
    private Guard $auth;
    private AppRoleRepository $appRoleRepository;
    private TeamAppRoleMapper $teamAppRoleMapper;
    private UserTeamService $userTeamService;

    public function __construct(
        UserQueryBuilder $userQueryBuilder,
        CompanyQueryBuilder $companyQueryBuilder,
        Dispatcher $eventDispatcher,
        Guard $guard,
        AppRoleRepository $appRoleRepository,
        TeamAppRoleMapper $teamAppRoleMapper,
        UserTeamService $userTeamService,
    ) {
        $this->authorizeResource(User::class);
        $this->userQueryBuilder = $userQueryBuilder;
        $this->companyQueryBuilder = $companyQueryBuilder;
        $this->eventDispatcher = $eventDispatcher;
        $this->auth = $guard;
        $this->appRoleRepository = $appRoleRepository;
        $this->teamAppRoleMapper = $teamAppRoleMapper;
        $this->userTeamService = $userTeamService;
    }

    public function create(): View
    {
        $companies = Company::where('status', '=', Company::STATUS_ACTIVE)->pluck('name', 'id')->prepend(
            'Pas d\'entreprise',
            '',
        );
        $allTeams = [];
        $actualTeams = [];
        $actualRoles = [];
        $allRoles = Role::with('permissions')->get()->pluck('name', 'id')->toArray();
        $user = new User();
        $roles = $this->appRoleRepository->getAllAvailableAppRolesWithAppPermissions();
        $serializedUserTeamAppRoles = [];

        return view(
            'admin.users.modal',
            [
                'user' => $user,
                'actualTeams' => $actualTeams,
                'allTeams' => $allTeams,
                'companies' => $companies,
                'actualRoles' => $actualRoles,
                'allRoles' => $allRoles,
                'roles' => $roles,
                'serializedUserTeamAppRoles' => $serializedUserTeamAppRoles,
            ],
        );
    }

    public function edit(User $user): View
    {
        $this->authorize('canAccessRestrictedData', $user->company);

        $companies = Company::where('status', '=', Company::STATUS_ACTIVE)->pluck('name', 'id')->prepend(
            'Pas d\'entreprise',
            '',
        );
        $allTeams = $user->company == null ? [] : $user->company->teams->pluck('name', 'id')->toArray();
        $actualTeams = $user->teams;
        $roles = array_combine(AppRole::AVAILABLE_ROLES, AppRole::AVAILABLE_ROLES);

        return view(
            'admin.users.edit',
            compact('user', 'allTeams', 'companies', 'actualTeams', 'roles'),
        );
    }

    public function index(Request $request): View|Response
    {
        $query = $this->userQueryBuilder->getBuilder()->with('company')->select('users.*');

        if ($request->has('search')) {
            $search = trim(preg_replace('/\s+/', ' ', $request->get('search')));
            if ($search !== '') {
                $query->searchByName($search);
            }
        }

        if ($request->get('company_id')) {
            $query->where('company_id', $request->get('company_id'));
        }

        if ($request->get('company_type')) {
            $query->searchByCompanyType($request->get('company_type'));
        }

        if ($request->get('is_active') === '0') {
            $query->whereNotNull('users.deleted_at');
        } else {
            if ($request->get('is_active', '1') === '1') {
                $query->whereNull('users.deleted_at');
            }
        }

        $query->when($this->auth->user()->isUsAdminPlayPlayer(), function (Builder $query) {
            return $query->whereHas('company', function (Builder $query) {
                return $query->where('data_is_restricted', false);
            });
        });

        if ($request->get('export') == true) {
            $users = $query->limit(10000)->get();

            return response()->csv($users);
        } else {
            $users = $query->with([
                'roles',
                'teams',
            ])
                ->paginate($request->get('per_page', 10))
                ->appends($request->all());

            $companies = $this->companyQueryBuilder->getBuilder()->orderBy('name')
                ->select('name', 'id')->pluck('name', 'id')
                ->toArray();
            $companyTypes = trans('static.company_type');

            return view('admin.users.index', compact('users', 'companies', 'companyTypes'));
        }
    }

    public function store(UserStoreRequest $request): JsonResponse
    {
        $data = $request->all();
        $data['company_id'] = $request->filled('company_id') ? $request->get('company_id') : null;
        $data['user_from'] = $request->filled('user_from') ? $request->get('user_from') : null;
        $data['user_until'] = $request->filled('user_until') ? $request->get('user_until') : null;
        $user = User::create($data);

        $this->userTeamService->setTeamsWithAppRolesToUser($user, $this->teamAppRoleMapper->fromRequest($request));

        // Update user roles
        $boRoles = $request->get('roles', []);
        $user->roles()->sync($boRoles);

        $this->eventDispatcher->dispatch(new AdminCreatedUser($user));

        return new JsonResponse([
            'success' => true,
            'redirect_uri' => route('admin.users.edit', $user),
        ], JsonResponse::HTTP_CREATED);
    }

    public function update(UserUpdateRequest $request, User $user): RedirectResponse
    {
        $this->authorize('canAccessRestrictedData', $user->company);

        if (!$request->get('is_active', true)) {
            $user->delete();
        }

        if ($request->get('is_active') && $user->trashed()) {
            $user->restore();
        }

        $data = $request->all();
        $data['user_until'] = $request->filled('user_until') ? $request->get('user_until') : null;
        $data['user_from'] = $request->filled('user_from') ? $request->get('user_from') : null;

        $user->update($data);

        return redirect()->back();
    }
}
