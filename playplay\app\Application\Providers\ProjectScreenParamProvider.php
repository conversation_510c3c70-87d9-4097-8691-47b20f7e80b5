<?php

namespace App\Application\Providers;

use App\Domain\ProjectScreen\Param\ProjectScreenParamRepository;
use App\Domain\ProjectScreen\Param\Sanitizer\OptionListSanitizer;
use App\Domain\ProjectScreen\Param\Sanitizer\ProjectScreenParamsSanitizer;
use App\Infrastructure\ProjectScreen\Param\Repositories\EloquentProjectScreenParamRepository;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Support\ServiceProvider;

class ProjectScreenParamProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(ProjectScreenParamRepository::class, EloquentProjectScreenParamRepository::class);
        $this->app->bind(ProjectScreenParamsSanitizer::class, function (Application $app) {
            return new ProjectScreenParamsSanitizer(
                [
                    $app->get(OptionListSanitizer::class),
                ]
            );
        });
    }
}
