<?php

declare(strict_types=1);

namespace App\Application\Http;

use App\Application\Http\Middleware\AuthenticateRenderingCallback;
use App\Application\Http\Middleware\CheckForMaintenanceMode;
use App\Application\Http\Middleware\CheckInactiveCompany;
use App\Application\Http\Middleware\CompanyApiAuthentication;
use App\Application\Http\Middleware\ExpiredUsers;
use App\Application\Http\Middleware\LogModelOperation;
use App\Application\Http\Middleware\LogUserAction;
use App\Application\Http\Middleware\RedirectIfCantSeeBackOffice;
use App\Application\Http\Middleware\SessionIfNeeded;
use App\Application\Http\Middleware\TrustHosts;
use App\Application\Http\Middleware\TrustProxies;
use App\Application\Http\Middleware\UnauthorizeIfUserFromDateIsNotReached;
use App\Application\Http\Middleware\UnregisterDevice;
use App\Application\Http\Middleware\UseAdminDatabaseConnection;
use App\Application\Http\Middleware\VerifyCsrfToken;
use Illuminate\Auth\Middleware\Authenticate;
use Illuminate\Auth\Middleware\AuthenticateWithBasicAuth;
use Illuminate\Auth\Middleware\Authorize;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Kernel as HttpKernel;
use Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull;
use Illuminate\Http\Middleware\SetCacheHeaders;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Routing\Middleware\ThrottleRequests;
use Illuminate\Routing\Middleware\ValidateSignature;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

final class Kernel extends HttpKernel
{
    protected $middleware = [
        TrustProxies::class,
        TrustHosts::class,
        LogUserAction::class,
    ];

    protected $middlewareGroups = [
        'web' => [
            EncryptCookies::class,
            AddQueuedCookiesToResponse::class,
            StartSession::class,
            ShareErrorsFromSession::class,
            SessionIfNeeded::class,
            VerifyCsrfToken::class,
            SubstituteBindings::class,
        ],

        'api' => [
            EncryptCookies::class,
            StartSession::class,
            CheckForMaintenanceMode::class,
            SessionIfNeeded::class,
            SubstituteBindings::class,
            ConvertEmptyStringsToNull::class,
        ],
        'rendering-callbacks-auth' => [
            CheckForMaintenanceMode::class,
            SubstituteBindings::class,
            ConvertEmptyStringsToNull::class,
            AuthenticateRenderingCallback::class,
        ],

        'admin' => [
            CheckForMaintenanceMode::class,
            UseAdminDatabaseConnection::class,
            SessionIfNeeded::class,
            RedirectIfCantSeeBackOffice::class,
            LogModelOperation::class,
        ],

    ];

    protected $routeMiddleware = [
        'auth' => Authenticate::class,
        'auth.basic' => AuthenticateWithBasicAuth::class,
        'custom-company-auth' => CompanyApiAuthentication::class,
        'bindings' => SubstituteBindings::class,
        'cache.headers' => SetCacheHeaders::class,
        'can' => Authorize::class,
        'signed' => ValidateSignature::class,
        'throttle' => ThrottleRequests::class,
        'set-cookies' => AddQueuedCookiesToResponse::class,
        'unregister-device' => UnregisterDevice::class,
        'user-from-date-not-reached' => UnauthorizeIfUserFromDateIsNotReached::class,
        'expired-users' => ExpiredUsers::class,
        'check-inactive-company' => CheckInactiveCompany::class,
    ];
}
