<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Screen;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\Screen\Family\StoreOrUpdateScreenFamilyRequest;
use App\Domain\Screen\ScreenFamilyRepository;
use App\Domain\Screen\ScreenRepository;
use App\Models\Screen\ScreenFamily;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

final class ScreenFamilyController extends BaseController
{
    private ScreenFamilyRepository $screenFamilyRepository;
    private ScreenRepository $screenRepository;

    public function __construct(ScreenFamilyRepository $screenFamilyRepository, ScreenRepository $screenRepository)
    {
        $this->authorizeResource(ScreenFamily::class, ScreenFamily::class);
        $this->screenFamilyRepository = $screenFamilyRepository;
        $this->screenRepository = $screenRepository;
    }

    public function create(): View
    {
        return view('admin.screens.families.create', [
            'screenFamily' => new ScreenFamily(),
            'screens' => $this->screenRepository->getAll()
        ]);
    }

    public function index(Request $request): View
    {
        return view('admin.screens.families.index', [
            'tabActive' => 'families',
            'screenFamilies' => $this->screenFamilyRepository->getPaginatedScreenFamilies(),
            'referer' => $request->getQueryString()
        ]);
    }

    public function showDangerZone(ScreenFamily $screenFamily): View
    {
        $this->authorize('destroy', ScreenFamily::class);

        return view('admin.screens.families.danger-zone', ['screenFamily' => $screenFamily]);
    }

    public function destroy(ScreenFamily $screenFamily): JsonResponse
    {
        $this->screenFamilyRepository->delete($screenFamily);

        return new JsonResponse([
            'success' => true,
            'redirect' => route('admin.screenFamilies.index'),
        ]);
    }

    public function edit(ScreenFamily $screenFamily, Request $request): View
    {
        return view('admin.screens.families.edit', [
            'screenFamily' => $screenFamily,
            'screens' => $this->screenRepository->getAll(),
            'referer' => $request->get('referer')
        ]);
    }

    public function store(StoreOrUpdateScreenFamilyRequest $request): RedirectResponse
    {
        $this->screenFamilyRepository->createFromNameAndScreens($request->get('name'), $request->get('screens_id', []));

        return redirect()->route('admin.screenFamilies.index');
    }

    public function update(ScreenFamily $screenFamily, StoreOrUpdateScreenFamilyRequest $request): RedirectResponse
    {
        $screenFamily->update($request->only(['name']));

        $this->screenFamilyRepository->updateScreensOfFamily(
            $screenFamily,
            $request->get('screens_id', [])
        );

        return redirect()->route('admin.screenFamilies.index', $request->get('referer') ?: []);
    }
}
