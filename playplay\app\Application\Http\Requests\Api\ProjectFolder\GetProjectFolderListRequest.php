<?php

namespace App\Application\Http\Requests\Api\ProjectFolder;

use Illuminate\Foundation\Http\FormRequest;

class GetProjectFolderListRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'page' => ['integer', 'min:1'],
            'per_page' => ['integer', 'min:1', 'max:100'],
            'team_id' => ['integer', 'min:1', 'required'],
            'user_id' => ['integer', 'min:1'],
            'is_private' => ['sometimes', 'boolean'],
            'query' => ['string'],
            'folder_id' => ['integer', 'exists:folders,id'],
            'order_by' => ['sometimes', 'in:updated_at,title'],
            'order_direction' => ['sometimes', 'in:desc,asc'],
        ];
    }
}
