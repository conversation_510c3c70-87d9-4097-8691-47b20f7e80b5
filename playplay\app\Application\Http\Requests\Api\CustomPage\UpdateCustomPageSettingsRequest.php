<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\CustomPage;

use Illuminate\Foundation\Http\FormRequest;

final class UpdateCustomPageSettingsRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'title' => ['sometimes', 'nullable', 'string', 'min:0', 'max:255'],
            'description' => ['sometimes', 'nullable', 'string', 'min:0', 'max:1250'],
            'primary_color' => ['required', 'string', 'min:5', 'max:11'],
            'font_id' => ['required', 'integer', 'min:1'],
            'main_logo_processed_media_id' => ['sometimes', 'nullable', 'integer', 'min:1'],
        ];
    }
}
