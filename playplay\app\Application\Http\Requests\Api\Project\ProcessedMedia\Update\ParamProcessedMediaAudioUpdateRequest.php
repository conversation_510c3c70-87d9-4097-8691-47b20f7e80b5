<?php

namespace App\Application\Http\Requests\Api\Project\ProcessedMedia\Update;

use App\Models\ProcessedMedia;
use App\Models\ScreenParams\BaseParam;
use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Routing\Route;
use Illuminate\Validation\Factory;

class ParamProcessedMediaAudioUpdateRequest extends FormRequest
{
    public static function getRules(): array
    {
        return [
            'param_id' => ['required', 'integer', 'paramIsTypeAudioAndInProject'],
            'trimStart' => ['required', 'numeric', 'min:0'],
            'trimEnd' => ['required', 'numeric', 'greater_than:trimStart'],
        ];
    }

    public function __construct()
    {
        $validationFactory = app(Factory::class);
        $this->checkParamIsTypeAudioAndInProject($validationFactory);
        parent::__construct();
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    public function checkParamIsTypeAudioAndInProject(Factory $validationFactory): void
    {
        $validationFactory->extendImplicit(
            'paramIsTypeAudioAndInProject',
            static function ($attribute, $value): bool {
                /** @var Route $route */
                $route = request()?->route();
                /** @var ?ProcessedMedia $processedMedia */
                $processedMedia = $route->parameter('processedMedia');
                if ($processedMedia === null) {
                    return false;
                }

                $projectScreenIds = $processedMedia->project->projectScreens->pluck('screen_id')->toArray();

                return BaseParam::query()->where('id', $value)
                    ->with([
                        'screenLayout' => function (Builder $queryBuilder) use ($projectScreenIds) {
                            $queryBuilder->whereIn('screen_id', $projectScreenIds);
                        },
                    ])
                    ->where('type', '=', BaseParam::TYPE_AUDIO)
                    ->exists();
            }
        );
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return static::getRules();
    }
}
