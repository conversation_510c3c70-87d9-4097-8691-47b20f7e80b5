<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Screen;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\Screen\Category\ScreenCategoryReorderRequest;
use App\Application\Http\Requests\Admin\Screen\Category\ScreenCategoryStoreRequest;
use App\Application\Http\Requests\Admin\Screen\Category\ScreenCategoryUpdateRequest;
use App\Domain\Screen\ScreenCategoryRepository;
use App\Domain\Screen\ScreenRepository;
use App\Models\ScreenCategory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;

final class ScreenCategoryController extends BaseController
{
    private ScreenCategoryRepository $screenCategoryRepository;
    private ScreenRepository $screenRepository;

    public function __construct(ScreenCategoryRepository $screenCategoryRepository, ScreenRepository $screenRepository)
    {
        $this->authorizeResource(ScreenCategory::class, ScreenCategory::class);
        $this->screenCategoryRepository = $screenCategoryRepository;
        $this->screenRepository = $screenRepository;
    }

    public function create(): View
    {
        return view('admin.screens.categories.create', [
            'screenCategory' => new ScreenCategory(),
            'screens' => $this->screenRepository->getAll(),
        ]);
    }

    public function destroy(ScreenCategory $screenCategory): JsonResponse
    {
        $this->screenCategoryRepository->delete($screenCategory);

        return new JsonResponse([
            'success' => true,
            'redirect' => route('admin.screenCategories.index')
        ]);
    }

    public function edit(ScreenCategory $screenCategory): View
    {
        return view('admin.screens.categories.edit', [
            'screenCategory' => $screenCategory,
            'screens' => $this->screenRepository->getAll(),
        ]);
    }

    public function index(): View
    {
        return view('admin.screens.categories.index', [
            'tabActive' => 'categories',
            'screenCategories' => $this->screenCategoryRepository->getAllOrderedCategories(),
        ]);
    }

    public function reorder(ScreenCategoryReorderRequest $request): JsonResponse
    {
        $this->screenCategoryRepository->reorder($request->get('screen_category_ids'));

        return new JsonResponse(['status' => 'ok'], Response::HTTP_OK);
    }

    public function showDangerZone(ScreenCategory $screenCategory): View
    {
        $this->authorize('destroy', ScreenCategory::class);

        return view('admin.screens.categories.danger-zone', [
            'screenCategory' => $screenCategory,
        ]);
    }

    public function store(ScreenCategoryStoreRequest $request): RedirectResponse
    {
        $this->screenCategoryRepository->createFromNamesAndScreens(
            $request->get('name'),
            $request->get('backoffice_name'),
            $request->get('screens_id', [])
        );

        return redirect()->route('admin.screenCategories.index');
    }

    public function update(ScreenCategory $screenCategory, ScreenCategoryUpdateRequest $request): RedirectResponse
    {
        $screenCategory->update($request->only(['name', 'backoffice_name']));
        $screenCategory->touch();

        $this->screenCategoryRepository->updateScreensOfCategory($screenCategory, $request->get('screens_id', []));

        return redirect()->route('admin.screenCategories.index');
    }
}
