<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\CustomPage;

use App\Application\Http\Controllers\Api\V2\CustomPage\GetCustomPagePublicController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class CustomPagePublicRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::get('/custom-page/token/{token}', GetCustomPagePublicController::class)
            ->name('custom-page.get-public');
    }
}
