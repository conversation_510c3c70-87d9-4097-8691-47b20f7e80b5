<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\ShareableLink;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Project\ProjectShareableLinkRequest;
use App\Domain\ShareableLink\ShareableLinkRepository;
use App\Domain\ShareableLink\ShareableLinkTokenGeneratorInterface;
use App\Domain\User\Action\UserAction;
use App\Domain\User\Action\UserActionService;
use App\Models\Project;
use App\Models\ShareableLink;
use App\Models\User;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

class ShareableLinkSettingsController extends BaseController
{
    use AuthorizesRequests;

    private Guard $guard;
    private ShareableLinkRepository $shareableLinkRepository;
    private UserActionService $userActionService;
    private ShareableLinkTokenGeneratorInterface $tokenGenerator;

    public function __construct(
        ShareableLinkRepository $shareableLinkRepository,
        ShareableLinkTokenGeneratorInterface $tokenGenerator,
        UserActionService $userActionService,
        Guard $guard,
    ) {
        $this->shareableLinkRepository = $shareableLinkRepository;
        $this->userActionService = $userActionService;
        $this->guard = $guard;
        $this->tokenGenerator = $tokenGenerator;
    }

    public function getOrCreate(Project $project): JsonResponse
    {
        $this->authorize('createShareableLink', $project);

        /** @var User $user */
        $user = $this->guard->user();
        $render = $project->lastRenderProjectHtmlProcessed;

        $shareableLink = $this->shareableLinkRepository->getByRenderProjectHtmlId($render->id);

        if ($shareableLink === null) {
            $shareableLink = ShareableLink::query()->create([
                'token' => $this->tokenGenerator->generate(),
                'creator_id' => $user->id,
                'render_project_html_id' => $render->id,
                'has_comments' => true,
            ]);
            $httpCode = Response::HTTP_CREATED;

            $this->userActionService->addUserAction(
                new UserAction(
                    'shareable-link-generated',
                    [],
                    $project->team_id,
                    $project->id
                )
            );
        } else {
            $shareableLink->creator_id = $user->id;
            $shareableLink->update();
            $httpCode = Response::HTTP_ALREADY_REPORTED;
        }

        return $this->sendJsonResponse(new Collection([$shareableLink]), $httpCode);
    }

    public function update(ShareableLink $shareableLink, ProjectShareableLinkRequest $request): JsonResponse
    {
        $this->authorize('update', $shareableLink);

        // TODO : This logic should be on the domain service side
        $hasPasswordValueInRequest = $request->boolean('has_password');
        $enablingHasPassword = $shareableLink->has_password === false && $hasPasswordValueInRequest === true;
        $disablingHasPassword = $shareableLink->has_password === true && $hasPasswordValueInRequest === false;

        $shareableLink->update(
            array_merge(
                $request->only(['has_comments', 'can_download', 'has_password']),
                ['password' => trim($request->get('password', '') ?? '')]
            )
        );

        if ($enablingHasPassword === true || $disablingHasPassword === true) {
            $project = $shareableLink->renderProjectHtml->project;
            $this->userActionService->addUserAction(
                (new UserAction(
                    $enablingHasPassword ? 'shareable-link-password-enabled' : 'shareable-link-password-disabled',
                    [],
                    $project->team_id,
                    $project->id
                ))
            );
        }

        return $this->sendJsonResponse(new Collection([$shareableLink]), Response::HTTP_OK);
    }
}
