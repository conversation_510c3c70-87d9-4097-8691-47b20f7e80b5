<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\User;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\User\UserSwitchProjectRequest;
use App\Infrastructure\Project\ProjectTransferService;
use App\Models\Company;
use App\Models\Project;
use App\Models\Team;
use App\Models\User;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;

final class UserMigrateProjectController extends BaseController
{
    private const MAX_PROJECTS_FOR_PREVIEW = 1000;
    private ProjectTransferService $projectTransferService;
    private Factory $viewFactory;

    public function __construct(
        ProjectTransferService $projectTransferService,
        Factory $viewFactory,
    ) {
        $this->authorizeResource(User::class);
        $this->projectTransferService = $projectTransferService;
        $this->viewFactory = $viewFactory;
    }

    public function show(User $user): View
    {
        $this->authorize('canAccessRestrictedData', $user->company);
        $companies = Company::query()->where('status', '=', Company::STATUS_ACTIVE)->pluck('name', 'id')->prepend(
            'Pas d\'entreprise',
            ''
        );
        $allTeams = $user->company === null ? [] : $user->company->teams->pluck('name', 'id')->toArray();
        $maxLimitReached = Project::where('user_id', $user->id)
            ->where('is_private', false)
            ->where('is_team_template_project', false)
            ->count() > self::MAX_PROJECTS_FOR_PREVIEW;

        return $this->viewFactory->make('admin.users.migrate-projects', [
            'user' => $user,
            'allTeams' => $allTeams,
            'companies' => $companies,
            'projects' => $maxLimitReached
                ? []
                : $user->projects()
                    ->where('is_private', false)
                    ->where('is_team_template_project', false)
                    ->with(['team'])
                    ->get(['id', 'title', 'status', 'updated_at', 'team_id'])->lazy(),
            'maxLimitReached' => $maxLimitReached,
        ]);
    }

    public function migrateProjectsToAnotherUser(User $user, UserSwitchProjectRequest $request): RedirectResponse
    {
        $this->authorize('canAccessRestrictedData', $user->company);
        $this->authorize('update', $user);

        $targetUserId = (int) $request->input('user_id');
        /** @var Team $targetTeam */
        $targetTeam = Team::query()->find($request->input('team_id'));

        $rawProjectIds = $request->input('project_ids', '');
        $transferAll = $request->boolean('transfer_all');

        if ($transferAll === true) {
            $projectIds = null;
        } else {
            parse_str(urldecode($rawProjectIds), $projectIds);

            if ($rawProjectIds !== '') {
                $projectIds = array_values($projectIds['project_ids_serialized']);
            }
        }

        $this->projectTransferService->transferProjects($user, $targetTeam, $targetUserId, $projectIds);

        return redirect()->back();
    }
}
