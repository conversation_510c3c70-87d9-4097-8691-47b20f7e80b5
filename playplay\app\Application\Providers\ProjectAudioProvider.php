<?php

namespace App\Application\Providers;

use App\Domain\ProjectAudio\ProjectAudioRepository;
use App\Infrastructure\ProjectAudio\EloquentProjectAudioRepository;
use Illuminate\Support\ServiceProvider;

class ProjectAudioProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(ProjectAudioRepository::class, EloquentProjectAudioRepository::class);
    }
}
