<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Admin;

use App\Application\Http\Controllers\Admin\Screen\ScreenCategoryController;
use App\Application\Http\Controllers\Admin\Screen\ScreenController;
use App\Application\Http\Controllers\Admin\Screen\ScreenFamilyController;
use App\Application\Http\Controllers\Admin\Screen\ScreenLayoutController;
use App\Application\Http\Controllers\Admin\Screen\ScreenLayoutParamController;
use App\Application\Http\Controllers\Admin\Screen\ScreenVersionController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class ScreenRouteMapper implements RouteMapper
{
    public function map(): void
    {
        $this->mapScreens();
        $this->mapCategories();
        $this->mapFamilies();
    }

    private function mapCategories(): void
    {
        Route::group([
            'prefix' => '/screensCategories',
            'as' => 'screenCategories.',
        ], static function (Router $router) {
            $router->put('/reorder', [ScreenCategoryController::class, 'reorder'])->name('reorder');
            $router->get('/', [ScreenCategoryController::class, 'index'])->name('index');
            $router->get('/create', [ScreenCategoryController::class, 'create'])->name('create');
            $router->post('/', [ScreenCategoryController::class, 'store'])->name('store');
            $router->get('/{screenCategory}/edit', [ScreenCategoryController::class, 'edit'])->name('edit');
            $router->put('/{screenCategory}', [ScreenCategoryController::class, 'update'])->name('update');
            $router->delete('/{screenCategory}', [ScreenCategoryController::class, 'destroy'])->name('destroy');
            $router->get('/{screenCategory}/danger-zone', [ScreenCategoryController::class, 'showDangerZone'])
                ->name('danger-zone');
        });
    }

    private function mapFamilies(): void
    {
        Route::group([
            'prefix' => '/screenFamilies',
            'as' => 'screenFamilies.',
        ], static function (Router $router) {
            $router->get('/', [ScreenFamilyController::class, 'index'])->name('index');
            $router->get('/create', [ScreenFamilyController::class, 'create'])->name('create');
            $router->post('/', [ScreenFamilyController::class, 'store'])->name('store');
            $router->get('/{screenFamily}/edit', [ScreenFamilyController::class, 'edit'])->name('edit');
            $router->put('/{screenFamily}', [ScreenFamilyController::class, 'update'])->name('update');
            $router->delete('/{screenFamily}', [ScreenFamilyController::class, 'destroy'])->name('destroy');
            $router->get('/{screenFamily}/danger-zone', [ScreenFamilyController::class, 'showDangerZone'])
                ->name('danger-zone');
        });
    }

    private function mapLayout(Router $screenRouter): void
    {
        $screenRouter->group([
            'prefix' => '/{screen}/screenLayouts',
            'as' => 'screenLayouts.',
        ], function (Router $router) {
            $router->put('/reorder', [ScreenLayoutController::class, 'reorder'])->name('reorder');
            $router->get('/create', [ScreenLayoutController::class, 'create'])->name('create');
            $router->post('/', [ScreenLayoutController::class, 'store'])->name('store');
            $router->get('/{screenLayout}/edit', [ScreenLayoutController::class, 'edit'])->name('edit');
            $router->put('/{screenLayout}', [ScreenLayoutController::class, 'update'])->name('update');
            $router->delete('/{screenLayout}', [ScreenLayoutController::class, 'destroy'])->name('destroy');

            $router->put('/{screenLayout}/reorder-params', [ScreenLayoutParamController::class, 'reorder'])
                ->name('params.reorder');

            $this->mapLayoutParams($router);
        });
    }

    private function mapLayoutParams(Router $layoutRouter): void
    {
        $layoutRouter->group([
            'prefix' => '/{screenLayout}/params',
            'as' => 'params.',
        ], static function (Router $router) {
            $router->get('/create', [ScreenLayoutParamController::class, 'create'])->name('create');
            $router->post('/', [ScreenLayoutParamController::class, 'store'])->name('store');
            $router->get('/{screen_param}/edit', [ScreenLayoutParamController::class, 'edit'])->name('edit');
            $router->put('/{screen_param}', [ScreenLayoutParamController::class, 'update'])->name('update');
            $router->delete('/{screen_param}', [ScreenLayoutParamController::class, 'destroy'])->name('destroy');
        });
    }

    private function mapScreens(): void
    {
        Route::group([
            'prefix' => '/screens',
            'as' => 'screens.',
        ], function (Router $router) {
            $router->get('/', [ScreenController::class, 'index'])->name('index');
            $router->get('/create', [ScreenController::class, 'create'])->name('create');
            $router->post('/', [ScreenController::class, 'store'])->name('store');
            $router->get('/{screen}/edit', [ScreenController::class, 'edit'])->name('edit');
            $router->put('/{screen}', [ScreenController::class, 'update'])->name('update');
            $router->delete('/{screen}', [ScreenController::class, 'destroy'])->name('destroy');
            $router->get('/{screen}/danger-zone', [ScreenController::class, 'showDangerZone'])->name('danger-zone');
            $router->get('/{screen}/duplicate', [ScreenController::class, 'duplicate'])->name('duplicate');
            $router->get('/{screen}/card', [ScreenController::class, 'card'])->name('card');
            $router->get('/versions', [ScreenVersionController::class, 'index'])->name('versions');
            $router->post('/versions', [ScreenVersionController::class, 'bulkUpdate'])->name('bulk-update-versions');

            $this->mapLayout($router);
        });
    }
}
