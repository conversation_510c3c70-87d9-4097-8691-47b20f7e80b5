<?php

declare(strict_types=1);

namespace App\Application\Console\Commands\Getty;

use App\Application\Mail\Getty\InvalidAssetUsages;
use App\Domain\AssetUsages\AssetUsageRepository;
use App\Domain\AssetUsages\AssetUsagesSender;
use Illuminate\Config\Repository as ConfigRepository;
use Illuminate\Console\Command;
use Illuminate\Contracts\Mail\Mailer;
use Psr\Log\LoggerInterface;

final class PublishGettyAssetUsages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'getty:publish-asset-usages
                            {--D|date= : Usage date.}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send to Getty asset usages for a specific day.';

    private AssetUsageRepository $assetUsageRepository;
    private AssetUsagesSender $sender;
    private Mailer $mailer;
    private ConfigRepository $configRepository;
    private LoggerInterface $logger;

    public function __construct(
        AssetUsageRepository $assetUsageRepository,
        AssetUsagesSender $sender,
        Mailer $mailer,
        ConfigRepository $configRepository,
        LoggerInterface $logger
    ) {
        parent::__construct();

        $this->assetUsageRepository = $assetUsageRepository;
        $this->sender = $sender;
        $this->mailer = $mailer;
        $this->configRepository = $configRepository;
        $this->logger = $logger;
    }

    public function handle(): int
    {
        try {
            $usageDate = new \DateTimeImmutable($this->option('date') ?? '-1 day');
        } catch (\Exception $exception) {
            $this->error("Invalid date given. {$exception->getMessage()}");

            return 1;
        }

        $message = "Send asset usage for date : {$usageDate->format('Y-m-d')}";
        $this->info($message);
        $this->logger->info($message);

        $invalidAssetIds = $this->sender->send($this->assetUsageRepository->getAllByDate($usageDate));

        if ($invalidAssetIds !== []) {
            $this->logger->warning('Getty usage : Not sent asset usages', [
                'date' => $usageDate->format('Y-m-d'),
                'all_asset_ids' => $invalidAssetIds,
            ]);

            $gettyContact = $this->configRepository->get('services.stock_apis.getty.contact_email');
            $playplayContact = $this->configRepository->get('services.stock_apis.getty.playplay_contact_email');
            $this->mailer
                ->to($gettyContact)
                ->cc($playplayContact)
                ->send(new InvalidAssetUsages($invalidAssetIds));

            $this->logger->warning("An error occurred while sending getty asset usages. {$usageDate->format('Y-m-d')}");

            return 1;
        }

        return 0;
    }
}
