<?php

declare(strict_types=1);

namespace App\Application\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Http\Request;

final class RedirectIfCantSeeBackOffice
{
    private ?User $user;

    public function __construct(Guard $auth)
    {
        /** @var User $user */
        $user = $auth->user();
        $this->user = $user;
    }

    public function handle(Request $request, Closure $next)
    {
        if ($this->user->can('see-backoffice')) {
            return $next($request);
        }

        return redirect('app/home');
    }
}
