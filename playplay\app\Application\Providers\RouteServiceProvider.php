<?php

namespace App\Application\Providers;

use App\Application\Http\Controllers\Auth\ForgotPasswordController;
use App\Application\Http\Controllers\Auth\LoginController;
use App\Application\Http\Controllers\Front\ShareLinkController;
use App\Application\Http\Controllers\Front\SsoMobileCallbackController;
use App\Models\CorporateLibraryItem;
use App\Models\Music\Music;
use App\Models\Project;
use App\Models\Renders\RenderMedia;
use App\Models\Renders\RenderProjectHtml;
use App\Models\Screen;
use App\Models\ScreenParams\BaseParam;
use App\Models\Template;
use App\Models\User;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class RouteServiceProvider extends ServiceProvider
{
    /**
     * This namespace is set as the URL generator's root namespace.
     *
     * @var string
     */
    protected $namespace = 'App\Http\Controllers';

    /**
     * Define your route model bindings, pattern filters, etc.
     */
    public function boot()
    {
        parent::boot();

        Route::bind('user', function (int $id) {
            return User::withTrashed()->find($id);
        });

        Route::bind('screen', function (int $id) {
            return Screen::withTrashed()->find($id);
        });

        Route::bind('template', function (int $id) {
            return Template::withTrashed()->find($id);
        });

        Route::bind('screen_param', function (int $id) {
            return BaseParam::withTrashed()->find($id);
        });

        Route::bind('renderProjectsHtml', function (int $id) {
            return RenderProjectHtml::find($id);
        });

        Route::bind('render_projects_html', function (int $id) {
            return RenderProjectHtml::find($id);
        });

        Route::bind('project', function (int $id) {
            if (Route::currentRouteName() === 'admin.projects.restore') {
                return Project::withTrashed()->find($id);
            }

            return Project::find($id);
        });

        Route::model('renderMedia', RenderMedia::class);

        Route::model('option_music', Music::class);

        Route::model('library-item', CorporateLibraryItem::class);
    }

    /**
     * Define the routes for the application.
     */
    public function map()
    {
        $this->mapAuthRoutes();
        $this->mapFrontRoute();
    }

    private function mapAuthRoutes()
    {
        Route::group([
            'middleware' => 'web',
            'as' => 'auth.',
        ], function (Router $router) {
            $router->any('logout', LoginController::class . '@logout')->name('logout');
            $router->get('password/reset', ForgotPasswordController::class . '@showLinkRequestForm')->name(
                'passwordReset'
            );
            $router->post('password/email', ForgotPasswordController::class . '@sendResetLinkEmail')->name(
                'passwordSendEmail'
            );
        });
    }

    private function mapFrontRoute(): void
    {
        Route::get('/app/share/{companySlug}/{shareLinkToken}', [ShareLinkController::class, 'show'])
            ->name('app.share.link');
        Route::get('/app/login-sso-mobile-callback', [SsoMobileCallbackController::class, 'show'])
            ->name('app.sso.mobile.callback');
    }
}
