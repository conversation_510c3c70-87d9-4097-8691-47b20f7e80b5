<?php

namespace App\Application\Http\Requests\Api\Project\ProcessedMedia\Store;

use App\Domain\Project\ProjectFormat;
use App\Domain\RawMedia\RawMediaSource;
use App\Domain\RawMedia\RawMediaType;
use App\Models\RawMedia;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Factory;

class ProcessedMediaOutroStoreRequest extends FormRequest
{
    public static function getRules(): array
    {
        return [
            'raw_media_id' => ['required', 'isImageOrVideo'],
            'format' => ['required', 'in:' . implode(',', ProjectFormat::FORMATS)],
            'source' => ['required', 'in:' . RawMediaSource::UPLOAD],
        ];
    }

    public function __construct()
    {
        $validationFactory = app(Factory::class);
        $this->checkRawMediaIsImageOrVideo($validationFactory);
        parent::__construct();
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    public function checkRawMediaIsImageOrVideo(Factory $validationFactory): void
    {
        $validationFactory->extendImplicit(
            'isImageOrVideo',
            function () {
                $rawMedia = RawMedia::select('type')->find($this->get('raw_media_id'));
                if ($rawMedia === null) {
                    return false;
                }

                return in_array(
                    $rawMedia->type?->value,
                    [RawMediaType::IMAGE, RawMediaType::VIDEO],
                    true
                );
            }
        );
    }

    public function rules(): array
    {
        return static::getRules();
    }
}
