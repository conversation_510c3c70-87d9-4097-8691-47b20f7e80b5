<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Team;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Domain\Template\Repositories\CategoryRepository;
use App\Models\Category;
use App\Models\Team;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

final class TemplateController extends BaseController
{
    private CategoryRepository $categoryRepository;

    public function __construct(CategoryRepository $categoryRepository)
    {
        $this->categoryRepository = $categoryRepository;
    }

    public function index(Team $team): View
    {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('view', $team);

        return view('admin.teams.templates.index', [
            'tabActive' => 'templates',
            'genericTemplateCategories' => $this->categoryRepository->getGenericCategoriesForTeam($team),
            'customTemplateCategories' => $this->categoryRepository->getCustomCategoriesForTeam($team),
            'team' => $team,
        ]);
    }

    public function disable(Team $team, Category $category): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);

        $team->categories()->detach($category->id);

        return new JsonResponse(['status' => 'ok'], Response::HTTP_OK);
    }

    public function enable(Team $team, Category $category): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);

        $team->categories()->syncWithoutDetaching([$category->id]);

        return new JsonResponse(['status' => 'ok'], Response::HTTP_OK);
    }
}
