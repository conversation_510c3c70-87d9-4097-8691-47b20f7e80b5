<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\DataSeeding;

use App\Domain\DataSeeding\DataSeedingException;
use App\Domain\DataSeeding\ModelContainer;
use App\Infrastructure\DataSeeding\DatabaseSeeder;
use App\Infrastructure\DataSeeding\DatabaseSeedsCleaner;
use Illuminate\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Symfony\Component\HttpFoundation\Response;

final class SeedingController extends Controller
{
    public function seed(Application $app, DatabaseSeeder $databaseSeeder, ModelContainer $modelContainer): JsonResponse
    {
        if ($app->environment() === 'production') {
            return new JsonResponse([], Response::HTTP_FORBIDDEN);
        }

        try {
            $seedId = $databaseSeeder->seed();
        } catch (DataSeedingException $ex) {
            return new JsonResponse([
                'error' => $ex->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return new JsonResponse([
            'timestamp' => $seedId,
            'data' => array_values((array) $modelContainer)
        ], Response::HTTP_OK);
    }

    public function clean(Application $app, DatabaseSeedsCleaner $databaseSeedsCleaner): JsonResponse
    {
        if ($app->environment() === 'production') {
            return new JsonResponse([], Response::HTTP_FORBIDDEN);
        }

        try {
            $databaseSeedsCleaner->clean();
        } catch (DataSeedingException $ex) {
            return new JsonResponse([
                'error' => $ex->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return new JsonResponse([], Response::HTTP_NO_CONTENT);
    }
}
