<?php

namespace App\Application\Http\Controllers\Api\Render;

use App\Models\RenderJob;
use App\Models\Renders\RenderMedia;
use App\Models\Renders\RenderSubtitle;
use App\Services\Processing\WorkflowManager as ProcessingWorkflowManager;
use App\Services\Rendering\WorkflowManager as RenderingWorkflowManager;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;

class RenderJobController extends Controller
{
    public function callback(
        RenderJob $job,
        Request $request,
        RenderingWorkflowManager $renderingWorkflowManager,
        ProcessingWorkflowManager $processingWorkflowManager
    ) {
        $workflowManager = in_array($job->parent_type, [RenderMedia::class, RenderSubtitle::class], true)
            ? $processingWorkflowManager
            : $renderingWorkflowManager;

        if ($request->has('error')) {
            $retry = $workflowManager->newError(
                $job,
                $request->input('error'),
                [
                    'config' => $request->input('config'),
                    'metrics' => $request->input('metrics'),
                ],
                $request->input('result')
            );

            return new JsonResponse(null, $retry ? 449 : JsonResponse::HTTP_OK);
        }

        if ($request->has('result')) {
            $workflowManager->ackJob($job, $request->input('result'), [
                'config' => $request->input('config'),
                'metrics' => $request->input('metrics'),
            ]);

            return new JsonResponse();
        }

        return new Response(
            'Error: missing error or result',
            Response::HTTP_BAD_REQUEST
        );
    }
}
