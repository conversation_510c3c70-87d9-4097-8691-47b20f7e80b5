<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\ShareableLink;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\ShareableLink\ShareableLinkCommentRequest;
use App\Domain\ShareableLink\ShareableLinkCommentService;
use App\Models\ShareableLink;
use App\Models\ShareableLink\Comment;
use App\Models\User;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\NotAcceptableHttpException;

final class ShareableLinkCommentController extends BaseController
{
    private ShareableLinkCommentService $commentService;

    public function __construct(ShareableLinkCommentService $shareableLinkCommentService)
    {
        $this->commentService = $shareableLinkCommentService;
    }

    public function destroy(Guard $auth, ShareableLink $shareableLink, Comment $comment): JsonResponse
    {
        if (!$this->commentService->hasCommentsEnabled($shareableLink)) {
            throw new NotAcceptableHttpException('Comments on sharelink are disabled');
        }

        /** @var User $user */
        $user = $auth->user();
        if (!$this->commentService->canUserManageComment($user, $comment, $shareableLink)) {
            throw new AccessDeniedHttpException();
        }

        $comment->delete();

        return $this->sendJsonResponse(new Collection([]), Response::HTTP_NO_CONTENT);
    }

    public function store(Guard $auth, ShareableLink $shareableLink, ShareableLinkCommentRequest $request): JsonResponse
    {
        if (!$this->commentService->hasCommentsEnabled($shareableLink)) {
            throw new NotAcceptableHttpException('Comments on sharelink are disabled');
        }

        $comment = $shareableLink->comments()->create([
            'text' => $request->get('text'),
            'username' => $request->get('username'),
            'user_id' => $auth->id(),
        ]);

        return $this->sendJsonResponse(new Collection([$comment]), Response::HTTP_CREATED);
    }

    public function update(
        Guard $auth,
        ShareableLink $shareableLink,
        Comment $comment,
        ShareableLinkCommentRequest $request
    ): JsonResponse {
        if (!$this->commentService->hasCommentsEnabled($shareableLink)) {
            throw new NotAcceptableHttpException('Comments on sharelink are disabled');
        }

        /** @var User $user */
        $user = $auth->user();
        if (!$this->commentService->canUserManageComment($user, $comment, $shareableLink)) {
            throw new AccessDeniedHttpException();
        }

        $comment->update($request->all());

        return $this->sendJsonResponse(new Collection([$comment]), Response::HTTP_OK);
    }
}
