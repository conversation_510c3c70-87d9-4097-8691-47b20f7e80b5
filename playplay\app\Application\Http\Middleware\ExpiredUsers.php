<?php

declare(strict_types=1);

namespace App\Application\Http\Middleware;

use App\Models\Company;
use App\Models\User;
use Closure;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Routing\ResponseFactory;
use Illuminate\Translation\Translator;
use Symfony\Component\HttpFoundation\Response;

final class ExpiredUsers
{
    private Guard $guard;
    private ResponseFactory $factory;
    private Translator $translator;

    public function __construct(Guard $guard, ResponseFactory $responseFactory, Translator $translator)
    {
        $this->guard = $guard;
        $this->factory = $responseFactory;
        $this->translator = $translator;
    }

    public function handle($request, Closure $next)
    {
        /** @var User $user */
        $user = $this->guard->user();

        if ($user->company->isInactiveFreeTrial()) {
            return $this->factory->make(
                [
                    'message' => $this->translator->get('message.payment_required'),
                    'company_type' => Company::TYPE_FREE_TRIAL
                ],
                Response::HTTP_PAYMENT_REQUIRED,
            );
        }

        return $next($request);
    }
}
