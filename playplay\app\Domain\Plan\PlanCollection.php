<?php

declare(strict_types=1);

namespace App\Domain\Plan;

use App\Models\Feature;
use App\Models\Plan;
use Illuminate\Support\Collection;

class PlanCollection extends Collection
{
    public function buildFeatureValueForPlans(string $featureName): array
    {
        $valuesForPlans = [];
        /** @var Plan $plan */
        foreach ($this as $plan) {
            $valuesForPlans[$plan->id] = $plan->getValueOfFeature($featureName);
        }

        return $valuesForPlans;
    }

    public function getEnterprisePlanId(): ?int
    {
        return $this->getPlanIdByName('enterprise');
    }

    public function getStandardPlanId(): ?int
    {
        return $this->getPlanIdByName('standard');
    }

    public function getFreeTrialPlanId(): ?int
    {
        return $this->getPlanIdByName('free-trial');
    }

    public function isEnterpriseFeature(Feature $feature): bool
    {
        $featureValuesForPlans = $this->buildFeatureValueForPlans($feature->name);
        $idPlanEnterprise = $this->getEnterprisePlanId();
        $idPlanStandard = $this->getStandardPlanId();

        if (!array_key_exists($idPlanEnterprise, $featureValuesForPlans)) {
            return false;
        }

        if (!array_key_exists($idPlanStandard, $featureValuesForPlans)) {
            return false;
        }

        $enterpriseFeatureValue = $featureValuesForPlans[$idPlanEnterprise];
        $standardFeatureValue = $featureValuesForPlans[$idPlanStandard];
        if ($feature->type === 'Boolean') {
            return $enterpriseFeatureValue === 1 && $standardFeatureValue === 0;
        }

        return $enterpriseFeatureValue !== 0 && $standardFeatureValue === 0;
    }

    private function getPlanIdByName(string $planName): ?int
    {
        return $this->firstWhere('name', $planName)->id ?? null;
    }
}
