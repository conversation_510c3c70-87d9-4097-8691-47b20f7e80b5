<?php

declare(strict_types=1);

namespace App\Domain\Billing;

interface CompanyRepository
{
    /**
     * @throws PlanhatCompanyException
     */
    public function getById(int $id): PlanhatCompany;

    /**
     * @throws PlanhatCompanyException
     */
    public function setPhaseToExit(int $id): void;

    /**
     * @throws PlanhatCompanyException
     */
    public function setPhaseToOngoing(int $id): void;
}
