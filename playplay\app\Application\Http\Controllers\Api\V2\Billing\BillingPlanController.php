<?php

namespace App\Application\Http\Controllers\Api\V2\Billing;

use App\Application\Http\Controllers\Api\BaseController;
use App\Infrastructure\Billing\StripeSubscriptionService;
use Illuminate\Http\JsonResponse;
use Stripe\Exception\ApiErrorException;
use Symfony\Component\HttpFoundation\Response;

class BillingPlanController extends BaseController
{
    private StripeSubscriptionService $stripeSubscriptionService;

    public function __construct(StripeSubscriptionService $stripeSubscriptionService)
    {
        $this->stripeSubscriptionService = $stripeSubscriptionService;
    }

    /**
     * @throws ApiErrorException
     */
    public function index(): JsonResponse
    {
        return $this->sendJsonResponse(
            $this->stripeSubscriptionService->getAvailablePlans(),
            Response::HTTP_OK
        );
    }
}
