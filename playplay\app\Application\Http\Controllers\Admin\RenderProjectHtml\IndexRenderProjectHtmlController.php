<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\RenderProjectHtml;

use App\Application\Http\Controllers\Admin\AbstractRenderController;
use App\Infrastructure\Render\RenderProject\RenderProjectQueryBuilder;
use App\Models\Project;
use App\Models\Renders\RenderProjectHtml;
use App\Models\User;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class IndexRenderProjectHtmlController extends AbstractRenderController
{
    private RenderProjectQueryBuilder $renderProjectQueryBuilder;
    private Guard $guard;
    private Factory $viewFactory;

    public function __construct(
        RenderProjectQueryBuilder $renderProjectQueryBuilder,
        Guard $guard,
        Factory $viewFactory
    ) {
        $this->renderProjectQueryBuilder = $renderProjectQueryBuilder;
        $this->guard = $guard;
        $this->viewFactory = $viewFactory;
    }

    public function __invoke(Request $request): View | Response
    {
        $this->authorize('index', RenderProjectHtml::class);

        $data = $request->all([
            'user_id',
            'project_id',
            'company_id',
            'company_type',
            'team_id',
            'status',
        ]);

        // TODO : move this in a repository
        $query = $this->createQueryBuilder();

        $query = $this->filterRestrictedCompanies($query);

        $query = $this->filterBy($query, $request);

        if ($request->boolean('export') === true) {
            return $this->createExportResponse($query);
        }

        $query = $query->select('render_projects_html.*')->groupBy('render_projects_html.id')->distinct();

        $renders = $query
            ->simplePaginate($request->get('per_page', 50))
            ->appends($request->all());

        $columns = $this->getColumns();

        return $this->viewFactory->make(
            'admin.renders.index',
            compact('data', 'columns', 'renders')
        );
    }

    private function filterBy(Builder $query, Request $request): Builder
    {
        $filters = [
            'projects.id' => $request->get('project_id'),
            'projects.user_id' => $request->get('user_id'),
            'projects.team_id' => $request->get('team_id'),
            'projects.company_id' => $request->get('company_id'),
            'projects.company_type' => $request->get('company_type'),
            'render_projects_html.status' => $request->get('status'),
            'projects.status' => $request->get('download') === 1 ? Project::STATUS_DOWNLOADED : null,
        ];

        return $this->applyFilters($query, $filters);
    }

    private function filterRestrictedCompanies(Builder $query): Builder
    {
        /** @var User $user */
        $user = $this->guard->user();

        if ($user->isUsAdminPlayPlayer()) {
            $query->join('companies', 'projects.company_id', '=', 'companies.id')
                ->where('companies.data_is_restricted', false);
        }

        return $query;
    }

    private function createQueryBuilder(): Builder
    {
        return $this->renderProjectQueryBuilder->getBuilder(new RenderProjectHtml())->with([
            'downloads',
            'project.user:id,first_name,last_name',
            'project.renders',
            'project.company:id,name,type,csm_id',
            'project.team:id,name',
            'project.template:id,name,is_team_template',
            'project.template',
        ])
            ->whereNull('render_duplicated_id')
            ->orderByDesc('render_projects_html.id');
    }

    private function createExportResponse(Builder $query): Response
    {
        // Filter PlayPlay company
        if (!(new Collection($query->getQuery()->joins))->pluck('table')->contains('projects AS pr')) {
            $query = $query->join('projects AS pr', 'pr.id', '=', 'render_projects_html.project_id');
        }

        if (!(new Collection($query->getQuery()->joins))->pluck('table')->contains('companies')) {
            $query = $query->join('companies', 'pr.company_id', '=', 'companies.id');
        }

        return $this->export($query);
    }

    private function getColumns(): array
    {
        return [
            'id',
            'title',
            'project_id',
            'type',
            'user',
            'company',
            'company_type',
            'team',
            'csm',
            'template',
            'format',
            'day',
            'time',
            'waiting_time',
            'render_time',
            'download',
            'actions',
        ];
    }
}
