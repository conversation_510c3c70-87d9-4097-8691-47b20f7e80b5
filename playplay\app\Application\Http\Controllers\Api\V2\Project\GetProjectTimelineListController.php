<?php

namespace App\Application\Http\Controllers\Api\V2\Project;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\ProjectTimeline\ProjectTimelineIndexRequest;
use App\Domain\Audio\AudioTimelineSerializer;
use App\Domain\Project\ProcessedMedia\RelationType;
use App\Models\Project;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;

final class GetProjectTimelineListController extends BaseController
{
    use AuthorizesRequests;

    private AudioTimelineSerializer $audioTimelineSerializer;

    public function __construct(AudioTimelineSerializer $audioTimelineSerializer)
    {
        $this->audioTimelineSerializer = $audioTimelineSerializer;
    }

    public function __invoke(Project $project, ProjectTimelineIndexRequest $request): JsonResponse
    {
        $this->authorize('view', $project);
        $relationType = RelationType::fromKey($request->input('relation_type_key'));

        $project->processedMedias->loadMissing('rawMedia.lastRender');

        return $this->sendJsonResponse(
            new Collection($this->audioTimelineSerializer->serialize(
                $relationType,
                $project->processedMedias->all()
            )),
            JsonResponse::HTTP_OK
        );
    }
}
