<?php

declare(strict_types=1);

namespace App\Application\Mail;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

final class CreatePassword extends Mailable
{
    use Queueable, SerializesModels;

    private string $token;
    private string $email;
    private string $name;

    public function __construct(User $user)
    {
        $this->token = app('auth.password.broker.new_users')->createToken($user);
        $this->email = $user->email;
        $this->name = $user->first_name;
    }

    public function build(): self
    {
        $email = http_build_query(['email' => $this->email]);
        $url = url("/app/password/create/{$this->token}?{$email}");

        return $this->subject(__('email.create_password.subject'))
            ->markdown('emails.passwords.create', [
                'email' => $this->email,
                'name' => $this->name,
                'token' => $this->token,
                'url' => $url,
            ]);
    }
}
