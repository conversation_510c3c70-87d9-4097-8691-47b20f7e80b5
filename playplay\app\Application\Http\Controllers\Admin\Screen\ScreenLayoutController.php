<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Screen;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\Screen\Layout\ReorderScreenLayoutsRequest;
use App\Application\Http\Requests\Admin\Screen\Layout\StoreScreenLayoutRequest;
use App\Application\Http\Requests\Admin\Screen\Layout\UpdateScreenLayoutRequest;
use App\Models\Screen;
use App\Models\ScreenLayout;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

final class ScreenLayoutController extends BaseController
{
    private Factory $viewFactory;

    public function __construct(Factory $viewFactory)
    {
        $this->authorizeResource(ScreenLayout::class, ScreenLayout::class);
        $this->viewFactory = $viewFactory;
    }

    public function create(Screen $screen): View
    {
        $this->authorize('create', ScreenLayout::class);

        return $this->viewFactory->make(
            'admin.screens.layouts.form',
            ['screen' => $screen, 'screenLayout' => new ScreenLayout()]
        );
    }

    /**
     * @throws ValidationException|AuthorizationException
     */
    public function destroy(Screen $screen, ScreenLayout $screenLayout): RedirectResponse
    {
        if ($screenLayout->params->isNotEmpty()) {
            throw ValidationException::withMessages(['Cannot delete layout with params']);
        }

        $screenLayout->delete();

        return redirect()->route('admin.screens.edit', ['screen' => $screen]);
    }

    public function edit(Screen $screen, ScreenLayout $screenLayout): View
    {
        return $this->viewFactory->make(
            'admin.screens.layouts.form',
            ['screen' => $screen, 'screenLayout' => $screenLayout]
        );
    }

    public function reorder(Screen $screen, ReorderScreenLayoutsRequest $reorderScreenLayoutsRequest): void
    {
        foreach ($reorderScreenLayoutsRequest->get('screen_layout_ids', []) as $index => $screenLayoutId) {
            $screenLayout = $screen->layouts->firstWhere('id', $screenLayoutId);
            $screenLayout->update(['order' => $index]);
        }
    }

    public function store(Screen $screen, StoreScreenLayoutRequest $request): JsonResponse
    {
        $screenLayout = $screen->layouts()->create(
            array_merge($request->only(['type', 'default', 'max']) + ['order' => $screen->layouts->count()])
        );

        return new JsonResponse(['screen_layout' => $screenLayout,], Response::HTTP_CREATED);
    }

    public function update(Screen $screen, ScreenLayout $screenLayout, UpdateScreenLayoutRequest $request): JsonResponse
    {
        $screenLayout->update(
            $request->only([
                'type',
                'default',
                'max',
                'order',
            ])
        );

        return new JsonResponse(['screen_layout' => $screenLayout,], Response::HTTP_OK);
    }
}
