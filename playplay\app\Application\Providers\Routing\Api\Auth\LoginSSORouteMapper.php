<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Auth;

use App\Application\Http\Controllers\Api\V2\Auth\LoginSSOController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class LoginSSORouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group(['middleware' => 'user-from-date-not-reached'], static function (Router $router) {
            $router->post('login-sso', [LoginSSOController::class, 'login'])->name('login-sso');
            $router->post('login-sso-mobile', [LoginSSOController::class, 'loginMobile'])->name('login-sso-mobile');
        });
    }
}
