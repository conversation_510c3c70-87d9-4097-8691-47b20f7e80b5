<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\SignUp;

use App\Domain\Localization\SupportedLanguages;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

final class FreeTrialSignUpRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'company_name' => ['sometimes', 'nullable', 'max:255'],
            'first_name' => ['required', 'max:50', 'name'],
            'last_name' => ['required', 'max:50', 'name'],
            'email' => ['bail', 'required', 'email', 'uniqueFreeTrialActive'],
            'language' => ['required', Rule::in(SupportedLanguages::values())],
        ];
    }
}
