<?php

declare(strict_types=1);

namespace App\Domain\Audio;

use App\Domain\Time\Duration;
use App\Models\ProcessedMedia;

class MusicSerializer
{
    /**
     * @param ProcessedMedia[] $processedMedias
     */
    public function serialize(
        array $processedMedias,
        int $fadeInDuration,
        int $fadeBetweenDuration,
        int $fadeOutDuration
    ): array {
        $serializedData = [];

        foreach ($this->sortByStartAscending($processedMedias) as $key => $processedMedia) {
            $serializedData[] = $this->serializeMusic(
                $processedMedia,
                $key,
                $fadeInDuration,
                $fadeBetweenDuration,
                $fadeOutDuration,
                $processedMedias
            );
        }

        return $serializedData;
    }

    private function serializeMusic(
        ProcessedMedia $processedMedia,
        int $key,
        int $fadeInDuration,
        int $fadeBetweenDuration,
        int $fadeOutDuration,
        array $processedMedias
    ): array {
        $data = $processedMedia->data ?? [];

        $trimStart = $data['trimStart'] ?? null;
        $trimEnd = $data['trimEnd'] ?? null;
        $start = $data['start'] ?? 0;
        $duration = $processedMedia->duration ?? 0.0;

        if ($trimStart !== null && $trimEnd !== null) {
            $duration = $trimEnd - $trimStart;
        }

        $lastRender = $processedMedia->lastRender;

        return [
            'start' => (new Duration($start))->toMilliseconds()->getValue(),
            'end' => (new Duration($start + $duration))->toMilliseconds()->getValue(),
            'duration' => (new Duration($duration))->toMilliseconds()->getValue(),
            'url' => $lastRender->thumbnail_url ?? null,
            'render_media_id' => $lastRender->id ?? null,
            'effects' => [
                'fade_in_duration' => $this->getFadeInDuration($key, $fadeInDuration, $fadeBetweenDuration),
                'fade_out_duration' => $this->getFadeOutDuration(
                    $processedMedias,
                    $key,
                    $fadeOutDuration,
                    $fadeBetweenDuration
                ),
            ],
        ];
    }

    private function getFadeInDuration(int $key, int $fadeInDuration, int $fadeBetweenDuration): int
    {
        return $key === 0 ? $fadeInDuration : $fadeBetweenDuration / 2;
    }

    private function getFadeOutDuration(array $array, int $key, int $fadeOutDuration, int $fadeBetweenDuration): int
    {
        return count($array) - 1 === $key ? $fadeOutDuration : $fadeBetweenDuration / 2;
    }

    /**
     * @param ProcessedMedia[]
     *
     * @return ProcessedMedia[]
     */
    private function sortByStartAscending(array $processedMedias): array
    {
        usort(
            $processedMedias,
            fn(ProcessedMedia $media1, ProcessedMedia $media2) => $media1['data']['start'] <=> $media2['data']['start']
        );

        return $processedMedias;
    }
}
