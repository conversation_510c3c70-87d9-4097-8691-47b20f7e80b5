<?php

namespace App\Application\Providers;

use App\Domain\Render\RenderProject\Serializer\CompileSerializer;
use App\Domain\Render\RenderProject\Serializer\ConcatSerializer;
use App\Domain\Render\RenderProject\Serializer\FinalAudioSerializer;
use App\Domain\Render\RenderProject\Serializer\RenderChunkSerializer;
use App\Domain\Render\RenderProject\Serializer\TimelineSerializer;
use App\Domain\Render\RenderScreen\Serializer\ConcatScreenSerializer;
use App\Domain\RenderJob\JobStorageService;
use App\Domain\RenderJob\RenderJobRepository;
use App\Domain\RenderJob\RenderJobsSerializer;
use App\Domain\Transition\Serializer\TransitionSerializer;
use App\Infrastructure\RenderJob\EloquentRenderJobRepository;
use App\Infrastructure\RenderJob\JobGoogleStorageService;
use Illuminate\Foundation\Application;
use Illuminate\Support\ServiceProvider;

class RenderJobServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(RenderJobRepository::class, EloquentRenderJobRepository::class);
        $this->app->bind(JobStorageService::class, JobGoogleStorageService::class);

        $this->app->tag([
            CompileSerializer::class,
            ConcatSerializer::class,
            FinalAudioSerializer::class,
            RenderChunkSerializer::class,
            TimelineSerializer::class,
            ConcatScreenSerializer::class,
            TransitionSerializer::class,
        ], 'renderJobSerializers');

        $this->app->bind(RenderJobsSerializer::class, static function (Application $app) {
            return new RenderJobsSerializer($app->tagged('renderJobSerializers'));
        });
    }
}
