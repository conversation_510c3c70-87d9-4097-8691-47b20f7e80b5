<?php

declare(strict_types=1);

namespace App\Domain\CustomPage;

use App\Domain\CustomPage\Exception\UnauthorizedTeamPresetException;
use App\Domain\TeamPreset\TeamPresetRepository;
use App\Models\TeamPreset;

class TeamPresetValidator
{
    private TeamPresetRepository $teamPresetRepository;

    public function __construct(TeamPresetRepository $teamPresetRepository)
    {
        $this->teamPresetRepository = $teamPresetRepository;
    }

    public function validate(
        int $teamId,
        string $color,
        ?int $fontId,
        ?int $logoProcessedMediaId,
    ): void {
        $teamPreset = $this->teamPresetRepository->getByTeamId($teamId);
        $errors = [];

        if (!$this->isAuthorizedMainColor($teamPreset, $color)) {
            $errors[] = 'primary_color.not_in_team_presets';
        }

        if ($fontId !== null && !$this->isAuthorizedFont($teamPreset, $fontId)) {
            $errors[] = 'font_id.not_in_team_presets';
        }

        if ($logoProcessedMediaId !== null
            && !$this->isAuthorizedLogo($teamPreset, $logoProcessedMediaId)
        ) {
            $errors[] = 'main_logo_processed_media_id.not_in_team_presets';
        }

        if ($errors !== []) {
            throw new UnauthorizedTeamPresetException($errors);
        }
    }

    private function isAuthorizedMainColor(TeamPreset $teamPreset, string $color): bool
    {
        $teamPresetsHasNoMainColorsConstraints = ($teamPreset->colors['main']['values'] ?? []) === [];

        $teamPresetsContainGivenColor = in_array($color, $teamPreset->colors['main']['values'] ?? [], true);

        return $teamPresetsHasNoMainColorsConstraints || $teamPresetsContainGivenColor;
    }

    private function isAuthorizedFont(TeamPreset $teamPreset, int $fontId): bool
    {
        if (!in_array($fontId, $teamPreset->fonts->pluck('id')->toArray(), true)) {
            return false;
        }

        return true;
    }

    private function isAuthorizedLogo(TeamPreset $teamPreset, int $mainLogoProcessedMediaId): bool
    {
        if (!in_array(
            $mainLogoProcessedMediaId,
            data_get($teamPreset->logos, 'primary.logos.*.*.processed_media_id'),
            true
        )) {
            return false;
        }

        return true;
    }
}
