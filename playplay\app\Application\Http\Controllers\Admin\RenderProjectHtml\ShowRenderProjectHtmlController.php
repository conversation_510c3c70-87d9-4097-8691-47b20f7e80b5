<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\RenderProjectHtml;

use App\Application\Http\Controllers\Admin\AbstractRenderController;
use App\Domain\Common\Exceptions\EntityNotFoundException;
use App\Domain\CustomPage\CustomPageRepository;
use App\Domain\Render\RenderProject\RenderProjectHtmlRepository;
use App\Domain\ShareableLink\ShareableLinkRepository;
use App\Infrastructure\CustomPage\Serializers\CustomPageBackofficeSerializer;
use App\Models\Project;
use App\Models\Renders\RenderProjectHtml;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;

final class ShowRenderProjectHtmlController extends AbstractRenderController
{
    private Guard $guard;
    private RenderProjectHtmlRepository $renderProjectHtmlRepository;
    private ShareableLinkRepository $shareableLinkRepository;
    private CustomPageRepository $customPageRepository;
    private CustomPageBackofficeSerializer $customPageBackofficeSerializer;
    private Factory $viewFactory;

    public function __construct(
        Guard $guard,
        RenderProjectHtmlRepository $renderProjectHtmlRepository,
        ShareableLinkRepository $shareableLinkRepository,
        CustomPageRepository $customPageRepository,
        CustomPageBackofficeSerializer $customPageBackofficeSerializer,
        Factory $viewFactory,
    ) {
        $this->authorizeResource(RenderProjectHtml::class, RenderProjectHtml::class);
        $this->guard = $guard;
        $this->customPageRepository = $customPageRepository;
        $this->renderProjectHtmlRepository = $renderProjectHtmlRepository;
        $this->shareableLinkRepository = $shareableLinkRepository;
        $this->customPageBackofficeSerializer = $customPageBackofficeSerializer;
        $this->viewFactory = $viewFactory;
    }

    public function __invoke(RenderProjectHtml $renderProjectHtml): View
    {
        $this->authorize('show', $renderProjectHtml);
        $this->authorize('canAccessRestrictedData', $renderProjectHtml->project->company);

        $this->loadJobs($renderProjectHtml);

        $lastProcessedRender = $this->renderProjectHtmlRepository
            ->getLastProcessedRenderFromProject($renderProjectHtml->project);

        $sharelink = $this->shareableLinkRepository->getByRenderProjectHtmlId($renderProjectHtml->id);

        $serializedCustomPage = null;

        try {
            $customPage = $this->customPageRepository->getByProjectId($renderProjectHtml->project_id, true);
            $serializedCustomPage = $this->customPageBackofficeSerializer->serialize($customPage);
        } catch (EntityNotFoundException) {
            $customPage = null;
        }

        return $this->viewFactory->make(
            'admin.renders.show',
            [
                'render' => $renderProjectHtml,
                'project' => $renderProjectHtml->project,
                'motionVersion' => $renderProjectHtml->motion_config?->version,
                'motionUrl' => $renderProjectHtml->motion_config?->url,
                'lastProcessedRender' => $lastProcessedRender,
                'transitionsGroupedByOrder' => $renderProjectHtml->getTransitionJobs()->groupBy('data.order'),
                'privateModal' => $this->isPrivateVideoFromAnotherUser($renderProjectHtml->project),
                'customPage' => $serializedCustomPage,
                'sharelink' => $sharelink,
            ]
        );
    }

    private function isPrivateVideoFromAnotherUser(Project $project): bool
    {
        if (!$project->is_private) {
            return false;
        }

        return $this->guard->user()->id !== $project->user_id;
    }

    private function loadJobs(RenderProjectHtml $renderProjectHtml): void
    {
        $renderProjectHtml->load([
            'jobs',
            'errors',
            'jobs.errors',
            'renderScreens',
            'renderScreens.jobs',
            'renderScreens.jobs.errors',
            'renderScreens.errors',
            'renderScreens.errors.job',
        ]);
    }
}
