<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\Project;

use App\Domain\Project\ProjectFormat;
use App\Models\Template;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Factory;

abstract class AbstractProjectStoreFromRequest extends FormRequest
{
    protected Factory $validationFactory;

    public function __construct()
    {
        $this->validationFactory = app(Factory::class);
        $this->checkIfUserHasPreset($this->validationFactory);
        $this->checkFormatIsAvailable($this->validationFactory);
        $this->checkIsValidFormat($this->validationFactory);
        parent::__construct();
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    public function messages(): array
    {
        return [
            // Name validator messages
            'preset_id.user_has_preset' => 'User has not access to this preset',
            'preset_id.required' => 'Preset ID is mandatory',
            'preset_id.exists' => 'Preset ID does not exist',
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    abstract public function rules(): array;

    protected function checkFormatIsAvailable(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'formatAvailableForUser',
            function ($attribute, $value) {
                return $this->isFormatAvailableForUser($value);
            }
        );
    }

    protected function checkIfUserHasPreset(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'presetAvailableForUser',
            function ($attribute, $value) {
                return auth()->user()->canSeePreset($value);
            }
        );
    }

    protected function checkIsValidFormat(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'isValidFormat',
            function ($attribute, $value) {
                $templateId = request()->get('template_id');
                if ($templateId === null) {
                    return true;
                }

                $value = $value === ProjectFormat::STORY ? ProjectFormat::VERTICAL : $value;

                return Template::join('template_projects', 'template_projects.template_id', 'templates.id')
                    ->where('templates.id', $templateId)
                    ->where('template_projects.format', '=', $value)
                    ->select('template_projects.project_id')
                    ->exists();
            }
        );
    }

    abstract protected function isFormatAvailableForUser(string $format): bool;
}
