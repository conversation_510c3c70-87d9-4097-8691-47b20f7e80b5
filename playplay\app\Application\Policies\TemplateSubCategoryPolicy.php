<?php

declare(strict_types=1);

namespace App\Application\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

final class TemplateSubCategoryPolicy extends DefaultPolicy
{
    use HandlesAuthorization;

    public function before(User $authUser, $ability, $param = null)
    {
        if ($authUser->can('manage-subCategories')) {
            return true;
        }
    }
}
