<?php

declare(strict_types=1);

namespace App\Domain\Feature;

use App\Domain\Plan\PlanCollection;
use App\Models\FeatureGroup;
use Illuminate\Support\Collection;

class FeatureGroupViewModelCollectionFactory
{
    /**
     * @param FeatureGroup[]|Collection $featureGroups
     */
    public function createFeatureGroupViewModelCollection(
        HasFeatures $baseModel,
        Collection $featureGroups,
        PlanCollection $plans
    ): FeatureGroupViewModelCollection {
        $featureGroupVMCollection = new FeatureGroupViewModelCollection();

        foreach ($featureGroups as $featureGroup) {
            $featureVMsOfGroup = [];
            foreach ($featureGroup->features as $feature) {
                $featureVMsOfGroup[] = new FeatureViewModel(
                    $plans,
                    $feature,
                    $baseModel->getValueOfFeature($feature->name)
                );
            }

            $featureGroupVM = new FeatureGroupViewModel($featureGroup->name, $featureVMsOfGroup);

            $featureGroupVMCollection->push($featureGroupVM);
        }

        return $featureGroupVMCollection;
    }
}
