<?php

declare(strict_types=1);

namespace App\Application\Mail;

use App\Models\Project;
use App\Models\RenderError;
use App\Models\Renders\ARender;
use App\Models\Renders\RenderProjectHtml;
use App\Models\Renders\RenderScreenHtml;
use App\Models\Renders\RenderStory;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use InvalidArgumentException;

final class RenderCanceled extends Mailable
{
    use Queueable, SerializesModels;

    private iterable $renders;
    private RenderError $error;
    private Project $project;

    public function __construct(RenderError $error)
    {
        $this->error = $error;
        switch (true) {
            case $error->parent instanceof RenderScreenHtml:
                $this->renders = $error->parent->renderProjects->where('status', '=', ARender::STATUS_CANCELED);
                $this->project = $this->renders->first()->project;
                break;
            case $error->parent instanceof RenderStory:
                $this->renders = [$error->parent->renderProject];
                $this->project = $error->parent->renderProject->project;
                break;
            case $error->parent instanceof RenderProjectHtml:
                $this->renders = [$error->parent];
                $this->project = $this->renders[0]->project;
                break;
            default:
                throw new InvalidArgumentException('Missing render error template');
        }

        $this->subject("Project #{$this->project->id} in error {$error->type}");
    }

    public function build(): self
    {
        return $this->view('emails.render-canceled', [
            'project' => $this->project,
            'error' => $this->error,
            'renderProjects' => $this->renders,
        ]);
    }
}
