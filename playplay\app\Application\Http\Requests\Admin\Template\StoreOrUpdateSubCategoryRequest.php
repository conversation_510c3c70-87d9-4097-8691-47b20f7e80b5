<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\Template;

use Illuminate\Foundation\Http\FormRequest;

class StoreOrUpdateSubCategoryRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'max:100'],
            'backoffice_name' => ['required', 'max:100'],
            'categories_ids' => ['exists:categories,id'],
            'templates_ids' => ['exists:templates,id'],
        ];
    }

    public function authorize(): bool
    {
        return true;
    }
}
