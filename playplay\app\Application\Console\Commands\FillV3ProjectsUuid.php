<?php

declare(strict_types=1);

namespace App\Application\Console\Commands;

use App\Models\Project;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Throwable;

/**
 * @deprecated Don't remove it ! It'll be used to update V2 projects
 */
final class FillV3ProjectsUuid extends Command
{
    private const FIRST_PROJECT_WITH_VALID_UUID = 1128362;
    private const NB_MAX_OF_PROJECTS = 15000;
    private const CHUNK = 1000;

    protected $signature = 'db:fill-projects-uuid {--offset= : First project id.} 
                                                  {--nb_max= : Max number of projects to update.}
                                                  {--chunk= : chunk 🤷🏽‍♂..️}';
    protected $description = 'Command to fill V3 project uuid';

    public function handle(): int
    {
        $offset = $this->option('offset') !== null
            ? (int) $this->option('offset')
            : (
                Project::query()
                    ->withTrashed()
                    ->whereNotNull('uuid')
                    ->where('version', 3)
                    ->where('id', '<', self::FIRST_PROJECT_WITH_VALID_UUID)
                    ->max('id')
                ?? Project::query()
                    ->withTrashed()
                    ->where('version', 3)
                    ->where('id', '<', self::FIRST_PROJECT_WITH_VALID_UUID)
                    ->where('created_at', '<', '2022-06-24')
                    ->min('id')
            );

        $nbMaxOfProjects = $this->option('nb_max') !== null
            ? (int) $this->option('nb_max')
            : self::NB_MAX_OF_PROJECTS;

        $chunk = $this->option('chunk') !== null
            ? (int) $this->option('chunk')
            : self::CHUNK;

        try {
            $this->info("Filling {$nbMaxOfProjects} projects uuid from {$offset}");

            $projectsToUpdate = $this->getProjectsToUpdate($offset, $nbMaxOfProjects);

            if ($projectsToUpdate->isEmpty()) {
                $this->info('There is no projects to update.');

                return 0;
            }

            $bar = $this->output->createProgressBar($projectsToUpdate->count());

            foreach ($projectsToUpdate->chunk($chunk) as $projects) {
                $projects->each(
                    function (Project $project) use ($bar) {

                        if ($project->uuid === null) {
                            $uuid = Str::uuid();
                            $this->info("Creating uuid for project : {$project->id} with uuid {$uuid}");
                            DB::table('projects')->where('id', $project->id)->update(['uuid' => $uuid]);
                        } else {
                            $this->info("Uuid already filled for project : {$project->id}");
                        }

                        $bar->advance();
                    }
                );
            }

            $bar->advance();
        } catch (Throwable $e) {
            $this->error("Something went wrong. {$e->getMessage()}");

            return 1;
        } finally {
            if (isset($bar)) {
                $bar->finish();
            }
        }

        return 0;
    }

    private function getProjectsToUpdate(int $offset, int $nbMaxOfProjects): Collection
    {
        return Project::query()
            ->where('id', '>=', $offset)
            ->where('version', 3)
            ->where('id', '<', self::FIRST_PROJECT_WITH_VALID_UUID)
            ->whereNull('uuid')
            ->withTrashed()
            ->limit($nbMaxOfProjects)
            ->get();
    }
}
