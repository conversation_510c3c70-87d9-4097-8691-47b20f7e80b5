<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Project;

use App\Application\Http\Controllers\Api\V2\Project\GetProjectFolderListController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class ProjectFolderRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::get('project-folders', GetProjectFolderListController::class)->name('project-folders.get-list');
    }
}
