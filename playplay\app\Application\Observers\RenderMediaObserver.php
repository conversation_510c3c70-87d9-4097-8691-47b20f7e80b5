<?php

namespace App\Application\Observers;

use App\Application\Events\Renders\RenderMediaCanceled;
use App\Application\Events\Renders\RenderMediaProcessed;
use App\Models\Renders\RenderMedia;
use DateTimeImmutable;
use Illuminate\Contracts\Events\Dispatcher as DispatcherContract;

class RenderMediaObserver
{
    private DispatcherContract $dispatcher;

    public function __construct(DispatcherContract $dispatcher)
    {
        $this->dispatcher = $dispatcher;
    }

    public function updated(RenderMedia $renderMedia): void
    {
        if ($renderMedia->isDirty('status')) {
            $this->setRenderedAtIfMediaIsProcessed($renderMedia);
            $this->dispatchEvents($renderMedia);
        }
    }

    public function updating(RenderMedia $renderMedia): void
    {
        $this->setRenderedAtIfMediaIsProcessed($renderMedia);
    }

    private function dispatchEvents(RenderMedia $renderMedia): void
    {
        if (!$renderMedia->isParentProcessedMedia() && !$this->hasPendingSnapshot($renderMedia)) {
            return;
        }

        if ($renderMedia->isProcessed()) {
            $this->dispatcher->dispatch(new RenderMediaProcessed($renderMedia));
        }

        if ($renderMedia->isCanceled()) {
            $this->dispatcher->dispatch(new RenderMediaCanceled($renderMedia));
        }
    }

    private function hasPendingSnapshot(RenderMedia $renderMedia): bool
    {
        return $renderMedia->snapshots()->isNotFinished()->count() > 0;
    }

    private function setRenderedAtIfMediaIsProcessed(RenderMedia $renderMedia): void
    {
        if ($renderMedia->rendered_at === null && $renderMedia->isProcessed()) {
            $renderMedia->rendered_at = new DateTimeImmutable();
        }
    }
}
