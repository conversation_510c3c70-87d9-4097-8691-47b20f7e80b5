<?php

namespace App\Application\Http\Requests\Api\Project\Media;

use Illuminate\Foundation\Http\FormRequest;

class ProcessedMediaSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'raw_media_id' => ['required', 'numeric', 'exists:deprecated_raw_medias,id'],
        ];
    }
}
