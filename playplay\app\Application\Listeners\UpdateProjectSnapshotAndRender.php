<?php

declare(strict_types=1);

namespace App\Application\Listeners;

use App\Application\Events\ProjectSilentlyUpdated;
use Illuminate\Support\Facades\DB;

final class UpdateProjectSnapshotAndRender
{
    public function handle(ProjectSilentlyUpdated $event): void
    {
        DB::table('projects')
            ->where('id', '=', $event->getProjectId())
            ->update([
                'render_up_to_date' => false,
                'snapshot_up_to_date' => false,
            ]);
    }
}
