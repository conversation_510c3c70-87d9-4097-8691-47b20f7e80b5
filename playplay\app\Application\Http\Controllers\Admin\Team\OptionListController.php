<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Team;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Domain\Screen\Parameters\OptionListCategoryRepository;
use App\Models\Screen\Parameters\OptionList;
use App\Models\Team;
use Illuminate\Contracts\Cache\Repository as CacheRepository;
use Illuminate\Contracts\View\View;

final class OptionListController extends BaseController
{
    private const TAB_OPTION_LISTS = 'option-lists';
    private OptionListCategoryRepository $optionListCategoryRepository;
    private CacheRepository $cacheRepository;

    public function __construct(
        OptionListCategoryRepository $optionListCategoryRepository,
        CacheRepository $cacheRepository
    ) {
        $this->optionListCategoryRepository = $optionListCategoryRepository;
        $this->cacheRepository = $cacheRepository;
    }

    public function index(Team $team): View
    {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('view', $team);

        return view('admin.teams.option-lists.index', [
            'tabActive' => self::TAB_OPTION_LISTS,
            'categories' => $this->optionListCategoryRepository->getAllFor($team),
            'team' => $team,
        ]);
    }

    public function link(Team $team, OptionList $optionList): void
    {
        $this->authorize('canAccessRestrictedData', $team->company);

        $team->options()->syncWithoutDetaching($optionList->options->pluck('id')->toArray());

        $this->cacheRepository->tags(['api', 'screens'])->forget(route('api.v2.team.screens.index', $team->id));
    }

    public function unlink(Team $team, OptionList $optionList): void
    {
        $this->authorize('canAccessRestrictedData', $team->company);

        $team->options()->detach($optionList->options->pluck('id')->toArray());

        $this->cacheRepository->tags(['api', 'screens'])->forget(route('api.v2.team.screens.index', $team->id));
    }
}
