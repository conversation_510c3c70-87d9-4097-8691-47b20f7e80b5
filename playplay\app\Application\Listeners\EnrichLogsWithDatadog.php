<?php

namespace App\Application\Listeners;

use Monolog\Handler\StreamHandler;
use Monolog\Formatter\JsonFormatter;
use Illuminate\Support\Collection;

final class EnrichLogsWithDatadog
{

    public function __invoke($logger): void
    {
        (new Collection($logger->getHandlers()))->each(function (StreamHandler $handler) {
            // Faculatif : utiliser une mise en forme JSON
            $useJson = $handler->getFormatter() instanceof JsonFormatter;

            $handler->pushProcessor(function (array $record) use ($useJson) {
                if (!$this->isActiveSpan()) {
                    return $record;
                }

                if ($useJson === true) {
                    return $this->enrichJsonLog($record);
                }

                return $this->enrichTextLog($record);
            });
        });
    }

    /**
     * Enrich Json log with Datadog APM trace and span ID
     */
    private function enrichJsonLog(array $record): array
    {
        $record['dd'] = [
            'trace_id' => $this->getDatadogTraceId(),
            'span_id'  => $this->getDatadogSpanId(),
        ];

        return $record;
    }

    /**
     * Enrich Text log with Datadog APM trace and span ID
     */
    private function enrichTextLog(array $record): array
    {
        $record['message'] .= (
            ' [dd.trace_id='
            . $this->getDatadogTraceId()
            . ' dd.span_id='
            . $this->getDatadogSpanId() .']'
        );

        return $record;
    }

    /**
     * Know if we have an active span (if APM is configured on this environment)
     */
    private function isActiveSpan(): bool
    {
        if (!class_exists("\DDTrace\GlobalTracer")) {
            return false;
        }

        return \DDTrace\GlobalTracer::get()->getActiveSpan() !== null;
    }

    /**
     * Get the ID of the active TRACE on APM
     *
     * @return string
     */
    private function getDatadogTraceId(): string
    {
        if (!class_exists("\DDTrace\GlobalTracer")) {
            return "";
        }

        $span = \DDTrace\GlobalTracer::get()->getActiveSpan();

        return $span?->getTraceId();
    }

    /**
     * Get the ID of the active SPAN on APM
     */
    private function getDatadogSpanId(): string
    {
        if (!class_exists('\DDTrace\GlobalTracer')) {
            return '';
        }

        /**
         * @phpstan-ignore-next-line
         *
         * It seems this function is available at runtime but cannot be imported from anywhere
         *
         */
        return \dd_trace_peek_span_id();
    }
}
