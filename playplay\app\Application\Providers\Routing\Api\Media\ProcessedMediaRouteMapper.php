<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Media;

use App\Application\Http\Controllers\Api\V2\Media\ProcessedMediaAudioController;
use App\Application\Http\Controllers\Api\V2\Media\ProcessedMediaController;
use App\Application\Http\Controllers\Api\V2\ProcessedMedia\CreateGifTransformationsController;
use App\Application\Http\Controllers\Api\V2\ProcessedMedia\CreateImageBlurController;
use App\Application\Http\Controllers\Api\V2\ProcessedMedia\CreateImageCropController;
use App\Application\Http\Controllers\Api\V2\ProcessedMedia\CreateImageResizeController;
use App\Application\Http\Controllers\Api\V2\ProcessedMedia\UpdateGifTransformationsController;
use App\Application\Http\Controllers\Api\V2\ProcessedMedia\UpdateImageBlurController;
use App\Application\Http\Controllers\Api\V2\ProcessedMedia\UpdateImageCropController;
use App\Application\Http\Controllers\Api\V2\ProcessedMedia\VideoController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class ProcessedMediaRouteMapper implements RouteMapper
{
    public function map(): void
    {
        $this->mapProcessedMediasRoutesToRefacto();
        $this->mapProcessedMediasVideoRoutes();
        $this->mapProcessedMediasGifRoutes();
        $this->mapProcessedMediasAudioRoutes();
    }

    /**
     * TODO rename processed.medias. alias to processed-medias.
     */
    private function mapProcessedMediasRoutesToRefacto(): void
    {
        Route::group([
            'prefix' => '/processed-medias',
            'as' => 'processed.medias.',
        ], static function (Router $router) {
            $router
                ->post('/outro', [ProcessedMediaController::class, 'storeProcessedMediaOutro'])
                ->name('outro.store');
            $router->post('/image/blur', CreateImageBlurController::class)->name('image.blur.store');
            $router->post('/image/crop', CreateImageCropController::class)->name('image.crop.store');
            $router->post('/image/resize', CreateImageResizeController::class)->name('image.resize.store');
            $router
                ->post('/audio/resize', [ProcessedMediaController::class, 'storeParamProcessedMediaAudio'])
                ->name('audio_param.store');
            $router->put('/{processedMedia}/image/blur', UpdateImageBlurController::class)
                ->name('image.blur.update');
            $router->put('/{processedMedia}/image/crop', UpdateImageCropController::class)
                ->name('image.crop.update');
            $router
                ->put(
                    '/{processedMedia}/audio/resize',
                    [ProcessedMediaController::class, 'updateParamProcessedMediaAudioTrim']
                )
                ->name('audio_param.update');
            $router
                ->get('/{processedMedia}', [ProcessedMediaController::class, 'show'])
                ->name('show');
        });
    }

    private function mapProcessedMediasVideoRoutes(): void
    {
        Route::group([
            'prefix' => '/processed-medias',
            'as' => 'processed-medias.video.',
        ], static function (Router $router) {
            $router
                ->post('/video/transformations', [VideoController::class, 'storeTransformations'])
                ->name('transformations.store');
            $router
                ->put('/{processedMedia}/video/transformations', [VideoController::class, 'updateTransformations'])
                ->name('transformations.update');
        });
    }

    private function mapProcessedMediasGifRoutes(): void
    {
        Route::group([
            'prefix' => '/processed-medias',
            'as' => 'processed-medias.gif.',
        ], static function (Router $router) {
            $router
                ->post('/gif/transformations', CreateGifTransformationsController::class)
                ->name('transformations.store');
            $router
                ->put('/{processedMedia}/gif/transformations', UpdateGifTransformationsController::class)
                ->name('transformations.update');
        });
    }

    /**
     * Must be declared after all ProcessedMedias routes due to conflic in route url (/relationTypeKey)
     * TODO refacto routes paths (prefix /audio ?)
     */
    private function mapProcessedMediasAudioRoutes(): void
    {
        Route::group([
            'prefix' => '/processed-medias/{relationTypeKey}',
            'as' => 'processed-medias.audio.',
        ], static function (Router $router) {
            $router
                ->post('/', [ProcessedMediaAudioController::class, 'store'])
                ->name('store');
            $router
                ->post('/trim/{processedMedia}', [ProcessedMediaAudioController::class, 'trim'])
                ->name('trim');
            $router
                ->post('/split/{processedMedia}', [ProcessedMediaAudioController::class, 'split'])
                ->name('split');
            $router
                ->delete('/{processedMedia}', [ProcessedMediaAudioController::class, 'destroy'])
                ->name('delete');
        });
    }
}
