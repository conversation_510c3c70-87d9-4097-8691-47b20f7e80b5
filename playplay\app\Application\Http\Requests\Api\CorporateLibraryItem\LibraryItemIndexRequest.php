<?php

namespace App\Application\Http\Requests\Api\CorporateLibraryItem;

use App\Models\CorporateLibraryItem;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Factory;

class LibraryItemIndexRequest extends FormRequest
{
    public static function rules(): array
    {
        return [
            'page' => ['sometimes', 'integer', 'min:1'],
            'per_page' => ['sometimes', 'integer', 'min:1', 'max:100'],
            'folder_id' => ['sometimes', 'is_folder'],
            'order_by' => ['sometimes', 'in:name,type'],
            'order_direction' => ['sometimes', 'in:DESC,ASC'],
        ];
    }

    public function __construct()
    {
        $validationFactory = app(Factory::class);
        $this->checkIsFolder($validationFactory);
        parent::__construct();
    }

    public function authorize(): bool
    {
        return true;
    }

    public function checkIsFolder(Factory $validationFactory): void
    {
        $validationFactory->extendImplicit(
            'is_folder',
            function () {
                return CorporateLibraryItem::where('is_folder', true)
                    ->where('company_id', auth()->user()->company_id)
                    ->exists();
            }
        );
    }
}
