<?php

namespace App\Application\Policies;

use App\Models\Media;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

/**
 * @deprecated This is v1 legacy, media are replaced by RawMedia and ProcessedMedia.
 */
class MediaPolicy extends DefaultPolicy
{
    use HandlesAuthorization;

    public function before(User $authUser, $ability, $param = null)
    {
        if ($authUser->can('manage-media')) {
            return true;
        }
    }

    public function view(User $authUser, Media $media): bool
    {
        return $media->company_id === $authUser->company_id;
    }
}
