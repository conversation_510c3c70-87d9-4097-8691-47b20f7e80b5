<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Admin;

use App\Application\Http\Controllers\Admin\MaintenanceController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class MaintenanceRouteMapper implements RouteMapper
{

    public function map(): void
    {
        Route::group([
            'prefix' => 'maintenances',
            'as' => 'maintenances.'
        ], static function (Router $router) {
            $router->get('/', [MaintenanceController::class, 'index'])->name('index');
            $router->post('/', [MaintenanceController::class, 'store'])->name('store');
            $router->get('/create', [MaintenanceController::class, 'create'])->name('create');
            $router->put('/{maintenance}', [MaintenanceController::class, 'update'])->name('update');
            $router->delete('/{maintenance}', [MaintenanceController::class, 'destroy'])->name('destroy');
            $router->get('/{maintenance}/edit', [MaintenanceController::class, 'edit'])->name('edit');
            $router->get('{maintenance}/danger-zone', [MaintenanceController::class, 'showDangerZone'])
                ->name('danger-zone');
        });
    }
}
