<?php

declare(strict_types=1);

namespace App\Application\Console\Commands;

use App\Models\Company;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Psr\Log\LoggerInterface;

final class CheckForExpiredSubscriptions extends Command
{
    private LoggerInterface $logger;

    public function __construct(
        LoggerInterface $logger,
    ) {
        parent::__construct();
        $this->logger = $logger;
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:subscriptions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check companies for expired subscriptions, and make them inactive';

    public function handle(): int
    {
        // TODO move to a repository
        $updated = Company::whereStatus('active')
            ->where('client_until', '<', Carbon::today())
            ->update([
                'status' => 'inactive',
            ]);

        $message = $updated . ' prospects have been deactivated.';
        $this->comment($message);

        $this->logger->info($message);

        return 0;
    }
}
