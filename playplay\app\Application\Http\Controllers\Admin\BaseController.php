<?php

namespace App\Application\Http\Controllers\Admin;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller as LaravelController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;

abstract class BaseController extends LaravelController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    protected function ajaxDelete(Model $model, string $redirectUrl = '')
    {
        if ($model->delete()) {
            $responseBody = ['success' => true];
            if ($redirectUrl !== '') {
                $responseBody += ['redirect' => $redirectUrl];
            }

            return new JsonResponse($responseBody);
        }

        throw new HttpException(Response::HTTP_INTERNAL_SERVER_ERROR, 'Not deleted!');
    }

    protected function resourceAbilityMap(): array
    {
        return [
            'index' => 'list',
            'show' => 'view',
            'create' => 'create',
            'store' => 'create',
            'edit' => 'update',
            'update' => 'update',
            'reorder' => 'update',
            'destroy' => 'delete',
            'showDangerZone' => 'delete',
        ];
    }

    protected function resourceMethodsWithoutModels(): array
    {
        return ['index', 'create', 'store', 'reorder'];
    }
}
