<?php

namespace App\Application\Http\Requests\Api\Project\ProjectScreen\Layout;

use Illuminate\Foundation\Http\FormRequest;

class ProjectScreenLayoutRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nth_layout' => ['required', 'integer', 'min:0', 'max:255'],
        ];
    }
}
