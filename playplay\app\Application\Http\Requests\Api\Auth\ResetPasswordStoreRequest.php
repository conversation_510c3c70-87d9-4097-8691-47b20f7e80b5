<?php

namespace App\Application\Http\Requests\Api\Auth;

use App\Application\Http\Controllers\Api\V2\Auth\ResetPasswordController;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Factory;

class ResetPasswordStoreRequest extends FormRequest
{
    public static function getRules()
    {
        return [
            'token' => 'required',
            'email' => ['required', 'email', 'exists:users,email'],
            'type' => [
                'required',
                'in:' . ResetPasswordController::TYPE_CREATE . ',' . ResetPasswordController::TYPE_RESET,
            ],
            'password' => ['required', 'confirmed', 'check_password_rule'],
            'terms' => ['required_if:type,' . ResetPasswordController::TYPE_CREATE, 'isTrueIfTypeIsCreate'],
        ];
    }

    public function __construct()
    {
        $validationFactory = app(Factory::class);
        $this->checkIfTermsAreAccepted($validationFactory);

        parent::__construct();
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function checkIfTermsAreAccepted(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'isTrueIfTypeIsCreate',
            function ($attribute, $value, $parameters, $validator) {
                if ($validator->getData()['type'] === ResetPasswordController::TYPE_CREATE) {
                    return $value;
                }

                return true;
            }
        );
    }

    public function isCreation(): bool
    {
        return $this->input('type') === ResetPasswordController::TYPE_CREATE;
    }

    public function rules()
    {
        return static::getRules();
    }
}
