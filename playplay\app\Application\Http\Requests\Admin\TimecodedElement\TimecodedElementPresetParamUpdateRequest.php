<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\TimecodedElement;

use App\Models\TimecodedElement\TimecodedElementPresetParam;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Routing\Route;
use Illuminate\Validation\Rule;

final class TimecodedElementPresetParamUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        /** @var Route $route */
        $route = $this->route();
        /** @var TimecodedElementPresetParam $timecodedElementPresetParam */
        $timecodedElementPresetParam = $route->parameter('timecodedElementPresetParam');

        return [
            'name' => ['required', 'string'],
            'backoffice_name' => ['required', 'string'],
            'animaniac_ref' => ['required', 'string'],
            'default_value' => [
                Rule::requiredIf(
                    static fn() => $timecodedElementPresetParam->type
                        === TimecodedElementPresetParam::TYPE_TEXTAREA
                ),
                'string',
            ],
        ];
    }
}
