<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\User;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\User\CheckUserEmailExistenceRequest;
use Symfony\Component\HttpFoundation\Response;

final class CheckUserEmailExistenceController extends BaseController
{
    public function __invoke(CheckUserEmailExistenceRequest $request): Response
    {
        return new Response('', Response::HTTP_NO_CONTENT);
    }
}
