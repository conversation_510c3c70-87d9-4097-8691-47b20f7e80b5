<?php

namespace App\Application\Rules;

use Illuminate\Contracts\Translation\Translator;
use Illuminate\Contracts\Validation\Rule;

class HexColorWithoutSharpRule implements Rule
{
    private Translator $translator;

    public function __construct(Translator $translator)
    {
        $this->translator = $translator;
    }

    public function message(): string
    {
        return $this->translator->get('validation.hex-color');
    }

    public function passes($attribute, $value): bool
    {
        return is_string($value) && preg_match('/^[a-fA-F0-9]{6}$/m', $value) === 1;
    }
}
