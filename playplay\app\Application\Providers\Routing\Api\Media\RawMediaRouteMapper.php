<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Media;

use App\Application\Http\Controllers\Api\V2\Media\Estimation\ProcessingDurationEstimateController;
use App\Application\Http\Controllers\Api\V2\Media\RawMediaController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class RawMediaRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/raw-medias',
            'as' => 'raw-medias.'
        ], static function (Router $router) {
            $router
                ->get('/upload-url/{relationTypeKey?}', [RawMediaController::class, 'uploadUrl'])
                ->name('upload-url');
            $router
                ->post('/set-all-reader-from-url', [RawMediaController::class, 'setAllReaderFromUrl'])
                ->name('set-all-reader-from-url');
            $router
                ->post('/stock/{relationTypeKey?}', [RawMediaController::class, 'storeOrUpdateFromStock'])
                ->name('stock.store');
            $router
                ->post('{relationTypeKey?}', [RawMediaController::class, 'store'])
                ->name('store');
            $router
                ->delete('/{rawMedia}/{relationTypeKey?}', [RawMediaController::class, 'destroy'])
                ->name('destroy');
            $router
                ->get('/', [RawMediaController::class, 'index'])
                ->name('index');
            $router
                ->get('/processing-estimate', ProcessingDurationEstimateController::class)
                ->name('processing-estimate');
            $router
                ->get('/{rawMedia}', [RawMediaController::class, 'show'])
                ->name('show');
        });
    }
}
