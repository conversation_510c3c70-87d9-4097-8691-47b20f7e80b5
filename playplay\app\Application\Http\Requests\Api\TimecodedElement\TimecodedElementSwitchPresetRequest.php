<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\TimecodedElement;

use Illuminate\Foundation\Http\FormRequest;

final class TimecodedElementSwitchPresetRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'timecoded_element_preset_id' => ['required', 'integer']
        ];
    }
}
