<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\User;

use App\Application\Http\Controllers\Api\V2\User\UserController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class UserPublicRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::get('/users/me/country-code', [UserController::class, 'meCountryCode'])->name('users.me.country-code');
    }
}
