<?php

namespace App\Application\Http\Controllers\Api\V2\Template;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Template\TeamTemplateStoreRequest;
use App\Application\Http\Requests\Api\Template\TeamTemplateUpdateRequest;
use App\Domain\Template\Exceptions\CannotCreateTemplateFromStoryFormatProjectException;
use App\Domain\Template\Services\TeamTemplate as TeamTemplateService;
use App\Domain\User\Action\UserAction;
use App\Domain\User\Action\UserActionService;
use App\Models\Project;
use App\Models\TeamTemplate;
use App\Models\Template;
use App\Models\User;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class TeamTemplateController extends BaseController
{
    use AuthorizesRequests;

    private TeamTemplateService $teamTemplateService;
    private Guard $guard;
    private UserActionService $userActionService;

    public function __construct(
        Guard $guard,
        UserActionService $userActionService,
        TeamTemplateService $teamTemplateService
    ) {
        $this->userActionService = $userActionService;
        $this->teamTemplateService = $teamTemplateService;
        $this->guard = $guard;
    }

    public function destroy(Template $template): JsonResponse
    {
        if ($template->is_team_template) {
            $company = $template->teams()->first()->company;
            $this->authorize('canAccessRestrictedData', $company);
        }

        $this->authorize('destroy', $template);

        $project = $template->projects()->first();

        $this->teamTemplateService->deleteTemplateForUser($template, $this->getCurrentUser());

        $this->userActionService->addUserAction(
            new UserAction(
                'team_template_deleted',
                [],
                $project->team_id,
                $project->id,
            )
        );

        return $this->sendJsonResponse(new Collection([]), Response::HTTP_NO_CONTENT);
    }

    public function store(TeamTemplateStoreRequest $request): JsonResponse
    {
        /** @var Project $project */
        $project = Project::findOrFail($request->input('project_id'));

        $this->authorize('create', TeamTemplate::class);
        $this->authorize('duplicate', $project);

        $templateName = $request->input('template_name');

        $teamIds = $request->input('teams');

        try {
            $user = $this->getCurrentUser();
            $template = $this->teamTemplateService->createFromProjectForTeams($templateName, $project, $teamIds, $user);
        } catch (CannotCreateTemplateFromStoryFormatProjectException $e) {
            throw new BadRequestHttpException("You cannot create a template from a project in story format");
        }

        $this->userActionService->addUserAction(
            new UserAction(
                'team_template_created',
                [],
                $project->team_id,
                $project->id,
            )
        );

        return $this->sendJsonResponse(new Collection([$template]), Response::HTTP_CREATED);
    }

    public function update(Template $template, TeamTemplateUpdateRequest $request): JsonResponse
    {
        if ($template->is_team_template) {
            $company = $template->teams()->first()->company;
            $this->authorize('canAccessRestrictedData', $company);
        }

        $this->authorize('update', $template);

        $this->teamTemplateService->updateTemplateName($template, $request->input('template_name'));

        $this->teamTemplateService->updateTemplateTeams($template, $request->input('teams'), $this->getCurrentUser());

        $project = $template->projects()->first();

        $this->userActionService->addUserAction(
            new UserAction(
                'team_template_updated',
                [],
                $project->team_id,
                $project->id,
            )
        );

        return $this->sendJsonResponse(new Collection([$template->load('teams')]), Response::HTTP_OK);
    }

    private function getCurrentUser(): User
    {
        /** @var User $user */
        $user = $this->guard->user();

        return $user;
    }
}
