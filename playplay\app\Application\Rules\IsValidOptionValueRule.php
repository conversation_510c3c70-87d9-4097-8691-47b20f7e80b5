<?php

declare(strict_types=1);

namespace App\Application\Rules;

use App\Domain\Project\ProjectFormat;
use Illuminate\Contracts\Validation\Rule;

class IsValidOptionValueRule implements Rule
{
    private const ALLOWED_FILE_TYPES = ['jpg', 'jpeg', 'png', 'mp4'];

    /**
     * @inheritDoc
     * @note This message is not used. So we don't need to fill it.
     */
    public function message(): string
    {
        return '';
    }

    public function passes($attribute, $value): bool
    {
        if ($value === '') {
            return true;
        }

        if (is_array($value)) {
            if ($value === []) {
                return true;
            }

            if (!$this->hasAllFormats($value)) {
                return false;
            }

            foreach ($value as $valueByFormat) {
                if (strlen($valueByFormat) > 255) {
                    return false;
                }

                if ($valueByFormat !== '' && !$this->isExtensionValid($valueByFormat)) {
                    return false;
                }
            }

            return true;
        }

        return strlen($value) <= 255;
    }

    private function hasAllFormats(array $value): bool
    {
        return count(array_intersect(ProjectFormat::FORMATS, array_keys($value))) === 3;
    }

    private function isExtensionValid(string $value): bool
    {
        $extension = pathinfo($value, PATHINFO_EXTENSION);

        return in_array(strtolower($extension), self::ALLOWED_FILE_TYPES, true);
    }
}
