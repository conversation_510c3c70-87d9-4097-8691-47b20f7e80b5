<?php

namespace App\Application\Rules;

use App\Models\Company;
use App\Models\User;
use Illuminate\Contracts\Validation\Rule;

class UniqueActiveRule implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('validation.unique_user_active');
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed  $value
     *
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $user = User::whereEmail($value)->first();
        $company = optional($user)->company;

        return !($company && $company->status === Company::STATUS_ACTIVE);
    }
}
