<?php

declare(strict_types=1);

namespace App\Application\Listeners;

use App\Models\User;
use DateTimeImmutable;
use Illuminate\Auth\Events\Login;

final class UserLoggedAt
{
    public function handle(Login $event): void
    {
        /** @var User $user */
        $user = $event->user;
        $now = new DateTimeImmutable();
        if (!$user->last_logged_at && !$user->first_logged_at) {
            $user->first_logged_at = $now;
        }

        $user->last_logged_at = $now;

        $user->save();
    }
}
