<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\TransferMedia\CreateRawMediaController;
use App\Application\Http\Controllers\Api\TransferMedia\GetCdnUrlOrRawMediaOfMediaToBeTransferredController;
use App\Application\Http\Controllers\Api\TransferMedia\GetProjectInfosController;
use App\Application\Http\Controllers\Api\TransferMedia\GetUploadedFilesFromTransferMediaForProjectController;
use App\Application\Http\Controllers\Api\TransferMedia\SendUploadedFilesNotificationController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class TransferMediaPublicRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/transfer-media',
            'as' => 'transfer-media.',
        ], static function (Router $router) {
            $router->get('/upload-url-or-raw-media', GetCdnUrlOrRawMediaOfMediaToBeTransferredController::class)
                ->name('upload-url-or-raw-media');
            $router->post('/create-raw-media', CreateRawMediaController::class)
                ->name('create-raw-media');
            $router->get('/uploaded-files', GetUploadedFilesFromTransferMediaForProjectController::class)
                ->name('uploaded-files');
            $router->get('/project-infos', GetProjectInfosController::class)
                ->name('project-infos');
            $router->post('/send-uploaded-files-notification', SendUploadedFilesNotificationController::class)
                ->name('send-uploaded-files-notification');
        });
    }
}
