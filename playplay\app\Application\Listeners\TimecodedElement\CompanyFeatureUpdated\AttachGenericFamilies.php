<?php

declare(strict_types=1);

namespace App\Application\Listeners\TimecodedElement\CompanyFeatureUpdated;

use App\Application\Events\CompanyFeatureUpdated;
use App\Domain\Company\Repositories\CompanyRepository;
use App\Domain\Feature\Repositories\FeatureRepository;
use App\Domain\Team\TeamRepository;
use App\Domain\TimecodedElement\TimecodedElementsFamilyService;

final class AttachGenericFamilies
{
    private TimecodedElementsFamilyService $timecodedElementsFamilyService;
    private CompanyRepository $companyRepository;
    private FeatureRepository $featureRepository;
    private TeamRepository $teamRepository;

    public function __construct(
        TimecodedElementsFamilyService $timecodedElementsFamilyService,
        CompanyRepository $companyRepository,
        FeatureRepository $featureRepository,
        TeamRepository $teamRepository
    ) {
        $this->timecodedElementsFamilyService = $timecodedElementsFamilyService;
        $this->companyRepository = $companyRepository;
        $this->featureRepository = $featureRepository;
        $this->teamRepository = $teamRepository;
    }

    public function handle(CompanyFeatureUpdated $companyFeatureUpdated): void
    {
        $timecodedElementFeatureId = $this->featureRepository->getIdByName('has_timecoded_elements');

        if (!in_array($timecodedElementFeatureId, $companyFeatureUpdated->updatedFeaturesIds, true)) {
            return;
        }

        $company = $this->companyRepository->getById($companyFeatureUpdated->companyId);
        if (!$company->hasTimecodedElementsFeature()) {
            return;
        }

        $teamIds = $this->teamRepository->getIdsByCompanyId($company->id);
        foreach ($teamIds as $teamId) {
            $this->timecodedElementsFamilyService->attachAllGenericFamilies($teamId);
        }
    }
}
