<?php

namespace App\Application\Http\Requests\Api\CorporateLibraryItem;

use App\Models\CorporateLibraryItem;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Factory;

class LibraryItemMoveRequest extends FormRequest
{
    public static function rules(): array
    {
        return [
            'items' => ['required', 'array', 'are_valid'],
        ];
    }

    public function __construct(Factory $validationFactory)
    {
        $this->addAreValidRule($validationFactory);
    }

    public function authorize(): bool
    {
        return true;
    }

    private function addAreValidRule(Factory $validationFactory): void
    {
        $validationFactory->extendImplicit(
            'are_valid',
            function ($attribute, $value) {
                $ids = array_column($value, 'id');
                $folderId = data_get($value, '0.folder_id');
                $companyId = auth()->user()->company_id;

                return CorporateLibraryItem::whereIn('id', $ids)
                        ->where('company_id', $companyId)
                        ->where('is_folder', false)
                        ->count() === count($ids)
                    && ($folderId === null
                        || CorporateLibraryItem::where('id', $folderId)
                            ->where('company_id', $companyId)
                            ->where('is_folder', true)
                            ->exists());
            }
        );
    }
}
