<?php

declare(strict_types=1);

namespace App\Domain\Maintenance;

use App\Models\Maintenance;
use DateTimeImmutable;

interface MaintenanceRepository
{
    public function create(
        bool $isLightMode,
        string $message,
        DateTimeImmutable $startedAt,
        ?DateTimeImmutable $endedAt
    ): Maintenance;

    public function getCurrent(): ?Maintenance;

    public function startNow(bool $isLightMode, string $message): Maintenance;

    public function stopNow(Maintenance $maintenance): void;

    public function update(
        Maintenance $maintenance,
        string $message,
        DateTimeImmutable $startedAt,
        ?DateTimeImmutable $endedAt
    ): void;
}
