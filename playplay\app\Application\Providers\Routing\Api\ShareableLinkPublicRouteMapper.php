<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\V2\ShareableLink\ShareableLinkPublicController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class ShareableLinkPublicRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([], function (Router $router) {
            $this->mapperResource($router);
        });
    }

    private function mapperResource(Router $router): void
    {
        $router->post('/shareable-links/{shareable_link}', [ShareableLinkPublicController::class, 'show'])
            ->name('shareable-links.show');
        $router->get('/shareable-links/{shareable_link}/download', [ShareableLinkPublicController::class, 'download'])
            ->name('shareable-links.download');
    }
}
