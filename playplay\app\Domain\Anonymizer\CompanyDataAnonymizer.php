<?php

namespace App\Domain\Anonymizer;

use App\Models\Company;
use Throwable;

class CompanyDataAnonymizer
{
    /**
     * @var AnonymizableInterface[]
     */
    private iterable $anonymizers;

    public function __construct(iterable $anonymizers)
    {
        $this->anonymizers = $anonymizers;
    }

    /**
     * @throws DataAnonymizerException
     */
    public function anonymize(Company $company): void
    {
        try {
            foreach ($this->anonymizers as $anonymizer) {
                $anonymizer->anonymizeCompany($company);
            }
        } catch (Throwable $e) {
            throw new DataAnonymizerException(
                "Something went wrong while anonymising company #{$company->id} :{$e->getMessage()}"
            );
        }
    }
}
