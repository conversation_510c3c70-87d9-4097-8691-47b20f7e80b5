<?php

declare(strict_types=1);

namespace App\Domain\Billing;

use App\Domain\Plan\PlanType;
use App\Models\Subscription;
use App\Models\Team;
use App\Models\Permissions\AppRole;
use App\Models\User;

final class SubscriptionPolicy
{
    public function create(User $user): bool
    {
        return !$user->company->getCurrentSubscription();
    }

    public function churn(User $user, Subscription $subscription): bool
    {
        if ($user->company?->plan?->name !== PlanType::STANDARD->value) {
            return false;
        }

        if ($subscription->company->id !== $user->company->id) {
            return false;
        }

        // TODO: use pivot->app_role_id instead of pivot->role
        return $user->teams->contains(function (Team $team) {
            return $team->pivot->role === AppRole::ROLE_OWNER;
        });
    }
}
