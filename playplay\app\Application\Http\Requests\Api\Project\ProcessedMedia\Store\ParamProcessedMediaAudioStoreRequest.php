<?php

namespace App\Application\Http\Requests\Api\Project\ProcessedMedia\Store;

use App\Domain\RawMedia\RawMediaSource;
use App\Models\ProcessedMedia;
use App\Models\Project;
use App\Models\ScreenParams\BaseParam;
use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Factory;
use Illuminate\Validation\Rule;

class ParamProcessedMediaAudioStoreRequest extends FormRequest
{
    public static function getRules(): array
    {
        return [
            'param_id' => ['required', 'integer', 'paramIsTypeAudioAndInProject'],
            'trimStart' => ['required', 'numeric', 'min:0'],
            'trimEnd' => ['required', 'numeric', 'greater_than:trimStart'],
            'raw_media_id' => ['required', 'exists:raw_medias,id', 'isAudio'],
            'project_id' => ['required', 'exists:projects,id'],
            'source' => [
                'required',
                Rule::in([
                    RawMediaSource::UPLOAD,
                    ProcessedMedia::SOURCE_FAVORITES,
                    ProcessedMedia::SOURCE_LIBRARY
                ]),
            ],
        ];
    }

    public function __construct()
    {
        $validationFactory = app(Factory::class);
        $this->addParamIsTypeAudioAndInProjectRule($validationFactory);
        parent::__construct();
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    private function addParamIsTypeAudioAndInProjectRule(Factory $validationFactory): void
    {
        $validationFactory->extendImplicit(
            'paramIsTypeAudioAndInProject',
            static function ($attribute, $value): bool {
                $project = Project::find(request()->get('project_id'));
                if ($project === null) {
                    return false;
                }

                $projectScreenIds = $project->projectScreens->pluck('screen_id')->toArray();

                return BaseParam::where('id', $value)
                    ->with([
                        'screenLayout' => function (Builder $queryBuilder) use ($projectScreenIds) {
                            $queryBuilder->whereIn('screen_id', $projectScreenIds);
                        },
                    ])
                    ->where('type', '=', BaseParam::TYPE_AUDIO)
                    ->exists();
            }
        );
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return static::getRules();
    }
}
