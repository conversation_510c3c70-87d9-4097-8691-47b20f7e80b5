<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Template;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\Template\TemplateStoreRequest;
use App\Domain\Template\Repositories\TemplateRepository;
use App\Domain\Template\Services\TemplateService;
use App\Models\SubCategory;
use App\Models\Template;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

final class TemplateController extends BaseController
{
    private TemplateRepository $templateRepository;
    private TemplateService $templateService;

    public function __construct(
        TemplateRepository $templateRepository,
        TemplateService $templateService
    ) {
        $this->templateRepository = $templateRepository;
        $this->templateService = $templateService;
    }

    public function create(): View
    {
        $template = new Template();

        $subCategories = SubCategory::all()->pluck('backoffice_name', 'id')->toArray();

        return view('admin.templates.create', [
            'template' => $template,
            'subCategories' => $subCategories,
            'formatCovers' => $this->templateService->getTemplateCoversByFormat($template),
            'templateProjects' => $this->templateService->getTemplateProjectsByFormat($template),
        ]);
    }

    public function destroy(Template $template): JsonResponse
    {
        return $this->ajaxDelete($template, route('admin.templates.index'));
    }

    public function edit(Template $template): View
    {
        $subCategories = SubCategory::all()->pluck('backoffice_name', 'id')->toArray();

        return view('admin.templates.edit', [
            'template' => $template,
            'subCategories' => $subCategories,
            'formatCovers' => $this->templateService->getTemplateCoversByFormat($template),
            'templateProjects' => $this->templateService->getTemplateProjectsByFormat($template),
        ]);
    }

    public function index(Request $request): View
    {
        $query = $request->get('search', '');
        return view('admin.templates.index', [
            'tabActive' => 'templates',
            'templates' => $this->templateRepository->getPaginatedTemplatesOrderedById($query),
        ]);
    }

    public function showDangerZone(Template $template): View
    {
        return view('admin.templates.danger-zone', ['template' => $template]);
    }

    public function store(TemplateStoreRequest $request): RedirectResponse
    {
        $template = $this->templateRepository
            ->createFromNamesAndScope(
                $request->input('name'),
                $request->input('backoffice_name'),
                (bool) $request->input('has_only_assigned_screens')
            );
        $this->templateService->addCoversToATemplate($template, $request->get('covers', []));
        $this->templateRepository->updateSubCategoriesOfATemplate($template, $request->get('sub_categories_ids', []));
        $this->templateRepository->updateProjectModelsOfATemplate($template, $request->get('projects', []));

        return redirect()->route('admin.templates.index');
    }

    public function update(TemplateStoreRequest $request): RedirectResponse
    {
        /** @var Template $oldTemplate */
        $oldTemplate = request()->route('template');
        $template = $this->templateRepository->updateNamesAndScope(
            $oldTemplate,
            $request->get('name'),
            $request->get('backoffice_name'),
            (bool) $request->get('has_only_assigned_screens')
        );
        $coverIdsToDelete = (new Collection($request->input('delete_cover', [])))
            ->filter(function ($cover) {
                return (bool) $cover;
            })->keys();

        $this->templateRepository->deleteCoversOfATemplate($template, $coverIdsToDelete->toArray());
        $this->templateService->addCoversToATemplate($template, $request->get('covers', []));
        $this->templateRepository->updateSubCategoriesOfATemplate($template, $request->get('sub_categories_ids', []));
        $this->templateRepository->updateProjectModelsOfATemplate($template, $request->get('projects', []));

        return redirect()->route('admin.templates.index');
    }
}
