<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\ProjectScreen\Param\Subtitles;

use App\Application\Http\Controllers\Api\BaseController;
use App\Domain\ProjectScreen\Param\Subtitles\ExportSubtitlesFileNamingService;
use App\Domain\ProjectScreen\Param\Subtitles\InvalidSubripFileException;
use App\Domain\ProjectScreen\Param\Subtitles\SRTSubtitleTransformer;
use App\Domain\ProjectScreen\Param\Subtitles\TimecodedProjectScreenParamSubtitle;
use App\Infrastructure\ProjectScreen\Param\Subtitles\ImportSRTSubtitlesService;
use App\Models\ProjectScreen;
use App\Models\ProjectScreenParam;
use Illuminate\Contracts\Auth\Access\Gate;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

final class SubtitleController extends BaseController
{
    use AuthorizesRequests;

    private Gate $gate;

    private SRTSubtitleTransformer $subtitleTransformer;

    private ExportSubtitlesFileNamingService $exportSubtitlesFileNamingService;

    private ImportSRTSubtitlesService $importSRTSubtitlesService;

    public function __construct(
        Gate $gate,
        SRTSubtitleTransformer $subtitleTransformer,
        ExportSubtitlesFileNamingService $exportSubtitlesFileNamingService,
        ImportSRTSubtitlesService $importSRTSubtitlesService
    ) {
        $this->gate = $gate;
        $this->subtitleTransformer = $subtitleTransformer;
        $this->exportSubtitlesFileNamingService = $exportSubtitlesFileNamingService;
        $this->importSRTSubtitlesService = $importSRTSubtitlesService;
    }

    public function export(
        ProjectScreen $projectScreen,
        ProjectScreenParam $paramSubtitle
    ): Response {
        $this->gate->authorize('export', $paramSubtitle);

        $subtitles = TimecodedProjectScreenParamSubtitle::fromProjectScreenParam($paramSubtitle);

        return new Response(
            $this->subtitleTransformer->timecodedSubtitleArrayToSRTFormattedString($subtitles),
            SymfonyResponse::HTTP_OK,
            $this->makeDownloadableHeaders(
                $this->exportSubtitlesFileNamingService->suggestedFileName(
                    $projectScreen->project->title,
                    $projectScreen->order
                )
            )
        );
    }

    public function import(
        ProjectScreen $projectScreen,
        Request $request
    ): JsonResponse {
        $this->gate->authorize('import', $projectScreen);

        $subripFile = $request->file('file');
        if ($subripFile === null || !$subripFile->isValid() || $subripFile->getClientOriginalExtension() !== 'srt') {
            throw new UnprocessableEntityHttpException();
        }

        try {
            $this->importSRTSubtitlesService->importIntoProjectScreen($subripFile->getContent(), $projectScreen);
        } catch (InvalidSubripFileException) {
            throw new UnprocessableEntityHttpException();
        }

        return $this->sendJsonResponse(new Collection([$projectScreen->refresh()]), SymfonyResponse::HTTP_OK);
    }

    private function makeDownloadableHeaders(string $filename): array
    {
        return [
            'Content-Disposition' => "attachment; filename={$filename}",
            'Content-Type' => 'application/x-subrip; charset=UTF-8',
        ];
    }
}
