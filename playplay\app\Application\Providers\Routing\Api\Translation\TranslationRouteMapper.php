<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Translation;

use App\Application\Http\Controllers\Api\TranslationJobController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class TranslationRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => 'translation',
            'as' => 'translation.',
        ], static function (Router $router): void {
            $router
                ->middleware(['api'])
                ->post('/job/{job}', TranslationJobController::class)
                ->name('callback');
        });
    }
}
