<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\User;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Domain\User\UserRepository;
use App\Models\User;
use Illuminate\Contracts\Auth\Access\Gate;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Symfony\Component\HttpFoundation\Response;

final class UserPermissionController extends BaseController
{
    private Gate $gate;

    private UserRepository $userRepository;

    public function __construct(Gate $gate, UserRepository $userRepository)
    {
        $this->authorizeResource(User::class);
        $this->gate = $gate;
        $this->userRepository = $userRepository;
    }

    public function showDangerZone(User $user): View
    {
        $this->gate->authorize('canAccessRestrictedData', $user->company);
        $this->gate->authorize('update', $user);

        /** TODO à deplacer dans un repository */
        $actualRoles = $user->roles()->pluck('id')->toArray();
        $allRoles = Role::with('permissions')->get()->pluck('name', 'id')->toArray();

        return view('admin.users.danger-zone', [
            'user' => $user,
            'actualRoles' => $actualRoles,
            'allRoles' => $allRoles,
        ]);
    }

    public function updatePermissions(Request $request, User $user): RedirectResponse
    {
        $this->gate->authorize('canAccessRestrictedData', $user->company);
        $this->gate->authorize('update', $user);

        if ($request->get('type') === 'roles') {
            $user->roles()->sync($request->get('roles', []));
        }

        return redirect()->route('admin.users.danger-zone', $user);
    }

    public function showDeactivateAccount(User $user): View
    {
        $this->gate->authorize('canAccessRestrictedData', $user->company);
        $this->gate->authorize('update', $user);

        return view('admin.users.deactivate-account', ['user' => $user]);
    }

    public function deactivateAccount(User $user): JsonResponse
    {
        $this->gate->authorize('canAccessRestrictedData', $user->company);
        $this->gate->authorize('update', $user);

        $this->userRepository->deactivateUser($user);

        $redirectUri = route('admin.users.edit', [$user]);

        return new JsonResponse(['success' => true, 'redirect' => $redirectUri], Response::HTTP_OK);
    }
}
