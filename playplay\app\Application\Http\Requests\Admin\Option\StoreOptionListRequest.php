<?php

namespace App\Application\Http\Requests\Admin\Option;

use App\Models\Screen\Parameters\OptionList;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

final class StoreOptionListRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:45'],
            'backoffice_name' => ['required', 'string', 'max:45'],
            'value_type' => ['required', 'string', Rule::in(OptionList::AVAILABLE_TYPES)],
            'is_generic' => ['required', 'boolean'],
            'option_list_category_id' => ['required', 'exists:screen_parameters_option_list_categories,id'],
        ];
    }
}
