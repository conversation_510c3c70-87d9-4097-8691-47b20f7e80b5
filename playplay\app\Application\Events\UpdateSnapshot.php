<?php

namespace App\Application\Events;

use App\Models\Project;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

class UpdateSnapshot implements ShouldBroadcast
{
    private Project $project;
    private array $projectScreenIds;

    public function __construct(Project $project, array $projectScreenIds)
    {
        $this->project = $project;
        $this->projectScreenIds = $projectScreenIds;
    }

    public function broadcastAs(): string
    {
        return 'update-snapshot';
    }

    public function broadcastOn(): Channel
    {
        return new PrivateChannel("project.{$this->project->id}");
    }

    public function broadcastWith(): array
    {
        return ['projectScreenIds' => $this->projectScreenIds];
    }
}
