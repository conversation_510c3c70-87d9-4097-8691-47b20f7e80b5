<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\Stock\Getty;

use Illuminate\Foundation\Http\FormRequest;

class GettyAuthorizationRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'code' => ['required', 'string'],
            'state' => ['sometimes', 'string'],
        ];
    }
}
