<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\RenderMedia;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Models\Renders\RenderMedia;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;

final class ShowRenderMediaController extends BaseController
{
    private Factory $viewFactory;

    public function __construct(Factory $viewFactory)
    {
        $this->viewFactory = $viewFactory;
    }

    public function __invoke(RenderMedia $render): View
    {
        $this->authorize('view', $render);

        if ($render->isParentProcessedMedia()) {
            $this->authorize('canAccessRestrictedData', $render->parent->project->company);

            return $this->viewFactory->make('admin.render-medias.show-from-processed-media', [
                'render' => $render,
            ]);
        }

        return $this->viewFactory->make('admin.render-medias.show-from-raw-media', [
            'render' => $render,
        ]);
    }
}
