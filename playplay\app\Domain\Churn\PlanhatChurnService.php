<?php

declare(strict_types=1);

namespace App\Domain\Churn;

use App\Domain\Billing\BillingException;
use App\Domain\Billing\BillingPlanRepository;
use App\Domain\Billing\CompanyChurnRepository;
use App\Domain\Billing\CompanyRepository;
use App\Domain\Billing\PlanhatCompanyException;
use App\Domain\Billing\SubscriptionRevenue;
use App\Models\BillingPlan;
use App\Models\CompanyChurn;
use App\Models\Subscription;

final class PlanhatChurnService
{
    private BillingPlanRepository $billingPlanRepository;

    private CompanyRepository $companyRepository;

    private CompanyChurnRepository $companyChurnRepository;

    private ChurnRepository $churnRepository;

    public function __construct(
        BillingPlanRepository $billingPlanRepository,
        CompanyRepository $companyRepository,
        CompanyChurnRepository $companyChurnRepository,
        ChurnRepository $churnRepository,
    ) {
        $this->billingPlanRepository = $billingPlanRepository;
        $this->companyRepository = $companyRepository;
        $this->companyChurnRepository = $companyChurnRepository;
        $this->churnRepository = $churnRepository;
    }

    /**
     * @throws ChurnException
     */
    public function createPlanhatChurn(Subscription $subscription): void
    {
        if (!$this->canCreateAPlanhatChurn($subscription)) {
            throw new ChurnException("A planhat churn already exists for subscription {$subscription->id}");
        }

        /** @var CompanyChurn $companyChurn */
        $companyChurn = $subscription->churn;
        $planhatCompanyId = $this->getPlanhatCompanyId($subscription);
        $subscriptionRevenue = $this->getSubscriptionRevenue($subscription->billingPlan);
        $this->createChurn($companyChurn, $subscriptionRevenue, $planhatCompanyId);
        $this->companyRepository->setPhaseToExit($subscription->company_id);
    }

    private function getPlanhatCompanyId(Subscription $subscription): string
    {
        try {
            return $this->companyRepository->getById($subscription->company_id)->id;
        } catch (PlanhatCompanyException $exception) {
            throw new ChurnException($exception->getMessage());
        }
    }

    private function getSubscriptionRevenue(BillingPlan $billingPlan): SubscriptionRevenue
    {
        try {
            return $this->billingPlanRepository
                ->getBillingPlan($billingPlan->stripe_plan_id)
                ->subscriptionRevenue;
        } catch (BillingException $exception) {
            throw new ChurnException($exception->getMessage());
        }
    }

    private function createChurn(
        CompanyChurn $companyChurn,
        SubscriptionRevenue $subscriptionRevenue,
        string $planhatCompanyId
    ): void {
        $churn = $this->churnRepository->create($companyChurn, $subscriptionRevenue, $planhatCompanyId);
        $this->companyChurnRepository->updatePlanhatChurnId($companyChurn, $churn->id);
    }

    private function canCreateAPlanhatChurn(Subscription $subscription): bool
    {
        if ($subscription->churn === null) {
            return false;
        }

        if ($subscription->churn->planhat_churn_id !== null) {
            return false;
        }

        return true;
    }
}
