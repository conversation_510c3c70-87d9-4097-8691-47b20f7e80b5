<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\Permission;

use App\Application\Http\Controllers\Api\BaseController;
use App\Domain\Permissions\AppRoleRepository;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

final class GetAppRolesController extends BaseController
{
    private AppRoleRepository $appRoleRepository;

    public function __construct(AppRoleRepository $appRoleRepository)
    {
        $this->appRoleRepository = $appRoleRepository;
    }

    public function __invoke(): JsonResponse
    {
        return new JsonResponse(
            $this->appRoleRepository->getAllAvailableAppRolesWithAppPermissions(),
            Response::HTTP_OK
        );
    }
}
