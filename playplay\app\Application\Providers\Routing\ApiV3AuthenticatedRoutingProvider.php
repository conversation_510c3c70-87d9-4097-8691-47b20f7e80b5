<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing;

use App\Application\Providers\Routing\Api\Font\FontRouteMapper;
use App\Application\Providers\Routing\Api\Team\TeamPresetV3RouteMapper;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider;
use Illuminate\Support\Facades\Route;

final class ApiV3AuthenticatedRoutingProvider extends RouteServiceProvider
{
    /** @var RouteMapper[] */
    private array $routeMappers;

    public function __construct(Application $app)
    {
        parent::__construct($app);
        $this->routeMappers = [
            new FontRouteMapper(),
            new TeamPresetV3RouteMapper(),
        ];
    }

    public function map(): void
    {
        Route::group([
            'middleware' => ['api', 'auth'],
            'prefix' => 'api/v3',
            'as' => 'api.v3.',
        ], function () {
            foreach ($this->routeMappers as $routeMapper) {
                $routeMapper->map();
            }
        });
    }
}
