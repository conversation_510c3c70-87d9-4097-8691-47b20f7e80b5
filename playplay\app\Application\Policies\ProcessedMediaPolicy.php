<?php

declare(strict_types=1);

namespace App\Application\Policies;

use App\Domain\Music\MusicListRepository;
use App\Domain\ProcessedMedia\Repository\ProcessedMediaRepository;
use App\Domain\Project\ProcessedMedia\RelationType;
use App\Domain\RawMedia\RawMediaSource;
use App\Models\ProcessedMedia;
use App\Models\Project;
use App\Models\RawMedia;
use App\Models\User;

class ProcessedMediaPolicy
{
    private MusicListRepository $musicListRepository;
    private ProcessedMediaRepository $processedMediaRepository;

    public function __construct(
        MusicListRepository $musicListRepository,
        ProcessedMediaRepository $processedMediaRepository
    ) {
        $this->musicListRepository = $musicListRepository;
        $this->processedMediaRepository = $processedMediaRepository;
    }

    public function create(User $user, RawMedia $rawMedia, Project $project, string $source): bool
    {
        if (!$user->can('view', $project)) {
            return false;
        }

        return match ($source) {
            RawMediaSource::GIPHY,
            RawMediaSource::GETTY,
            RawMediaSource::GETTY_EDITO,
            RawMediaSource::STORYBLOCKS,
            RawMediaSource::UNSPLASH => $rawMedia->source?->value === $source,
            ProcessedMedia::SOURCE_RECENTLY_USED => $user->can('view', $rawMedia),
            RawMediaSource::UPLOAD => $this->canViewUploadedRawMedia($user, $rawMedia, $project),
            ProcessedMedia::SOURCE_LIBRARY => $user->company->libraryItems()
                ->where('raw_media_id', $rawMedia->id)
                ->exists(),
            ProcessedMedia::SOURCE_FAVORITES => $user->can('view', $rawMedia)
                || $user->favoritesRawMedias()
                    ->where('raw_media_id', $rawMedia->id)
                    ->exists(),
            ProcessedMedia::SOURCE_OPTION_LIST => $this
                ->musicListRepository
                ->isRawMediaInMusicLists(
                    $rawMedia->id,
                    $project->preset->musicListsMusics->pluck('id')->all()
                ),
            default => false,
        };
    }

    public function createBranding(
        User $user,
        RawMedia $rawMedia,
        Project $project = null,
        string $source = RawMediaSource::UPLOAD
    ): bool {
        if ($project !== null) {
            return $this->create($user, $rawMedia, $project, $source);
        }

        return $user->can('view', $rawMedia);
    }

    public function destroyAudioProcessedMedia(
        User $user,
        ProcessedMedia $processedMedia,
        RelationType $relationType
    ): bool {
        return $processedMedia->relation_type === $relationType
            && $processedMedia->isAudio()
            && $this->view($user, $processedMedia);
    }

    public function doesNotBelongToAProject(ProcessedMedia $processedMedia): bool
    {
        // we have decided to authorize processed_medias that aren't assigned to a project
        // this is an edge case that only affects logos so it is okay from a security perspective
        return $processedMedia->project_id === null;
    }

    public function update(User $user, ProcessedMedia $processedMedia): bool
    {
        return $this->view($user, $processedMedia);
    }

    public function view(User $user, ProcessedMedia $processedMedia): bool
    {
        return $user->can('view', $processedMedia->project)
            || $user->can('view', $processedMedia->rawMedia)
            || $this->doesNotBelongToAProject($processedMedia);
    }

    public function viewMany(User $user, array $processedMediaIds = []): bool
    {
        $teamIds = $user->teams->pluck('id')->all();
        $processedMediaIds = array_unique($processedMediaIds);
        $nbVisibleProcessedMedias = $this->processedMediaRepository->getNbVisibleProcessedMedias(
            $processedMediaIds,
            $teamIds
        );

        return $nbVisibleProcessedMedias === count($processedMediaIds);
    }

    private function canViewUploadedRawMedia(User $user, RawMedia $rawMedia, Project $project): bool
    {
        return $user->can('view', $rawMedia)
            || $project->rawMedias()
                ->where('raw_medias.id', $rawMedia->id)
                ->exists();
    }
}
