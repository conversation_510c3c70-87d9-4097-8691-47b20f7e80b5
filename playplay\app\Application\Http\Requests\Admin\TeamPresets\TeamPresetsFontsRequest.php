<?php

namespace App\Application\Http\Requests\Admin\TeamPresets;

use Illuminate\Config\Repository as ConfigRepository;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Factory;

class TeamPresetsFontsRequest extends FormRequest
{
    /** @var ConfigRepository */
    private $config;

    public function __construct(Factory $validationFactory, ConfigRepository $config)
    {
        $this->config = $config;
        $this->checkHasDefaultFont($validationFactory);

        parent::__construct();
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $extension = $this->config->get('app.playplay.fonts.extension');

        $rules = [
            'fonts' => ['has_default_font'],
            'fonts.*.id' => ['sometimes', 'exists:fonts,id'],
            'fonts.*.name' => ['required', 'string', 'max:255'],
            'fonts.*.regular_url' => ['required_without:fonts.*.regular_id', 'url', "ends_with:{$extension}"],
            'fonts.*.regular_id' => ['required_without:fonts.*.regular_url', 'exists:raw_medias,id'],
            'fonts.*.bold_url' => ['sometimes', 'nullable', 'url', "ends_with:{$extension}"],
            'fonts.*.bold_id' => ['sometimes', 'exists:raw_medias,id'],
            'fonts.*.italic_url' => ['sometimes', 'nullable', 'url', "ends_with:{$extension}"],
            'fonts.*.italic_id' => ['sometimes', 'exists:raw_medias,id'],
            'fonts.*.is_default' => ['sometimes', 'boolean'],
        ];

        return $rules;
    }

    private function checkHasDefaultFont(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'has_default_font',
            function (string $attribute, ?array $value) {
                $fontsDefault = collect(data_get($value, '*.is_default', []));

                return $fontsDefault->isEmpty() || $fontsDefault->contains(true);
            }
        );
    }
}
