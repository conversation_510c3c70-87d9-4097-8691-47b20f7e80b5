<?php

namespace App\Application\Http\Controllers\Api\V2;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\SignUp\EmailValidRequest;
use App\Application\Http\Requests\Api\SignUp\SignUpRequest;
use App\Application\Mail\Welcome;
use App\Domain\Plan\MissingPlanException;
use App\Services\SignUpService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class SignUpController extends BaseController
{
    public function emailValid(EmailValidRequest $request): JsonResponse
    {
        return $this->sendJsonResponse(new Collection([]), JsonResponse::HTTP_OK);
    }

    /**
     * @throws MissingPlanException
     */
    public function store(SignUpRequest $request, SignUpService $signUpService): JsonResponse
    {
        [$user, $firstProject] = $signUpService->createAccount(
            $request->only([
                'first_name',
                'last_name',
                'email',
                'phone',
                'password',
                'language',
                'terms',
                'option_newsletter',
            ]),
            [
                // Cookie for hubspot tracking
                'hutk' => $request->get('hutk'),
                // URL origin for hubspot tracking
                'origin' => $request->get('origin'),
                'referer' => $request->headers->get('referer'),
                'ip' => $request->ip(),
                'employees_number' => $request->get('employees_number'),
                'usage_of_playplay' => $request->get('usage_of_playplay'),
            ]
        );

        Auth::login($user, false);

        $result = [];

        if ($firstProject !== null) {
            $result = [
                'project_id' => $firstProject->id,
                'template_id' => $firstProject->template_id,
            ];
        }

        Mail::to($user)->send(new Welcome($user));

        return $this->sendJsonResponse(new Collection([$result]), JsonResponse::HTTP_CREATED);
    }
}
