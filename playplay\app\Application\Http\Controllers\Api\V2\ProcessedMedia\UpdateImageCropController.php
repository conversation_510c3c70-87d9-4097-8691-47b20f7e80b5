<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\ProcessedMedia;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Project\ProcessedMedia\Update\ProcessedMediaImageCropUpdateRequest;
use App\Domain\Workflow\Config\MediaWorkflow;
use App\Domain\Render\RenderMedia\Crop\CropFactory;
use App\Models\ProcessedMedia;
use App\Services\ProcessedMedia\FilterApplier;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

final class UpdateImageCropController extends BaseController
{
    use AuthorizesRequests;

    private FilterApplier $filterApplier;
    private MediaWorkflow $mediaWorkflow;

    public function __construct(FilterApplier $filterApplier, MediaWorkflow $mediaWorkflow)
    {
        $this->filterApplier = $filterApplier;
        $this->mediaWorkflow = $mediaWorkflow;
    }

    /**
     * @throws AuthorizationException
     */
    public function __invoke(
        ProcessedMedia $processedMedia,
        ProcessedMediaImageCropUpdateRequest $request
    ): JsonResponse {
        $this->authorize('view', $processedMedia->project);

        if (!$processedMedia->rawMedia->isImage()) {
            throw new BadRequestHttpException();
        }

        $this->mediaWorkflow->start($processedMedia->rawMedia);

        $this->filterApplier->cropImageBasedOnParam(
            $processedMedia,
            $request->get('param_id'),
            CropFactory::createCropFromArray($request->get('crop') ?? []),
        );

        return $this->sendJsonResponse(new Collection([$processedMedia]), Response::HTTP_OK);
    }
}
