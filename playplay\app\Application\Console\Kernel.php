<?php

namespace App\Application\Console;

use App\Application\Console\Commands\CheckForExpiredSubscriptions;
use App\Application\Console\Commands\Company\ButterflyToClientCommand;
use App\Application\Console\Commands\CustomAtScale\AddActivatedGenericScreensToTeams;
use App\Application\Console\Commands\Database\CleanDatabaseSeeds;
use App\Application\Console\Commands\Database\InactiveUsersAndCompaniesAnonymizer;
use App\Application\Console\Commands\DisableExpiredTemporaryUsers;
use App\Application\Console\Commands\FillDailyUsageTable;
use App\Application\Console\Commands\Getty\PublishGettyAssetUsages;
use App\Application\Console\Commands\GoogleCloudStorage\RemoveRenderScreenUselessFiles;
use App\Application\Console\Commands\PublishMetrics;
use App\Application\Console\Commands\PublishPlanhatData;
use App\Application\Console\Commands\SendFirstVideoMail;
use App\Application\Console\Commands\Subscriptions\SendStandardChurnNotifications;
use App\Application\Console\Commands\TimecodedElement\ActivateNewTimecodedElementPresets;
use App\Application\Console\Commands\Users\SendOnboardingEmail;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        PublishMetrics::class,
        CheckForExpiredSubscriptions::class,
        FillDailyUsageTable::class,
        SendFirstVideoMail::class,
        DisableExpiredTemporaryUsers::class,
        ActivateNewTimecodedElementPresets::class,
    ];

    /**
     * Register the Closure based commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');
    }

    /**
     * Define the application's command schedule.
     *
     * @param Schedule $schedule
     *
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // Every minute
        $schedule->command(PublishMetrics::class)->cron('* * * * *');

        // At 06:00 on Sunday
        $schedule->command(InactiveUsersAndCompaniesAnonymizer::class)->cron('0 6 * * 0')
            ->environments('production');

        // At 00:00 every day
        $schedule->command(CleanDatabaseSeeds::class)
            ->cron('0 0 * * *')
            ->skip(fn() => getenv('APP_ENV') === 'production');
        $schedule->command(DisableExpiredTemporaryUsers::class)->cron('0 0 * * *')
            ->environments('production');

        // At 00:30 every day
        // I will let it run on all envs
        $schedule->command(RemoveRenderScreenUselessFiles::class)->cron('30 0 * * *');

        // At 01:00 every day
        $schedule->command(FillDailyUsageTable::class)->cron('0 1 * * *');

        // At 02:00 every day
        $schedule->command(PublishPlanhatData::class)->cron('0 2 * * *')
            ->environments('production');

        // At 03:00 every day
        $schedule->command(CheckForExpiredSubscriptions::class)->cron('0 3 * * *');
        $schedule->command(AddActivatedGenericScreensToTeams::class)->cron('0 3 * * *')
            ->environments('production');

        // At 05:00 every day
        $schedule->command(PublishGettyAssetUsages::class)->cron('0 5 * * *')
            ->environments('production');

        // At 07:00 every day
        $schedule->command(SendOnboardingEmail::class)->cron('0 7 * * *')
            ->environments('production');
        $schedule->command(SendStandardChurnNotifications::class)->cron('0 7 * * *')
            ->environments('production');
        $schedule->command(ButterflyToClientCommand::class)->cron('0 7 * * *')
            ->environments('production');

        // At 10:00 every day
        $schedule->command(SendFirstVideoMail::class)->cron('0 10 * * *');
        // Every day at 4 am
        $schedule->command(ActivateNewTimecodedElementPresets::class)->cron('0 4 * * *');
    }
}
