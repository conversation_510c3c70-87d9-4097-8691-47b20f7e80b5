<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Project;

use App\Application\Http\Controllers\Api\V2\Project\CreateFromScratchProjectController;
use App\Application\Http\Controllers\Api\V2\Project\CreateFromTemplateProjectController;
use App\Application\Http\Controllers\Api\V2\Project\DeleteProjectController;
use App\Application\Http\Controllers\Api\V2\Project\DuplicateProjectController;
use App\Application\Http\Controllers\Api\V2\Project\UpdateProjectController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class ProjectAuthRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/projects',
            'as' => 'projects.',
        ], static function (Router $router) {
            $router->delete('/{project}', DeleteProjectController::class)->name('destroy');
            $router->post('/', CreateFromScratchProjectController::class)->name('store');
            $router->post('/fromTemplate', CreateFromTemplateProjectController::class)->name('storeFromTemplate');
            $router->post('/{project}/duplicate', DuplicateProjectController::class)->name('duplicate');
            $router->put('/{project}', UpdateProjectController::class)->name('update');
        });
    }
}
