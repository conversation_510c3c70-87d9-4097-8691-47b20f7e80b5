<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\Project;

use App\Application\Http\Controllers\Api\BaseController;
use App\Models\Project;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class ManageProjectController extends BaseController
{
    use AuthorizesRequests;

    /**
     * @throws AuthorizationException
     */
    public function privatize(Project $project): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $project->company);
        $this->authorize('can-privatize-or-share', $project);

        $project->update(['is_private' => true, 'folder_id' => null]);

        return $this->sendJsonResponse(new Collection(), Response::HTTP_NO_CONTENT);
    }

    /**
     * @throws AuthorizationException
     */
    public function shareWithTeam(Project $project): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $project->company);
        $this->authorize('can-privatize-or-share', $project);

        $project->update(['is_private' => false]);

        return $this->sendJsonResponse(new Collection(), Response::HTTP_NO_CONTENT);
    }
}
