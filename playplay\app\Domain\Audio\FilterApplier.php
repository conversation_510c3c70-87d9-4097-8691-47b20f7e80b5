<?php

namespace App\Domain\Audio;

use App\Domain\Processing\RenderMediaFactory;
use App\Domain\Render\RenderMedia\RenderMediaRepository;
use App\Domain\Render\RenderMedia\RenderMediaStatus;
use App\Models\ProcessedMedia;
use App\Models\Renders\RenderMedia;
use App\Services\Processing\RenderMediaService;

class FilterApplier
{
    private RenderMediaFactory $renderMediaFactory;
    private RenderMediaRepository $renderMediaRepository;
    private RenderMediaService $renderMediaService;
    private AudioFactory $audioFactory;
    private TimelineParameterService $timelineParameterService;

    public function __construct(
        AudioFactory $audioFactory,
        RenderMediaFactory $renderMediaFactory,
        RenderMediaService $renderMediaService,
        RenderMediaRepository $renderMediaRepository,
        TimelineParameterService $timelineParameterService
    ) {
        $this->audioFactory = $audioFactory;
        $this->renderMediaFactory = $renderMediaFactory;
        $this->renderMediaRepository = $renderMediaRepository;
        $this->renderMediaService = $renderMediaService;
        $this->timelineParameterService = $timelineParameterService;
    }

    public function applyTrims(array $renderMedias): void
    {
        /** @var RenderMedia $renderMedia */
        foreach ($renderMedias as $renderMedia) {
            if (!$this->shouldTrimRenderMedia($renderMedia)) {
                continue;
            }

            $trimStart = $renderMedia->data?->getTrimStart();
            $trimEnd = $renderMedia->data?->getTrimEnd();

            if ($trimStart === null || $trimEnd === null) {
                $renderMedia->update([
                    'status' => RenderMediaStatus::processed(),
                    'thumbnail_url' => $renderMedia->parent->thumbnail_url,
                ]);
                continue;
            }

            $this->renderMediaService->audioResizeRenderMedia($renderMedia);
        }
    }

    public function split(ProcessedMedia $processedMedia, float $splitTime): SplitProcessedMedia
    {
        $existingRenderMedia = $this->renderMediaRepository->getLastByProcessedMediaId($processedMedia->id);
        $data = $existingRenderMedia->data;
        $originalTrimEnd = $this->timelineParameterService->roundToZeroIfNeeded($data->getTrimEnd());
        $newTrimEnd = $data->getTrimStart() + $splitTime - $data->getStart();
        $newTrimEnd = $this->timelineParameterService->roundToZeroIfNeeded($newTrimEnd);

        $data = $data->toArray();
        $data['trimEnd'] = $newTrimEnd;
        $this->renderMediaFactory->create($processedMedia, $data);

        return new SplitProcessedMedia(
            $this->audioFactory->create(
                $processedMedia->relation_type,
                $processedMedia->raw_media_id,
                $processedMedia->source,
                $processedMedia->project,
                $splitTime,
                $newTrimEnd,
                $originalTrimEnd
            ),
            $processedMedia->refresh()
        );
    }

    public function trim(ProcessedMedia $processedMedia, float $trimStart, float $trimEnd, float $start): RenderMedia
    {
        return $this->renderMediaFactory->create($processedMedia, [
            'trimStart' => $this->timelineParameterService->roundToZeroIfNeeded($trimStart),
            'trimEnd' => $this->timelineParameterService->roundToZeroIfNeeded($trimEnd),
            'start' => $this->timelineParameterService->roundToZeroIfNeeded($start),
        ]);
    }

    private function shouldTrimRenderMedia(?RenderMedia $renderMedia): bool
    {
        if ($renderMedia === null) {
            return false;
        }

        $isAudioRenderMedia = $renderMedia->isAudio();
        $isProcessableRenderMedia = !in_array($renderMedia->status->value, [
            RenderMediaStatus::PROCESSING,
            RenderMediaStatus::PROCESSED,
        ], true);

        return $isAudioRenderMedia && $isProcessableRenderMedia;
    }
}
