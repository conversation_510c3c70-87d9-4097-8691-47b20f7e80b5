<?php

namespace App\Application\Console\Commands\Database\ClearForLightDB;

use App\Models\Company;
use Illuminate\Support\Facades\DB;

class DeleteCompanyDataService
{
    private const FREE_TRIAL_COMPANY_MODEL_ID = 591;
    private const TABLES_LINKED_TO_TEAMS = [
        'teams_screens',
        'team_categories',
        'team_features',
        'teams_screen_parameters_options',
        'team_presets',
        'team_timecoded_elements_family',
        'team_timecoded_element_preset',
        'users_teams_app_roles',
    ];
    private const TABLES = [
        'users',
        'teams',
        'company_features',
        'company_histories',
    ];

    public function deleteCompanyDataThatIsNotLinkedToPlayPlayOrCypress(): void
    {
        $companiesToKeepIds = Company::query()->select('id')
            ->whereIn('name', [
                'PlayPlay',
                'test-e2e',
                'test-e2e-2',
                'test-e2e-3',
            ])
            ->pluck('id')
            ->toArray();
        $companiesToKeepIds[] = self::FREE_TRIAL_COMPANY_MODEL_ID;

        foreach (self::TABLES_LINKED_TO_TEAMS as $tableLinkedToTeams) {
            $this->deleteWhereTeamNotIn($tableLinkedToTeams, $companiesToKeepIds);
        }

        foreach (self::TABLES as $table) {
            $this->deleteWhereCompanyNotIn($table, $companiesToKeepIds);
        }

        DB::table('companies')
            ->whereNotIn('id', $companiesToKeepIds)
            ->delete();
    }

    private function deleteWhereCompanyNotIn(
        string $table,
        array $companyIds
    ): void {
        DB::table($table)
            ->whereNotIn('company_id', $companyIds)
            ->delete();
    }

    private function deleteWhereTeamNotIn(
        string $table,
        array $companyIds
    ): void {
        $companyIds = implode(', ', $companyIds);
        DB::delete("DELETE FROM $table WHERE team_id NOT IN (
            SELECT id FROM teams WHERE company_id IN ($companyIds)
        )");
    }
}
