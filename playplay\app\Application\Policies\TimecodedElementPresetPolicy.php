<?php

declare(strict_types=1);

namespace App\Application\Policies;

use App\Models\User;

final class TimecodedElementPresetPolicy extends DefaultPolicy
{
    public function before(User $user, string $ability): ?bool
    {
        $companyHasTimecodedElementsFeature = $user->company?->hasTimecodedElementsFeature() ?? false;

        if (!$companyHasTimecodedElementsFeature) {
            return false;
        }

        return null;
    }
}
