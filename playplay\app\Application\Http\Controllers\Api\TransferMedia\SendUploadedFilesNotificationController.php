<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\TransferMedia;

use App\Application\Http\Requests\Api\TransferMedia\SendUploadedFilesNotificationRequest;
use App\Domain\Notification\NotificationContent;
use App\Domain\Notification\NotificationService;
use App\Domain\Notification\NotificationToBeSent;
use App\Domain\Project\ProjectRepository;
use App\Models\Notification;
use App\Models\Project;
use Illuminate\Contracts\Routing\UrlGenerator;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

final class SendUploadedFilesNotificationController
{
    private NotificationService $notificationService;
    private ProjectRepository $projectRepository;
    private UrlGenerator $urlGenerator;

    public function __construct(
        ProjectRepository $projectRepository,
        NotificationService $notificationService,
        UrlGenerator $urlGenerator
    ) {
        $this->notificationService = $notificationService;
        $this->projectRepository = $projectRepository;
        $this->urlGenerator = $urlGenerator;
    }

    public function __invoke(SendUploadedFilesNotificationRequest $request)
    {
        $project = $this->projectRepository->findOneByUuid($request->get('uuid'));
        $uploadedFilesCount = $request->get('uploaded_files_count');

        if ($project === null) {
            throw new NotFoundHttpException();
        }

        $this->notificationService->createAndSend(
            new NotificationToBeSent(
                Notification::MEDIA_TRANSFERRED_FROM_COLLABORATOR,
                NotificationContent::createForUploadedFiles($project->title, $uploadedFilesCount),
                null,
                $this->getProjectUrl($project),
                $project->user_id,
            )
        );

        return new JsonResponse([], Response::HTTP_CREATED);
    }

    private function getProjectUrl(Project $project): string
    {
        return $this->urlGenerator->to("/app/studio?project={$project->id}");
    }
}
