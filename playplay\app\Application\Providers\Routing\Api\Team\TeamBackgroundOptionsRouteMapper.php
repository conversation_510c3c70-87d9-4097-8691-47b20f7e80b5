<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Team;

use App\Application\Http\Controllers\Api\Team\GetTeamBackgroundOptionsController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class TeamBackgroundOptionsRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::get('/teams/{team}/background-options', GetTeamBackgroundOptionsController::class)
            ->name('teams.background-options');
    }
}
