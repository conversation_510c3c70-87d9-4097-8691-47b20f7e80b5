<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\User;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\User\UserSwitchTeamRequest;
use App\Domain\Permissions\AppRoleRepository;
use App\Domain\UserTeam\TeamAppRoleDTO;
use App\Domain\UserTeam\TeamAppRoleDTOCollection;
use App\Infrastructure\Project\ProjectTransferService;
use App\Infrastructure\User\UserTransferService;
use App\Models\Permissions\AppRole;
use App\Models\Team;
use App\Models\User;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Collection;

final class UserTransferController extends BaseController
{
    private UserTransferService $userTransferService;
    private ProjectTransferService $projectTransferService;
    private AppRoleRepository $appRoleRepository;
    private Factory $viewFactory;

    public function __construct(
        UserTransferService $userTransferService,
        ProjectTransferService $projectTransferService,
        AppRoleRepository $appRoleRepository,
        Factory $viewFactory,
    ) {
        $this->authorizeResource(User::class);
        $this->userTransferService = $userTransferService;
        $this->projectTransferService = $projectTransferService;
        $this->appRoleRepository = $appRoleRepository;
        $this->viewFactory = $viewFactory;
    }

    public function show(User $user): View
    {
        $this->authorize('canAccessRestrictedData', $user->company);
        $allTeams = $user->company === null ? [] : $user->company->teams->pluck('name', 'id')->toArray();

        return $this->viewFactory->make('admin.users.transfer', [
            'user' => $user,
            'allTeams' => $allTeams,
        ]);
    }

    public function transferUser(User $user, UserSwitchTeamRequest $request): RedirectResponse
    {
        $this->authorize('canAccessRestrictedData', $user->company);
        $this->authorize('update', $user);

        $teamId = $request->get('team_id');
        $transferProjects = $request->boolean('transfer_projects');

        /** @var Team $team */
        $team = Team::query()->find($teamId);
        $appRoleCreator = $this->appRoleRepository->getByName(AppRole::ROLE_CREATOR);
        $teamAppRole = new TeamAppRoleDTO($team->id, $appRoleCreator->id, AppRole::ROLE_CREATOR);
        $this->userTransferService->transferUserToCompanyAndTeams(
            $user,
            $team->company_id,
            new TeamAppRoleDTOCollection([$teamAppRole])
        );

        if ($transferProjects === true) {
            $this->projectTransferService->transferProjects($user, $team, $user->id);
        }

        return redirect()->back();
    }
}
