<?php

namespace App\Domain\Localization;

use Illuminate\Support\Collection;
use InvalidArgumentException;

final class CountryCode
{
    private const DEFAULT_COUNTRY_CODE = 'FR';

    public static function default(): CountryCode
    {
        return new self(self::DEFAULT_COUNTRY_CODE);
    }

    public static function fromArray(array $attributes): CountryCode
    {
        if (!isset($attributes['country_code'])) {
            throw new InvalidArgumentException("attributes must be an array with country_code key");
        }

        if (strlen($attributes['country_code']) <= 1 || $attributes['country_code'] === 'Not found') {
            throw new InvalidArgumentException("country_code is not found : " . $attributes['country_code']);
        }

        return new self($attributes['country_code']);
    }

    private string $countryCode;

    private function __construct(string $countryCode)
    {
        $this->countryCode = $countryCode;
    }

    public function toVue(): Collection
    {
        return new Collection([$this->countryCode]);
    }
}
