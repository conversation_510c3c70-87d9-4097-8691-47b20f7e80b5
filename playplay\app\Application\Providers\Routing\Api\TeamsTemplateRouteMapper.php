<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\V2\Template\GetTemplateCategoriesController;
use App\Application\Http\Controllers\Api\V2\Template\TeamTemplateController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class TeamsTemplateRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/teams',
            'as' => 'teams.',
        ], static function (Router $router) {
            $router->get('/{team}/template-categories', GetTemplateCategoriesController::class)
                ->name('template-categories.index');

            Route::group([
                'prefix' => '/templates',
                'as' => 'templates.',
            ], static function (Router $router) {
                $router->post('/', [TeamTemplateController::class, 'store'])->name('store');
                $router->put('/{template}', [TeamTemplateController::class, 'update'])->name('update');
                $router->delete('/{template}', [TeamTemplateController::class, 'destroy'])->name('destroy');
            });
        });
    }
}
