<?php

declare(strict_types=1);

namespace App\Application\Listeners\Subscription;

use App\Application\Events\SubscriptionCreated;
use App\Domain\Company\CompanyFeatureService;
use App\Domain\Company\CompanyService;
use App\Domain\Plan\MissingPlanException;
use App\Models\Plan;

final class UpdateCompanyFeatures
{
    private CompanyService $companyService;
    private CompanyFeatureService $companyFeatureService;

    public function __construct(
        CompanyService $companyService,
        CompanyFeatureService $companyFeatureService
    ) {
        $this->companyService = $companyService;
        $this->companyFeatureService = $companyFeatureService;
    }

    /**
     * @throws MissingPlanException
     */
    public function handle(SubscriptionCreated $event): void
    {
        $subscription = $event->getSubscription();

        $this->companyService->applyPlanToCompany($subscription->company, Plan::DEFAULT_PLAN_FOR_NEW_SUBSCRIPTION);
        $this->companyFeatureService->updateCompanyFeaturesBasedOnItsPlan($subscription->company);
    }
}
