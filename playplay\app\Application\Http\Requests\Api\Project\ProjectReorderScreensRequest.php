<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\Project;

use Illuminate\Foundation\Http\FormRequest;

final class ProjectReorderScreensRequest extends FormRequest
{
    public static function rules(): array
    {
        return [
            'project_screen_ids' => ['required', 'array'],
            'project_screen_ids.*' => ['int'],
        ];
    }

    public function authorize(): bool
    {
        return true;
    }
}
