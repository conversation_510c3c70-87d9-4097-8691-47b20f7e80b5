<?php

declare(strict_types=1);

namespace App\Domain\Motion;

use App\Domain\Motion\Exception\InvalidMotionConfigDataException;

final class MotionConfigData
{
    public readonly string $version;
    public readonly string $url;

    private function __construct(string $version, string $url)
    {
        $this->version = $version;
        $this->url = $url;
    }

    public static function fromArray(array $motionConfig): MotionConfigData
    {
        $version = $motionConfig['version'] ?? '';
        $url = $motionConfig['url'] ?? '';

        if ($version === '' || !is_string($version)) {
            throw new InvalidMotionConfigDataException('Config "version" is invalid');
        }

        if ($url === '' || !is_string($url)) {
            throw new InvalidMotionConfigDataException('Config "url" is invalid');
        }

        return new self($version, $url);
    }

    public function toArray(): array
    {
        return [
            'version' => $this->version,
            'url' => $this->url,
        ];
    }
}
