<?php

declare(strict_types=1);

namespace App\Domain\Project\Sanitizer;

use App\Domain\TeamPreset\TeamPresetService;
use App\Models\Project;

final class ProjectMusicsSanitizer implements ProjectElementsSanitizer
{
    private TeamPresetService $teamPresetService;

    public function __construct(TeamPresetService $teamPresetService)
    {
        $this->teamPresetService = $teamPresetService;
    }

    public function sanitize(Project $project): bool
    {
        $hasBeenSanitized = false;
        foreach ($project->musics as $processedMedia) {
            if (!$this->teamPresetService->isMusicAllowed($project->preset, $processedMedia->raw_media_id)) {
                $processedMedia->delete();
                $hasBeenSanitized = true;
            }
        }

        return $hasBeenSanitized;
    }
}
