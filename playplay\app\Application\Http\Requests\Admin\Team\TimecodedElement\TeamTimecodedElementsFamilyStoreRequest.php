<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\Team\TimecodedElement;

use Illuminate\Foundation\Http\FormRequest;

final class TeamTimecodedElementsFamilyStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'timecoded_elements_family_id' => ['integer', 'required'],
        ];
    }
}
