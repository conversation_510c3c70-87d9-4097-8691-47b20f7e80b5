<?php

namespace App\Application\Http\Requests\Admin\Option;

use Illuminate\Foundation\Http\FormRequest;

class UpdateOptionListCategoryRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:45'],
            'backoffice_name' => ['required', 'string', 'max:45'],
            'is_option_required' => ['required', 'boolean'],
            'is_thumbnail_padded' => ['required', 'boolean'],
        ];
    }
}
