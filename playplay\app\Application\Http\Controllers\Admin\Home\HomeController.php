<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Home;

use App\Application\Http\Controllers\Admin\BaseController;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Redirector;

final class HomeController extends BaseController
{
    private Redirector $redirector;

    public function __construct(Redirector $redirector)
    {
        $this->redirector = $redirector;
    }

    public function __invoke(): RedirectResponse
    {
        return $this->redirector->route('admin.projects.index');
    }
}
