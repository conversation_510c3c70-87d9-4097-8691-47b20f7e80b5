<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Stock\GettyTokenRepository as GettyTokenRepositoryInterface;
use App\Infrastructure\Stock\GettyTokenRepository;
use Illuminate\Support\ServiceProvider;

class GettyServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(GettyTokenRepositoryInterface::class, GettyTokenRepository::class);
    }
}
