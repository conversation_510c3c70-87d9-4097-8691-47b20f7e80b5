<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\User;

use Illuminate\Foundation\Http\FormRequest;

final class CheckUserEmailExistenceRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'email' => ['required', 'unique:users,email'],
        ];
    }
}
