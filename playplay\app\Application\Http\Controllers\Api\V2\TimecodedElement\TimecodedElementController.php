<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\TimecodedElement;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\TimecodedElement\TimecodedElementStoreRequest;
use App\Application\Http\Requests\Api\TimecodedElement\TimecodedElementUpdateRequest;
use App\Domain\TimecodedElement\Factories\PresetParamValueFactory;
use App\Domain\TimecodedElement\Repositories\TimecodedElementPresetParamRepository;
use App\Domain\TimecodedElement\Repositories\TimecodedElementRepository;
use App\Domain\TimecodedElement\Serializers\TimecodedElementsSerializerInterface;
use App\Domain\TimecodedElement\Specifications\TimecodedElement\TimecodedElementSpecificationDto;
use App\Domain\TimecodedElement\Specifications\TimecodedElement\TimecodedElementSpecificationInterface;
use App\Domain\TimecodedElement\TimecodedElementPresetParamValueNotFoundException;
use App\Domain\TimecodedElement\TimecodedElementPresetParamValueService;
use App\Domain\TimecodedElement\TimecodedElementService;
use App\Domain\TimecodedElement\ValueObjects\Timecodes;
use App\Domain\TimecodedElement\ValueObjects\Transformations;
use App\Models\ProjectScreen;
use App\Models\TimecodedElement\TimecodedElement;
use App\Models\TimecodedElement\TimecodedElementPresetParam;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

final class TimecodedElementController extends BaseController
{
    use AuthorizesRequests;

    private TimecodedElementService $timecodedElementService;
    private TimecodedElementPresetParamValueService $timecodedElementPresetParamValueService;
    private TimecodedElementsSerializerInterface $timecodedElementsSerializer;
    private TimecodedElementRepository $timecodedElementRepository;
    private TimecodedElementSpecificationInterface $timecodedElementSpecificationValidator;
    private TimecodedElementPresetParamRepository $timecodedElementPresetParamRepository;

    public function __construct(
        TimecodedElementService $timecodedElementService,
        TimecodedElementPresetParamValueService $timecodedElementPresetParamValueService,
        TimecodedElementsSerializerInterface $timecodedElementsSerializer,
        TimecodedElementRepository $timecodedElementRepository,
        TimecodedElementSpecificationInterface $timecodedElementSpecificationValidator,
        TimecodedElementPresetParamRepository $timecodedElementPresetParamRepository,
    ) {
        $this->timecodedElementService = $timecodedElementService;
        $this->timecodedElementsSerializer = $timecodedElementsSerializer;
        $this->timecodedElementRepository = $timecodedElementRepository;
        $this->timecodedElementSpecificationValidator = $timecodedElementSpecificationValidator;
        $this->timecodedElementPresetParamValueService = $timecodedElementPresetParamValueService;
        $this->timecodedElementPresetParamRepository = $timecodedElementPresetParamRepository;
    }

    /**
     * @throws AuthorizationException
     */
    public function index(ProjectScreen $projectScreen): JsonResponse
    {
        $this->authorize('view', [TimecodedElement::class, $projectScreen]);

        $timecodedElements = $this->timecodedElementRepository->getByProjectScreenId($projectScreen->id);

        return $this->sendJsonResponse(
            new Collection($this->timecodedElementsSerializer->serialize($timecodedElements->all())),
            Response::HTTP_OK
        );
    }

    /**
     * @throws AuthorizationException|ValidationException
     */
    public function store(
        ProjectScreen $projectScreen,
        TimecodedElementStoreRequest $request
    ): JsonResponse {
        $timecodedElementPresetId = $request->get('timecoded_element_preset_id');

        $this->authorize('create', [
            TimecodedElement::class,
            $projectScreen,
            $timecodedElementPresetId,
        ]);

        $timecodes = Timecodes::fromArray($request->get('timecodes'));
        $transformations = Transformations::fromArray(
            $request->get('transformations')
        );
        $timecodedElementSpecificationDto = new TimecodedElementSpecificationDto(
            $timecodedElementPresetId,
            $timecodes,
        );

        if (!$this->timecodedElementSpecificationValidator->isValid($timecodedElementSpecificationDto)) {
            throw ValidationException::withMessages(
                $this->timecodedElementSpecificationValidator->getFailureMessages()
            );
        }

        $timecodedElement = $this->timecodedElementService->create(
            $projectScreen,
            $timecodedElementPresetId,
            $timecodes,
            $transformations,
        );

        $timecodedElement = $this->timecodedElementPresetParamValueService->createManyWithDefaultValues(
            $timecodedElement,
            $timecodedElementPresetId
        );

        return $this->sendJsonResponse(
            new Collection(
                $this->timecodedElementsSerializer->serialize(
                    [$timecodedElement]
                )
            ),
            Response::HTTP_CREATED
        );
    }

    /**
     * @throws AuthorizationException|ValidationException
     */
    public function update(
        ProjectScreen $projectScreen,
        TimecodedElement $timecodedElement,
        TimecodedElementUpdateRequest $request
    ): JsonResponse {
        $this->authorize('update', [$timecodedElement, $projectScreen]);

        $timecodes = Timecodes::fromArray($request->get('timecodes'));
        $transformations = Transformations::fromArray($request->get('transformations'));
        $paramValues = $this->getParamValuesFromUpdateRequest($request);
        $timecodedElementSpecificationDto = new TimecodedElementSpecificationDto(
            $timecodedElement->timecoded_element_preset_id,
            $timecodes,
            $paramValues
        );

        if (!$this->timecodedElementSpecificationValidator->isValid($timecodedElementSpecificationDto)) {
            throw ValidationException::withMessages(
                $this->timecodedElementSpecificationValidator->getFailureMessages()
            );
        }

        $timecodedElement = $this->timecodedElementService->update(
            $timecodedElement,
            $timecodes,
            $transformations,
        );

        try {
            $timecodedElement = $this->timecodedElementPresetParamValueService->updateMany(
                $timecodedElement,
                $paramValues
            );
        } catch (TimecodedElementPresetParamValueNotFoundException $exception) {
            throw ValidationException::withMessages([$exception->getMessage()]);
        }

        return $this->sendJsonResponse(
            new Collection(
                $this->timecodedElementsSerializer->serialize(
                    [$timecodedElement]
                ),
            ),
            Response::HTTP_OK
        );
    }

    /**
     * @throws AuthorizationException
     */
    public function destroy(
        ProjectScreen $projectScreen,
        TimecodedElement $timecodedElement,
    ): JsonResponse {
        $this->authorize('destroy', [$timecodedElement, $projectScreen]);

        $this->timecodedElementService->deleteWithEventDispatching($timecodedElement);

        $timecodedElements = $this->timecodedElementRepository->getByProjectScreenId($projectScreen->id);

        return $this->sendJsonResponse(
            new Collection(
                $this->timecodedElementsSerializer->serialize(
                    $timecodedElements->all()
                ),
            ),
            Response::HTTP_OK,
        );
    }

    private function getParamValuesFromUpdateRequest(TimecodedElementUpdateRequest $request): array
    {

        $timecodedElementPresetParamsIds = array_column(
            $request->get('params'),
            'timecoded_element_preset_param_id'
        );
        /** @var Collection<TimecodedElementPresetParam> $timecodedElementPresetParams */
        $timecodedElementPresetParams = $this->timecodedElementPresetParamRepository->getByIds(
            $timecodedElementPresetParamsIds
        );

        return array_map(
            static function (array $paramValue) use ($timecodedElementPresetParams) {
                $timecodedElementPresetParamId = $paramValue['timecoded_element_preset_param_id'];

                $timecodedElementPresetParam = $timecodedElementPresetParams->find(
                    $paramValue['timecoded_element_preset_param_id']
                );

                if ($timecodedElementPresetParam === null) {
                    throw new ModelNotFoundException(
                        'TimecodedElementPresetParam with id ' . $timecodedElementPresetParamId . ' not found'
                    );
                }

                return [
                    'timecoded_element_preset_param_id' => $timecodedElementPresetParamId,
                    'value' => PresetParamValueFactory::fromArray([
                        'type' => $timecodedElementPresetParam->type,
                        'value' => $paramValue['value'],
                    ]),
                ];
            },
            $request->get('params')
        );
    }
}
