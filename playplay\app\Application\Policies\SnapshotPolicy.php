<?php

namespace App\Application\Policies;

use App\Models\Project;
use App\Models\ProjectScreen;
use App\Models\Snapshot;
use App\Models\User;

class SnapshotPolicy
{
    public function create(User $user, Project|ProjectScreen $parent): bool
    {
        $project = $parent instanceof Project ? $parent : $parent->project;

        return $user->can('view', $project);
    }

    public function view(User $user, Snapshot $snapshot): bool
    {
        $project = $snapshot->project;

        return $user->can('view', $project);
    }
}
