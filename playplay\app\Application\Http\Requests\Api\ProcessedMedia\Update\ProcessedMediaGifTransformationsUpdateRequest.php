<?php

namespace App\Application\Http\Requests\Api\ProcessedMedia\Update;

use App\Domain\Render\RenderMedia\Transformation\KeepSizeValue;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProcessedMediaGifTransformationsUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function messages(): array
    {
        return [
            'crop.required_without' => 'The crop field is required when keepSize is not present',
            'keepSize.required_without' => 'The keepSize field is required when crop is not present',
            'keepSize.in' => 'The selected keepSize is invalid: '.implode('|', KeepSizeValue::getAllValues()),
        ];
    }

    public function rules(): array
    {
        return [
            'crop' => ['nullable', 'required_without:keepSize', 'crop'],
            'param_id' => ['required', 'numeric', 'exists:layout_params,id', 'paramIsInProject'],
            'keepSize' => ['nullable', 'required_without:crop', Rule::in(KeepSizeValue::getAllValues())],
        ];
    }
}
