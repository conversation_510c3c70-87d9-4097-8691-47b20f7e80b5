<?php

declare(strict_types=1);

namespace App\Application\Console\Commands\Team;

use App\Domain\Common\Exceptions\EntityNotFoundException;
use App\Domain\Team\TeamRepository;
use Illuminate\Console\Command;

final class AttachManyScreensToManyTeams extends Command
{
    protected $signature = 'teams:attach-screens';

    protected $description = 'Bulk attach multiple screens to one or multiple teams';

    private TeamRepository $teamRepository;

    public function __construct(TeamRepository $teamRepository)
    {
        parent::__construct();
        $this->teamRepository = $teamRepository;
    }

    public function handle(): int
    {
        $screensIds = array_filter(explode(',', $this->ask('List of screens IDs separated by a comma (e.g "1,2,3")')));
        $teamsIds = array_filter(explode(',', $this->ask('List of teams IDs separated by a comma (e.g "1,2,3")')));
        $erroredTeams = [];

        $start = microtime(true);
        foreach ($teamsIds as $teamId) {
            try {
                $team = $this->teamRepository->getById((int) $teamId);
            } catch (EntityNotFoundException $exception) {
                $erroredTeams[] = $teamId;
                continue;
            }

            $team->screens()->syncWithoutDetaching($screensIds);
        }

        $end = microtime(true);

        if ($erroredTeams !== []) {
            $this->error(
                'The following team IDs (' . count($erroredTeams) . ') were not found: ' . implode(',', $erroredTeams)
            );
        }

        $this->info(
            'Successfully attached '
            . count($screensIds)
            . ' screen(s) to '
            . (count($teamsIds) - count($erroredTeams))
            . ' team(s) in '
            . round(($end - $start), 2)
            . ' seconds'
        );

        return 0;
    }
}
