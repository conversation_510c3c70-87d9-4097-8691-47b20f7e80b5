<?php

declare(strict_types=1);

namespace App\Application\Events;

final class CompanyFeatureUpdated
{
    public readonly int $companyId;
    /** @var int[] $updatedFeaturesIds */
    public readonly array $updatedFeaturesIds;

    /** @param int[] $updatedFeaturesIds */
    public function __construct(int $companyId, array $updatedFeaturesIds)
    {
        $this->companyId = $companyId;
        $this->updatedFeaturesIds = $updatedFeaturesIds;
    }
}
