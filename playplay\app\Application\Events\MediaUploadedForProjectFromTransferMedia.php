<?php

declare(strict_types=1);

namespace App\Application\Events;

use App\Models\RawMedia;
use Illuminate\Broadcasting\Channel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

final class MediaUploadedForProjectFromTransferMedia implements ShouldBroadcast
{
    private int $projectId;
    private RawMedia $rawMedia;

    public function __construct(RawMedia $rawMedia, int $projectId)
    {
        $this->projectId = $projectId;
        $this->rawMedia = $rawMedia;
    }

    public function broadcastAs(): string
    {
        return 'media-uploaded-from-transfer-media';
    }

    public function broadcastOn(): Channel
    {
        return new Channel("media-uploaded-from-transfer-media-for-project.{$this->projectId}");
    }

    public function broadcastWith(): array
    {
        return [
            'rawMedia' => [
                'id' => $this->rawMedia->id,
                'name' => $this->rawMedia->name,
                'stock_id' => $this->rawMedia->stock_id,
                'status' => $this->rawMedia->status,
                'source' => $this->rawMedia->source->value,
                'browser_url' => $this->rawMedia->browser_url,
                'thumbnail_url' => $this->rawMedia->thumbnail_url,
                'audio_peaks_data_url' => $this->rawMedia->audio_peaks_data_url,
                'type' => $this->rawMedia->type->value,
                'timestamp' => $this->rawMedia->getPivotUpdatedAtAttribute(),
                'data' => $this->rawMedia->data?->toArray(),
                'relation_type' => $this->rawMedia->getPivotRelationTypeAttribute(),
                'duration' => $this->rawMedia->duration,
                'height' => $this->rawMedia->height,
                'width' => $this->rawMedia->width,
            ],
        ];
    }
}
