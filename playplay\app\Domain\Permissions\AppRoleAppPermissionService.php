<?php

declare(strict_types=1);

namespace App\Domain\Permissions;

use App\Models\Permissions\AppRole;

class AppRoleAppPermissionService
{
    public function appPermissionIdsBelongToAppRole(array $appPermissionIds, AppRole $appRole): bool
    {
        return $appRole->appPermissions->whereIn('id', $appPermissionIds)->count() === count($appPermissionIds);
    }

    public function mandatoryAppPermissionIdsArePresentForAppRole(array $appPermissionIds, AppRole $appRole): bool
    {
        $mandatoryAppPermissions = $appRole
            ->appPermissions
            ->where('pivot.is_optional', false)
            ->pluck('id')
            ->toArray();

        return count(array_intersect($mandatoryAppPermissions, $appPermissionIds)) === count($mandatoryAppPermissions);
    }
}
