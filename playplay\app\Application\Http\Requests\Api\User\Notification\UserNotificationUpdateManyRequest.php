<?php

namespace App\Application\Http\Requests\Api\User\Notification;

use Illuminate\Foundation\Http\FormRequest;

class UserNotificationUpdateManyRequest extends FormRequest
{
    public static function getRules(): array
    {
        return [
            'notifications' => ['required', 'array'],
        ];
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return static::getRules();
    }
}
