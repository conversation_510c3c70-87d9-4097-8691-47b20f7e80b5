<?php

namespace App\Application\Http\Requests\Api\Folder;

use Illuminate\Foundation\Http\FormRequest;

class ProjectMoveFolderRequest extends FormRequest
{
    public static function rules(): array
    {
        return [
            'folder_id' => ['nullable', 'exists:folders,id'],
            'project_ids' => ['array', 'min:1', 'exists:projects,id'],
        ];
    }

    public function authorize(): bool
    {
        return true;
    }
}
