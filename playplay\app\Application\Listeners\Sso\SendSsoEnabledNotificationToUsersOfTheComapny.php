<?php

declare(strict_types=1);

namespace App\Application\Listeners\Sso;

use App\Application\Events\SsoEnabledForCompany;
use App\Domain\User\Onboarding\NotificationService;

final class SendSsoEnabledNotificationToUsersOfTheComapny
{
    private NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    public function handle(SsoEnabledForCompany $event): void
    {
        $this->notificationService->sendSsoEnabledNotificationToUsersOfTheCompany($event->getCompany());
    }
}
