<?php

declare(strict_types=1);

namespace App\Application\Http\Mappers\CutawayShot;

use App\Application\Exceptions\InvalidFormRequestMappingException;
use App\Application\Http\Requests\Api\Project\ProjectScreen\Param\CutawayShot\StoreCutawayShotImageRequest;
use App\Domain\CutawayShot\CutawayShotImageRenderMediaData;
use App\Domain\Render\RenderMedia\Crop\CropFactory;
use App\Domain\Render\RenderMedia\TargetDimension;
use App\Models\ProjectScreen;
use App\Models\ScreenParams\ParamCutawayShot;
use Exception;
use Illuminate\Routing\Route;
use InvalidArgumentException;
use TypeError;

final class CutawayShotImageRenderMediaDataMapper
{
    public function fromRequest(StoreCutawayShotImageRequest $request): CutawayShotImageRenderMediaData
    {
        /** @var Route $route */
        $route = $request->route();
        /** @var ?ParamCutawayShot $param */
        $param = $route->parameter('param');
        if ($param === null) {
            throw new InvalidArgumentException('Route parameter "param" is missing');
        }

        /** @var ?ProjectScreen $projectScreen */
        $projectScreen = $route->parameter('project_screen');
        if ($projectScreen === null) {
            throw new InvalidArgumentException('Route parameter "project_screen" is missing');
        }

        $format = $projectScreen->project->normalized_format;

        try {
            return new CutawayShotImageRenderMediaData(
                CropFactory::createCropFromArray($request->get('crop')),
                $request->get('duration'),
                $request->get('keep_size'),
                $request->get('start'),
                new TargetDimension(
                    $param->maxHeight($format),
                    $param->maxWidth($format),
                )
            );
        } catch (TypeError | Exception) {
            throw new InvalidFormRequestMappingException($request, self::class);
        }
    }
}
