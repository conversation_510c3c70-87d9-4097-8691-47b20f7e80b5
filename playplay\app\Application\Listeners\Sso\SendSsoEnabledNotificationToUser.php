<?php

declare(strict_types=1);

namespace App\Application\Listeners\Sso;

use App\Application\Events\SsoEnabledForUser;
use App\Domain\User\Onboarding\NotificationService;

final class SendSsoEnabledNotificationToUser
{
    private NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    public function handle(SsoEnabledForUser $event): void
    {
        $this->notificationService->sendSsoEnabledNotificationToUser($event->getUser());
    }
}
