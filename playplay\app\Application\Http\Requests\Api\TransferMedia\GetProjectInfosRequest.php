<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\TransferMedia;

use Illuminate\Foundation\Http\FormRequest;

final class GetProjectInfosRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'uuid' => ['required', 'uuid', 'exists:projects,uuid,deleted_at,NULL'],
        ];
    }
}
