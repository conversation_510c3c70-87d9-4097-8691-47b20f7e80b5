<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing;

use App\Application\Providers\Routing\Api\BoostRouteMapper;
use App\Application\Providers\Routing\Api\DataSeedingRouteMapper;
use App\Application\Providers\Routing\Api\FreeTrialSignupRouteMapper;
use App\Application\Providers\Routing\Api\MetricsRouteMapper;
use App\Application\Providers\Routing\Api\HealthzRouteMapper;
use App\Application\Providers\Routing\Api\TransferMediaPublicRouteMapper;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider;
use Illuminate\Support\Facades\Route;

final class ApiPublicRoutingProvider extends RouteServiceProvider
{
    /** @var RouteMapper[] */
    private array $routeMappers;

    public function __construct(Application $app)
    {
        parent::__construct($app);
        // Declare all route mappers
        $this->routeMappers = [
            new BoostRouteMapper(),
            new DataSeedingRouteMapper(),
            new FreeTrialSignupRouteMapper(),
            new MetricsRouteMapper(),
            new TransferMediaPublicRouteMapper(),
        ];
    }

    public function map(): void
    {
        Route::group([
            'middleware' => ['api'],
            'prefix' => 'api',
            'as' => 'api.',
        ], function () {
            foreach ($this->routeMappers as $routeMapper) {
                $routeMapper->map();
            }
        });
        Route::group([
            'prefix' => 'api',
        ], static function () {
            (new HealthzRouteMapper())->map();
        });
    }
}
