<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Admin;

use App\Application\Http\Controllers\Admin\ConfigController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class ConfigRouteMapper implements RouteMapper
{

    public function map(): void
    {
        Route::group([
            'prefix' => '/configs',
            'as' => 'configs.'
        ], static function (Router $router) {
            $router->get('/', [ConfigController::class, 'index'])->name('index');
            $router->post('/', [ConfigController::class, 'store'])->name('store');
            $router->get('/create', [ConfigController::class, 'create'])->name('create');
            $router->put('/{config}', [ConfigController::class, 'update'])->name('update');
            $router->delete('/{config}', [ConfigController::class, 'destroy'])->name('destroy');
            $router->get('/{config}/edit', [ConfigController::class, 'edit'])->name('edit');
            $router->get('/{config}/danger-zone', [ConfigController::class, 'showDangerZone'])
                ->name('danger-zone');
        });
    }
}
