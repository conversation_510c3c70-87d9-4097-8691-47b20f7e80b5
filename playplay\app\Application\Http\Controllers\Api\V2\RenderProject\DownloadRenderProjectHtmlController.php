<?php

namespace App\Application\Http\Controllers\Api\V2\RenderProject;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\RenderProject\DownloadRenderProjectHtmlRequest;
use App\Domain\Project\ProjectFormat;
use App\Domain\Render\RenderProject\RenderProjectDownloadService;
use App\Domain\Render\RenderProject\RenderProjectDownloadUrlNotFoundException;
use App\Domain\Render\RenderProject\RenderStoryDownloadService;
use App\Models\Project;
use App\Models\User;
use App\Models\VideoDownload;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

final class DownloadRenderProjectHtmlController extends BaseController
{
    use AuthorizesRequests;

    private Guard $guard;
    private RenderProjectDownloadService $downloadService;
    private RenderStoryDownloadService $renderStoryDownloadService;

    public function __construct(
        Guard $guard,
        RenderProjectDownloadService $downloadService,
        RenderStoryDownloadService $renderStoryDownloadService
    ) {
        $this->guard = $guard;
        $this->downloadService = $downloadService;
        $this->renderStoryDownloadService = $renderStoryDownloadService;
    }

    public function __invoke(
        Project $project,
        DownloadRenderProjectHtmlRequest $request,
    ): JsonResponse {

        /** @var User $user */
        $user = $this->guard->user();
        $this->authorize('download', $project);

        try {
            $render = $project->lastRenderProjectHtmlProcessed;
            if ($render === null) {
                throw new RenderProjectDownloadUrlNotFoundException(
                    'no generated video has been found for this project'
                );
            }

            $downloadType = $request->get('type', VideoDownload::TYPE_DIRECT);
            $nth = $request->get('nth');

            if ($render->format === ProjectFormat::STORY) {
                $status = $this->renderStoryDownloadService->getRenderStoryStatus($render);

                $url = $this->renderStoryDownloadService->getProjectRenderStoryDownloadUrl(
                    $render,
                    $user,
                    $downloadType,
                    $nth
                );
            } else {
                $status = $render->status;
                $url = $this->downloadService->getRenderProjectDownloadUrl(
                    $render,
                    $user,
                    $downloadType
                );
            }
        } catch (RenderProjectDownloadUrlNotFoundException $e) {
            throw new NotFoundHttpException($e->getMessage(), $e);
        }

        return $this->sendJsonResponse(
            new Collection([
                [
                    'status' => $status,
                    'url' => $url,
                ],
            ]),
            Response::HTTP_OK
        );
    }
}
