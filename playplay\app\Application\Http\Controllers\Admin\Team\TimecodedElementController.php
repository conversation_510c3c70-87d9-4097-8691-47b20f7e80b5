<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Team;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\Team\TimecodedElement\TeamTimecodedElementsFamilyStoreRequest;
use App\Domain\TimecodedElement\Repositories\TeamTimecodedElementPresetRepository;
use App\Domain\TimecodedElement\Repositories\TimecodedElementsFamilyRepository;
use App\Domain\TimecodedElement\TimecodedElementsFamilyService;
use App\Domain\TimecodedElement\TimecodedElementsFamilyType;
use App\Models\Team;
use App\Models\TimecodedElement\TimecodedElementsFamily;
use Illuminate\Contracts\View\Factory as ViewFactory;
use Illuminate\Contracts\View\View;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\RedirectResponse;

final class TimecodedElementController extends BaseController
{
    private const TAB_TIMECODED_ELEMENTS = 'timecoded-elements';
    private ViewFactory $viewFactory;
    private TimecodedElementsFamilyRepository $timecodedElementsFamilyRepository;
    private TeamTimecodedElementPresetRepository $teamTimecodedElementPresetRepository;
    private TimecodedElementsFamilyService $timecodedElementsFamilyService;
    private Redirector $redirector;

    public function __construct(
        ViewFactory $viewFactory,
        TimecodedElementsFamilyRepository $timecodedElementsFamilyRepository,
        TeamTimecodedElementPresetRepository $teamTimecodedElementPresetRepository,
        TimecodedElementsFamilyService $timecodedElementsFamilyService,
        Redirector $redirector,
    ) {
        $this->viewFactory = $viewFactory;
        $this->timecodedElementsFamilyRepository = $timecodedElementsFamilyRepository;
        $this->teamTimecodedElementPresetRepository = $teamTimecodedElementPresetRepository;
        $this->timecodedElementsFamilyService = $timecodedElementsFamilyService;
        $this->redirector = $redirector;
    }

    public function edit(Team $team): View
    {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('update', $team);

        $timecodedElementsFamilies = $this->timecodedElementsFamilyRepository->getAll();
        $attachedTimecodedElementsFamilies = $this->timecodedElementsFamilyRepository->getByTeamIdWithActivatedPresets(
            $team->id
        );
        $attachedTimecodedElementsFamiliesGroupedByType = $attachedTimecodedElementsFamilies->groupBy(
            fn(TimecodedElementsFamily $timecodedElementsFamily) => $timecodedElementsFamily->type->value
        );
        $timecodedElementsFamiliesGroupedByType = $this->getFilteredFamiliesGroupedByTypeDropdown(
            $timecodedElementsFamilies,
            $attachedTimecodedElementsFamilies
        );
        $teamTimecodedElementPresets = $this->teamTimecodedElementPresetRepository->getByTeamId($team->id);

        return $this->viewFactory->make('admin.teams.timecoded-elements.edit', [
            'tabActive' => self::TAB_TIMECODED_ELEMENTS,
            'team' => $team,
            'timecodedElementsFamiliesTypes' => TimecodedElementsFamilyType::values(),
            'attachedTimecodedElementsFamiliesGroupedByType' => $attachedTimecodedElementsFamiliesGroupedByType,
            'timecodedElementsFamiliesGroupedByType' => $timecodedElementsFamiliesGroupedByType,
            'teamTimecodedElementPresetIds' => $teamTimecodedElementPresets->pluck('timecoded_element_preset_id')
        ]);
    }

    public function store(Team $team, TeamTimecodedElementsFamilyStoreRequest $request): RedirectResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('update', $team);

        $this->timecodedElementsFamilyService->attachToTeam(
            (int) $request->get('timecoded_elements_family_id'),
            $team->id
        );

        return $this->redirector->route('admin.teams.timecoded-elements.edit', ['team' => $team]);
    }

    public function destroy(Team $team, int $timecodedElementsFamilyId): RedirectResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('update', $team);

        $this->timecodedElementsFamilyService->detachFromTeam(
            $timecodedElementsFamilyId,
            $team->id
        );

        return $this->redirector->route('admin.teams.timecoded-elements.edit', ['team' => $team]);
    }

    private function getFilteredFamiliesGroupedByTypeDropdown(
        Collection $timecodedElementsFamilies,
        Collection $timecodedElementsFamiliesToFilter
    ): array {
        $timecodedElementsFamilies = $timecodedElementsFamilies->diff($timecodedElementsFamiliesToFilter);
        $filteredFamiliesDropdown = [];
        /** @var TimecodedElementsFamily $timecodedElementsFamily */
        foreach ($timecodedElementsFamilies as $timecodedElementsFamily) {
            $filteredFamiliesDropdown[$timecodedElementsFamily->type->value][$timecodedElementsFamily->id] =
                $timecodedElementsFamily->backoffice_name;
        }

        return $filteredFamiliesDropdown;
    }
}
