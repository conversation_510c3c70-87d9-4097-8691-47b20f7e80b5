<?php
declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Billing;

use App\Application\Http\Controllers\Api\V2\Billing\SubscriptionChurnController;
use App\Application\Http\Controllers\Api\V2\Billing\SubscriptionController;
use App\Application\Http\Controllers\Api\V2\Billing\UpdateSubscriptionChurnController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class SubscriptionRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'as' => 'subscriptions.',
        ], static function (Router $router) {
            $router->post('subscriptions', [SubscriptionController::class, 'store'])->name('store');

            $router->post(
                'confirm-subscription/{stripeSubscriptionId}',
                [SubscriptionController::class, 'confirm']
            )->name('confirm');

            $router->post(
                'subscriptions/{subscription}/churn',
                SubscriptionChurnController::class,
            )->name('churn');

            $router->put(
                'subscriptions/{subscription}/churn',
                UpdateSubscriptionChurnController::class,
            )->name('update-churn');
        });
    }
}
