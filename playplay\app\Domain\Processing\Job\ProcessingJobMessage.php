<?php

declare(strict_types=1);

namespace App\Domain\Processing\Job;

final class ProcessingJobMessage
{
    public readonly string $type;
    public readonly string $folder;
    public readonly string $callback;
    public readonly array $data;

    public function __construct(string $type, string $folder, string $callback, array $data)
    {
        $this->type = $type;
        $this->folder = $folder;
        $this->callback = $callback;
        $this->data = $data;
    }

    public function toArray(): array
    {
        return [
            'type' => $this->type,
            'folder' => $this->folder,
            'callback' => $this->callback,
            'data' => $this->data,
        ];
    }
}
