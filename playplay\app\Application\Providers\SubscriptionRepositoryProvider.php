<?php

namespace App\Application\Providers;

use App\Domain\Billing\CompanyChurnRepository;
use App\Domain\Billing\SubscriptionRepository as BillingSubscriptionRepository;
use App\Domain\Churn\ChurnRepository;
use App\Domain\Subscription\SubscriptionRepository;
use App\Infrastructure\Billing\EloquentCompanyChurnRepository;
use App\Infrastructure\Billing\StripeSubscriptionRepository;
use App\Infrastructure\Churn\PlanhatChurnRepository;
use App\Infrastructure\Subscription\EloquentSubscriptionRepository;
use Illuminate\Support\ServiceProvider;

final class SubscriptionRepositoryProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(SubscriptionRepository::class, EloquentSubscriptionRepository::class);
        $this->app->bind(BillingSubscriptionRepository::class, StripeSubscriptionRepository::class);
        $this->app->bind(CompanyChurnRepository::class, EloquentCompanyChurnRepository::class);
        $this->app->bind(ChurnRepository::class, PlanhatChurnRepository::class);
    }
}
