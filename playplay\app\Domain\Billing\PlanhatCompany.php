<?php

declare(strict_types=1);

namespace App\Domain\Billing;

final class PlanhatCompany
{
    public readonly string $id;

    public readonly string $playplayCompanyId;

    private function __construct(string $id, string $playplayCompanyId)
    {
        $this->id = $id;
        $this->playplayCompanyId = $playplayCompanyId;
    }

    public static function fromArray(array $data): self
    {
        return new self($data['_id'], $data['externalId']);
    }
}
