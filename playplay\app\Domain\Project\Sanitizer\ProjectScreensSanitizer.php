<?php

declare(strict_types=1);

namespace App\Domain\Project\Sanitizer;

use App\Domain\ProjectScreen\ProjectScreenDeleteRepository;
use App\Domain\ProjectScreen\Sanitizer\ProjectScreenSanitizer;
use App\Models\Project;
use Illuminate\Contracts\Auth\Guard;

final class ProjectScreensSanitizer implements ProjectElementsSanitizer
{
    private ProjectScreenSanitizer $projectScreenSanitizer;
    private ProjectScreenDeleteRepository $projectScreenDeleteRepository;
    private Guard $auth;
    private TimecodedElementSanitizer $timecodedElementSanitizer;

    public function __construct(
        ProjectScreenSanitizer $projectScreenSanitizer,
        ProjectScreenDeleteRepository $projectScreenDeleteRepository,
        Guard $auth,
        TimecodedElementSanitizer $timecodedElementSanitizer,
    ) {
        $this->projectScreenSanitizer = $projectScreenSanitizer;
        $this->projectScreenDeleteRepository = $projectScreenDeleteRepository;
        $this->auth = $auth;
        $this->timecodedElementSanitizer = $timecodedElementSanitizer;
    }

    public function sanitize(Project $project): bool
    {
        $hasProjectBeenUpdated = false;

        /**
         * @todo Take in consideration these 2 lines to update the flag $hasProjectBeenUpdated
         *       It doesn't make sens to say a project is updated when a screen is
         *       updated but not when a screen is removed
         */
        $this->sanitizeUnavailableScreensInProjectTeam($project);
        $this->sanitizeUnavailableScreensInProjectFormat($project);

        foreach ($project->projectScreens as $projectScreen) {
            if ($this->projectScreenSanitizer->sanitize($projectScreen)) {
                $hasProjectBeenUpdated = true;
            }
        }

        if ($this->timecodedElementSanitizer->sanitize($project)) {
            $hasProjectBeenUpdated = true;
        }

        return $hasProjectBeenUpdated;
    }

    public function sanitizeUnavailableScreensInProjectFormat(Project $project): void
    {
        $this->projectScreenDeleteRepository->deleteProjectScreensWhenScreensAreUnavailableInProjectFormat($project);
        $project->reorderScreens();
    }

    public function sanitizeUnavailableScreensInProjectTeam(Project $project): void
    {
        if ($this->shouldDeleteScreens($project)) {
            $this->projectScreenDeleteRepository->deleteScreensFromProjectWhenNotAvailableInTeam($project);
            $project->fresh()->reorderScreens();
        }
    }

    private function shouldDeleteScreens(Project $project): bool
    {
        $connectedUser = $this->auth->user();
        if ($connectedUser === null || $connectedUser->is_admin) {
            return false;
        }

        return $project->template_id === null || !$project->template->has_only_assigned_screens;
    }
}
