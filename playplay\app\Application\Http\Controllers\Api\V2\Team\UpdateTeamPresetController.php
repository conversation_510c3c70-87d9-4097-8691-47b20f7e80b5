<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Team;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Team\TeamPresetUpdateRequest;
use App\Domain\Project\ProjectFormat;
use App\Domain\ProjectScreen\WysiwygColorConverter;
use App\Domain\TeamPreset\TeamPresetService;
use App\Models\Team;
use App\Models\TeamPreset;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;

class UpdateTeamPresetController extends BaseController
{
    use AuthorizesRequests;

    private TeamPresetService $teamPresetService;
    private WysiwygColorConverter $colorConverter;

    public function __construct(TeamPresetService $teamPresetService, WysiwygColorConverter $colorConverter)
    {
        $this->teamPresetService = $teamPresetService;
        $this->colorConverter = $colorConverter;
    }

    /**
     * @throws AuthorizationException
     */
    public function __invoke(Team $team, TeamPresetUpdateRequest $request): JsonResponse
    {
        $processedMediaIds = $request->input('logos.*.*', []) + $request->input('outros.*.*', []);

        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('update-preset', [$team, $processedMediaIds]);

        /** @var TeamPreset $preset */
        $preset = $team->presets()->first();

        if ($request->has('logos')) {
            $preset = $this->updateTeamPresetLogos($preset, $request);
        }

        if ($request->has('colors')) {
            $preset = $this->updateTeamPresetColors($preset, $request);
        }

        if ($request->has('outros')) {
            /** @todo check if canUpload === true */
            $preset = $this->teamPresetService->updateTeamPresetOptionalOutros($preset, $request->get('outros', []));
        }

        $preset->save();

        return $this->sendJsonResponse(new Collection([$preset]), JsonResponse::HTTP_OK);
    }

    /** @todo Move this code into a service and inject Colors instead of Request */
    private function updateTeamPresetColors(TeamPreset $preset, TeamPresetUpdateRequest $request): TeamPreset
    {
        if ($request->get('colors') === null) {
            // If colors is null, we clear the previous value
            $preset->colors = [];
        } else {
            $newColors = $preset->colors === [] ? TeamPreset::COLORS_STRUCTURE : $preset->colors;
            $newColorsValues = (new Collection($request->get('colors')))->map(function ($color) {
                return $this->colorConverter->hexToRgb($color);
            });
            data_set($newColors, 'main.values', $newColorsValues);
            data_set($newColors, 'main.default', $newColorsValues[0]);
            data_set($newColors, 'word.values', $newColorsValues);
            data_set($newColors, 'word.default', $newColorsValues[1]);
            $preset->colors = $newColors;
        }

        return $preset;
    }

    /** @todo Move this code into a service and inject Logos instead of Request */
    private function updateTeamPresetLogos(TeamPreset $preset, TeamPresetUpdateRequest $request): TeamPreset
    {
        $newLogos = $preset->logos;
        $logos = $request->get('logos', []);

        foreach ($logos as $type => $logo) {
            $newLogos[$type]['logos'] = (new Collection($logos[$type]))->map(function ($logoId) use ($preset, $type) {
                if ($preset->isLogoAutoResize($logoId, $type)) {
                    return ['auto_resize' => true] + array_fill_keys(
                        ProjectFormat::FORMATS,
                        ['processed_media_id' => $logoId]
                    );
                }

                $logos = [];
                foreach (ProjectFormat::FORMATS as $format) {
                    $sizes = $preset->getLogoSize($logoId, $type, $format);
                    $logos[$format] = [
                        'processed_media_id' => $logoId,
                    ];
                    if ($sizes) {
                        $logos[$format]['width'] = data_get($sizes, 'width');
                        $logos[$format]['height'] = data_get($sizes, 'height');
                    }
                }

                return $logos;
            });
        }

        $preset->logos = $newLogos;

        return $preset;
    }
}
