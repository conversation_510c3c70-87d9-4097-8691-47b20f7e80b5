<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\Team;

use App\Domain\Screen\Parameters\OptionListCategoryRepository;
use App\Domain\Screen\Parameters\OptionRepository;
use App\Domain\Team\TeamBackgroundOptionsSerializer;
use App\Models\Screen\Parameters\Option;
use App\Models\Team;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Routing\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;

final class GetTeamBackgroundOptionsController extends Controller
{
    use AuthorizesRequests;

    private OptionRepository $optionRepository;
    private OptionListCategoryRepository $categoryRepository;
    private TeamBackgroundOptionsSerializer $teamBackgroundOptionsSerializer;

    public function __construct(
        OptionRepository $optionRepository,
        OptionListCategoryRepository $categoryRepository,
        TeamBackgroundOptionsSerializer $teamBackgroundOptionsSerializer
    ) {
        $this->optionRepository = $optionRepository;
        $this->categoryRepository = $categoryRepository;
        $this->teamBackgroundOptionsSerializer = $teamBackgroundOptionsSerializer;
    }

    public function __invoke(Team $team): JsonResponse
    {
        $this->authorize('view', $team);

        $optionListCategory = $this->categoryRepository->getByName('Backgrounds');
        $options = $this->optionRepository->getAvailableOptionsForTeamAndCategory($team, $optionListCategory);

        return new JsonResponse($this->teamBackgroundOptionsSerializer->serialize($options));
    }
}
