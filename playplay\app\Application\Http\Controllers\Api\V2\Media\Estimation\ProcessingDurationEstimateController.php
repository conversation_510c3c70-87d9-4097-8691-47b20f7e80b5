<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Media\Estimation;

use App\Application\Http\Requests\Api\RawMedia\Estimation\ProcessingDurationEstimateRequest;
use App\Domain\RawMedia\Estimation\ProcessingDurationEstimateParameters;
use App\Infrastructure\RawMedia\Estimation\ProcessingDurationEstimateContext;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

final class ProcessingDurationEstimateController
{
    public function __invoke(ProcessingDurationEstimateRequest $request): JsonResponse
    {
        $processingDurationEstimateParameters = new ProcessingDurationEstimateParameters(
            $request->input('type'),
            (int) $request->input('size'),
            (int) $request->input('duration')
        );

        $context = new ProcessingDurationEstimateContext($processingDurationEstimateParameters);

        return new JsonResponse(
            ['estimatedProcessingDuration' => $context->estimateProcessingDurationInSeconds()],
            Response::HTTP_OK
        );
    }
}
