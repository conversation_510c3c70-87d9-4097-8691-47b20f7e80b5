<?php

namespace App\Application\Http\Requests\Api\Project\ProjectScreen;

use Illuminate\Foundation\Http\FormRequest;

class ProjectScreenUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'has_transition_after' => ['boolean'],
            'params' => ['bail', 'sometimes', 'required', 'checkParamAreValid'],
            'params.*.param_id' => ['required', 'isInScreen'],
            'params.*.value.*.options.dark_filter' => ['sometimes', 'nullable', 'numeric', 'between:0,1'],
            'params.*.value' => ['present'],
            'params.*.value.*.value' => ['sometimes', 'valueIsCorrect'],
            // TODO: remove after kill mobile
            'order' => ['sometimes', 'required', 'numeric', 'min:0', 'orderIsCorrect'],
            'params.*.nth_layout' => ['required', 'integer', 'gte:0', 'lte:255'],
            'settings' => ['checkAudioLevels', 'checkSettings'],
            'settings.show_logos' => [
                'nullable',
                'bool',
            ],
            'settings.screen_duration' => [
                'nullable',
                'string',
                'in:very_slow,slow,fast,regular,very_fast,',
            ],
            'colors' => ['checkColors'],
            'screen_id' => ['sometimes', 'integer', 'exists:screens,id'],
        ];
    }
}
