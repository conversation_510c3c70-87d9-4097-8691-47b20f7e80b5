<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\User;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Mappers\UserTeam\TeamAppRoleMapper;
use App\Application\Http\Requests\Admin\User\UserTeamsUpdateRequest;
use App\Domain\Permissions\AppRoleRepository;
use App\Domain\UserTeam\Serializer\UserTeamAppRoleSerializer;
use App\Infrastructure\User\UserTransferService;
use App\Models\User;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;

final class UserTeamController extends BaseController
{
    private UserTransferService $userTransferService;
    private AppRoleRepository $appRoleRepository;
    private TeamAppRoleMapper $teamAppRoleMapper;
    private UserTeamAppRoleSerializer $userTeamAppRoleSerializer;

    public function __construct(
        UserTransferService $userTransferService,
        AppRoleRepository $appRoleRepository,
        TeamAppRoleMapper $teamAppRoleMapper,
        UserTeamAppRoleSerializer $userTeamAppRoleSerializer
    ) {
        $this->authorizeResource(User::class);
        $this->userTransferService = $userTransferService;
        $this->appRoleRepository = $appRoleRepository;
        $this->teamAppRoleMapper = $teamAppRoleMapper;
        $this->userTeamAppRoleSerializer = $userTeamAppRoleSerializer;
    }

    public function show(User $user): View
    {
        $this->authorize('canAccessRestrictedData', $user->company);

        $allTeams = $user->company === null ? [] : $user->company->teams->pluck('name', 'id')->toArray();
        $roles = $this->appRoleRepository->getAllAvailableAppRolesWithAppPermissions();

        return view('admin.users.team', [
            'user' => $user,
            'actualTeams' => $user->teams,
            'allTeams' => $allTeams,
            'roles' => $roles,
            'serializedUserTeamAppRoles' => $this->userTeamAppRoleSerializer->serialize($user->userTeamAppRoles),
        ]);
    }

    public function updateTeamAndCompany(UserTeamsUpdateRequest $request, User $user): RedirectResponse
    {
        $this->authorize('canAccessRestrictedData', $user->company);
        $this->authorize('update', $user);

        $companyId = intVal($request->get('company_id', $user->company_id));
        $teamsRoles = $this->teamAppRoleMapper->fromRequest($request);

        $this->userTransferService->transferUserToCompanyAndTeams($user, $companyId, $teamsRoles);

        return redirect()->back();
    }
}
