<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\TransferMedia;

use Illuminate\Foundation\Http\FormRequest;

final class GetCdnUrlOfMediaToBeTransferredRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'uuid' => ['required', 'uuid', 'exists:projects,uuid,deleted_at,NULL'],
            'size' => ['required', 'integer', 'min:1'],
            'hash' => ['required', 'string'],
            'extension' => ['required', 'string'],
            'content_type' => ['required', 'string'],
        ];
    }
}
