<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\Project\ProjectScreen\Param\CutawayShot;

use App\Domain\Render\RenderMedia\Transformation\KeepSizeValue;
use Illuminate\Validation\Rule;

class StoreCutawayShotVideoRequest extends CutawayShotRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'raw_media_id' => ['required', 'isVideo'],
            'source' => [
                'required',
                Rule::in($this->getAllowedSources()),
            ],
            'crop' => ['required', 'crop'],
            'keep_size' => ['present', 'nullable', Rule::in(KeepSizeValue::getAllValues())],
            'start' => ['required', 'numeric', 'min:0'],
            'trim_start' => ['required', 'numeric', 'min:0'],
            'trim_end' => [
                'required',
                'numeric',
                'isValidCutawayShotItemDuration',
            ],
        ];
    }
}
