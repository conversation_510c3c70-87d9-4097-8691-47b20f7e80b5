<?php

declare(strict_types=1);

namespace App\Application\Html;

final class MenuConfiguration
{
    /**
     * Menus with [$route, $label, $permission, $submenu]
     * $route can be an array with [$routeName, $parameters]
     * $parameters can be a callback if needed (for auth() usage for example)
     * $label can be an array with [$label, $fontAwesomeClass]
     * $permission can be null : the entry will always be shown
     * $submenu (optional) It's a list of menus with [$route, $label, $permission, $submenu]
     */
    public const ELEMENTS = [
        ['admin.users.index', ['Users', 'users'], 'manage-users'],
        ['admin.companies.index', ['Companies', 'building'], 'manage-companies'],
        [
            'admin.screens.index',
            ['Motion', 'archive'],
            null,
            [
                ['admin.screens.index', 'Screens', null],
                ['admin.screens.versions', 'Screen Versions', 'manage-screen-versions'],
                ['admin.templates.index', 'Templates', null],
                ['admin.option-list-categories.index', 'Option Lists', null],
                ['admin.timecoded-elements-family.index', 'Timecoded Elements', null],
            ],
        ],
        ['admin.projects.index', ['Projects', 'video-camera'], 'manage-videos'],
        ['admin.renderProjectsHtml.index', ['Renders', 'video-camera'], 'manage-renders'],
        ['admin.renderMedias.index', ['Renders Medias', 'file-image-o'], 'manage-renders'],
        ['admin.maintenances.index', ['Maintenance', 'cog'], null],
        ['admin.configs.index', ['Configuration', 'server'], 'manage-configs'],
        [
            'admin.plans.index',
            ['Plans', 'money'],
            null,
            [
                ['admin.plans.index', 'Features plans', null],
                ['admin.billing-plans.index', 'Billing plans', 'manage-plans'],
            ],
        ],
        ['admin.music-lists.index', ['Music List', 'music'], 'manage-option-lists'],
    ];
}
