<?php

declare(strict_types=1);

namespace App\Domain\Config;

use App\Models\Config;

// TODO: ConfigRepository must be an interface. This implementation must be in the infrastructure layer as EloquentConfigRepository
class ConfigRepository
{
    private const DELIMITER = ',';

    /** @throws MissingConfigurationException */
    public function getValueByKey(string $key): string
    {
        /** @var Config|null $configItem */
        $configItem = Config::query()->where('key', $key)->first();

        if ($configItem === null) {
            throw new MissingConfigurationException();
        }

        return $configItem->value;
    }

    public function getArray(string $key): array
    {
        $config = Config::query()->where('key', $key)->first();
        if ($config !== null) {
            $jsonDecoded = json_decode($config->value, true);
            $isJson = is_string($config->value) && (is_object($jsonDecoded) || is_array($jsonDecoded));
            if ($isJson) {
                return (array) $jsonDecoded;
            }

            $values = preg_replace('/\s/m', '', $config->value);

            return explode(self::DELIMITER, $values);
        }

        return [];
    }
}
