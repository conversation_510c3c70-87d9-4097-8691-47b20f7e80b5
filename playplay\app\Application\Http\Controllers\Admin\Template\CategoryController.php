<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Template;

use App\Application\Events\NewGenericTemplateCategoryIsCreated;
use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\Category\CategoryStoreRequest;
use App\Application\Http\Requests\Admin\Template\CategoryReorderRequest;
use App\Application\Http\Requests\Admin\Template\GetGenericOrCustomCategoriesRequest;
use App\Domain\Template\Repositories\CategoryRepository;
use App\Domain\Template\Repositories\SubCategoryRepository;
use App\Models\Category;
use App\Models\SubCategory;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;

final class CategoryController extends BaseController
{
    private const GENERIC_ACTIVE_TAB = 'generic';
    private const CUSTOM_ACTIVE_TAB = 'custom';
    private CategoryRepository $categoryRepository;
    private SubCategoryRepository $subCategoryRepository;
    private Dispatcher $eventDispatcher;
    private Factory $viewFactory;

    public function __construct(
        CategoryRepository $categoryRepository,
        SubCategoryRepository $subCategoryRepository,
        Dispatcher $eventDispatcher,
        Factory $viewFactory,
    ) {
        $this->authorizeResource(Category::class);
        $this->categoryRepository = $categoryRepository;
        $this->subCategoryRepository = $subCategoryRepository;
        $this->eventDispatcher = $eventDispatcher;
        $this->viewFactory = $viewFactory;
    }

    public function index(GetGenericOrCustomCategoriesRequest $request): View
    {
        $isGeneric = $this->getCategoryFromRequest($request)->is_generic;
        $categories = $isGeneric
            ? $this->categoryRepository->getOrderedGenericCategories()
            : $this->categoryRepository->getCustomCategories();
        $tabActive = $this->getActiveTab((bool) $isGeneric);

        return $this->viewFactory->make('admin.templates.categories.index', [
            'categories' => $categories,
            'tabActive' => $tabActive,
        ]);
    }

    public function create(GetGenericOrCustomCategoriesRequest $request): View
    {
        $category = $this->getCategoryFromRequest($request);
        $subCategories = $this->subCategoryRepository->getAllSubCategories();

        return $this->viewFactory->make('admin.templates.categories.create', [
            'category' => $category,
            'subCategories' => $subCategories,
        ]);
    }

    public function edit(Category $category): View
    {
        $subCategories = $this->subCategoryRepository->getAllSubCategories();

        return $this->viewFactory->make(
            'admin.templates.categories.edit',
            ['category' => $category, 'subCategories' => $subCategories]
        );
    }

    public function showDangerZone(Category $category): View
    {
        return $this->viewFactory->make('admin.templates.categories.danger-zone', ['category' => $category]);
    }

    public function store(CategoryStoreRequest $request): RedirectResponse
    {
        $categoriesIds = $this->categoryRepository->getCustomCategories()->pluck('id');

        /** @var Category $category */
        $category = Category::create($request->only('name', 'backoffice_name', 'is_generic'));
        $this->categoryRepository->setSubCategories($category, $request->get('sub_categories_ids', []));
        $this->categoryRepository->reorder($categoriesIds->prepend($category->id)->toArray());

        if ((bool) $category->is_generic === true) {
            $this->eventDispatcher->dispatch(new NewGenericTemplateCategoryIsCreated($category));
        }

        return redirect()->route('admin.templateCategories.index', [
            'type' => $this->getActiveTab((bool) $category->is_generic),
        ]);
    }

    public function destroy(Category $category): JsonResponse
    {
        $categoryType = $this->getActiveTab((bool) $category->is_generic);
        $this->categoryRepository->delete($category);

        return new JsonResponse([
            'success' => true,
            'redirect' => route('admin.templateCategories.index', ['type' => $categoryType]),
        ]);
    }

    public function reorder(CategoryReorderRequest $categoryReorderRequest): void
    {
        $this->categoryRepository->reorder($categoryReorderRequest->get('category_ids', []));
    }

    public function card(SubCategory $subCategory): View
    {
        return $this->viewFactory->make('admin.templates.categories.sub-categories-card', compact('subCategory'));
    }

    public function update(Category $category, CategoryStoreRequest $request): RedirectResponse
    {
        $category->update($request->only(['name', 'backoffice_name']));

        $this->categoryRepository->setSubCategories($category, $request->get('sub_categories_ids', []));

        return redirect()->route('admin.templateCategories.index', [
            'type' => $this->getActiveTab((bool) $category->is_generic),
        ]);
    }

    private function getActiveTab(bool $isGeneric): string
    {
        return $isGeneric ? self::GENERIC_ACTIVE_TAB : self::CUSTOM_ACTIVE_TAB;
    }

    private function getCategoryFromRequest(GetGenericOrCustomCategoriesRequest $request): Category
    {
        return new Category(['is_generic' => $request->get('type') !== 'custom']);
    }
}
