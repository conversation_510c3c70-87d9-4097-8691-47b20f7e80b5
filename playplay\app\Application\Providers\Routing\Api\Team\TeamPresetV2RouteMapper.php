<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Team;

use App\Application\Http\Controllers\Api\V2\Team\UpdateTeamPresetController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class TeamPresetV2RouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::put('/teams/{team}/presets', UpdateTeamPresetController::class)->name('teams.presets.update');
    }
}
