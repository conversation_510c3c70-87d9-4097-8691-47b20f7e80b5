<?php

declare(strict_types=1);

namespace App\Application\Listeners\Renders;

use App\Application\Events\Renders\RenderProjectUpdated;
use App\Domain\ShareableLink\ShareableLinkRepository;

final class UpdateShareableLinkWithLastRender
{
    private ShareableLinkRepository $shareableLinkRepository;

    public function __construct(ShareableLinkRepository $shareableLinkRepository)
    {
        $this->shareableLinkRepository = $shareableLinkRepository;
    }

    public function handle(RenderProjectUpdated $event): void
    {
        $renderProject = $event->getRenderProject();

        if ($renderProject->rendered_url !== null) {
            $shareableLink = $this->shareableLinkRepository->getUniqueShareableLinkByProjectId(
                $renderProject->project->id
            );

            if ($shareableLink !== null) {
                $shareableLink->render_project_html_id = $renderProject->id;
                $shareableLink->update();
            }
        }
    }
}
