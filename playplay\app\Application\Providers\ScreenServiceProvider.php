<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Screen\Parameters\ParamMediaRepository;
use App\Domain\Screen\ScreenCategoryRepository;
use App\Domain\Screen\ScreenCoverRepository;
use App\Domain\Screen\ScreenFamilyRepository;
use App\Domain\Screen\ScreenLayoutRepository;
use App\Domain\Screen\ScreenRepository;
use App\Infrastructure\Screen\EloquentScreenCategoryRepository;
use App\Infrastructure\Screen\EloquentScreenCoverRepository;
use App\Infrastructure\Screen\EloquentScreenFamilyRepository;
use App\Infrastructure\Screen\EloquentScreenLayoutRepository;
use App\Infrastructure\Screen\EloquentScreenRepository;
use App\Infrastructure\Screen\Parameters\EloquentParamMediaRepository;
use Illuminate\Support\ServiceProvider;

class ScreenServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(ScreenRepository::class, EloquentScreenRepository::class);
        $this->app->bind(ScreenCoverRepository::class, EloquentScreenCoverRepository::class);
        $this->app->bind(ScreenCategoryRepository::class, EloquentScreenCategoryRepository::class);
        $this->app->bind(ScreenFamilyRepository::class, EloquentScreenFamilyRepository::class);
        $this->app->bind(ScreenLayoutRepository::class, EloquentScreenLayoutRepository::class);
        $this->app->bind(ParamMediaRepository::class, EloquentParamMediaRepository::class);
    }
}
