<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\TransferMedia;

use App\Application\Events\MediaUploadedForProjectFromTransferMedia;
use App\Application\Http\Requests\Api\TransferMedia\CreateRawMediaRequest;
use App\Domain\Workflow\Config\MediaWorkflow;
use App\Domain\Cdn\CdnService;
use App\Domain\Project\ProjectRepository;
use App\Domain\Project\RawMedia\RelationType;
use App\Domain\RawMedia\RawMediaRepository;
use App\Domain\RawMedia\RawMediaService;
use App\Domain\RawMedia\RawMediaSource;
use App\Domain\RawMedia\RawMediaType;
use App\Models\Project;
use App\Services\CloudStorageService;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Routing\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;

final class CreateRawMediaController extends Controller
{
    private CdnService $cdnService;
    private CloudStorageService $storageService;
    private RawMediaService $rawMediaService;
    private Dispatcher $eventDispatcher;
    private ProjectRepository $projectRepository;
    private RawMediaRepository $mediaRepository;

    public function __construct(
        CdnService $cdnService,
        CloudStorageService $storageService,
        RawMediaService $rawMediaService,
        Dispatcher $eventDispatcher,
        ProjectRepository $projectRepository,
        RawMediaRepository $mediaRepository
    ) {
        $this->cdnService = $cdnService;
        $this->storageService = $storageService;
        $this->rawMediaService = $rawMediaService;
        $this->eventDispatcher = $eventDispatcher;
        $this->projectRepository = $projectRepository;
        $this->mediaRepository = $mediaRepository;
    }

    public function __invoke(CreateRawMediaRequest $request): JsonResponse
    {
        $url = $this->cdnService->getUrlFromCdn($request->get('cdn_url'));
        $this->storageService->setAllReaderFromUrl($url);

        $rawMedia = $this->rawMediaService->findOrCreateRawMedia(
            [
                'source' => new RawMediaSource($request->get('source')),
                'name' => $request->get('name'),
                'content_type' => $request->get('content_type'),
                'md5' => $request->get('hash'),
                'url' => $url,
                'type' => RawMediaType::fromMimeType($request->get('content_type')),
            ],
            true,
            false
        );

        /** @note We have to manually set the project_id to the rawMedia because the service doesn't do it */
        /** @var Project $project */
        $project = $this->projectRepository->findOneByUuid($request->get('uuid'));
        $this->mediaRepository->linkRawMediaToProject(
            $rawMedia,
            $project,
            RelationType::LIBRARY
        );
        $this->mediaRepository->linkRawMediaToUser($rawMedia, $project->user);

        $this->eventDispatcher->dispatch(
            new MediaUploadedForProjectFromTransferMedia($rawMedia->refresh(), $project->id)
        );

        return new JsonResponse($rawMedia->refresh()->toVue());
    }
}
