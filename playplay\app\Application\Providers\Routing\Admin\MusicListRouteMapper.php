<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Admin;

use App\Application\Http\Controllers\Admin\Music\MusicController;
use App\Application\Http\Controllers\Admin\Music\MusicListController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class MusicListRouteMapper implements RouteMapper
{
    public function map(): void
    {
        $this->mapMusicLists();
        $this->mapMusics();
    }

    private function mapMusicLists(): void
    {
        Route::group([
            'prefix' => '/music-lists',
            'as' => 'music-lists.',
        ], static function (Router $router) {
            $router->get('/', [MusicListController::class, 'index'])->name('index');
            $router->get('/create', [MusicListController::class, 'create'])->name('create');
            $router->get('/{music_list}/edit', [MusicListController::class, 'edit'])->name('edit');
            $router->post('/store', [MusicListController::class, 'store'])->name('store');
            $router->put('/{music_list}/update', [MusicListController::class, 'update'])->name('update');
            $router->delete('/{music_list}', [MusicListController::class, 'destroy'])->name('destroy');
            $router->put('/{music_list}/reorder-options', [MusicListController::class, 'reorderOptions'])
                ->name('reorder-options');
        });
    }

    private function mapMusics(): void
    {
        Route::group([
            'prefix' => '/musics',
            'as' => 'musics.',
        ], static function (Router $router) {
            $router->get('/create', [MusicController::class, 'create'])->name('create');
            $router->get('/{music}/edit', [MusicController::class, 'edit'])->name('edit');
            $router->post('/store', [MusicController::class, 'store'])->name('store');
            $router->put('/{music}/update', [MusicController::class, 'update'])->name('update');
            $router->delete('/{music}', [MusicController::class, 'destroy'])->name('destroy');
        });
    }
}
