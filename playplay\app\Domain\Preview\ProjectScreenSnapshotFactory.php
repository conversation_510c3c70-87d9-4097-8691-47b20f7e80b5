<?php

declare(strict_types=1);

namespace App\Domain\Preview;

use App\Domain\ProcessedMedia\Repository\ProcessedMediaRepository;
use App\Domain\ProjectScreen\ProjectScreenRepository;
use App\Domain\ProjectScreen\Serializer\ProjectScreenSerializer;
use App\Domain\RawMedia\RawMediaRepository;
use App\Domain\Render\RenderMedia\RenderMediaRepository;
use App\Models\ProcessedMedia;
use App\Models\ProjectScreen;
use App\Models\RawMedia;
use App\Models\Snapshot;
use Illuminate\Contracts\Auth\Access\Gate;

/**
 * @property ProjectScreenRepository $projectScreenRepository
 */
class ProjectScreenSnapshotFactory
{
    private ProcessedMediaRepository $processedMediaRepository;
    private SnapshotRepository $snapshotRepository;
    private Gate $gate;
    private RawMediaRepository $rawMediaRepository;
    private ProjectScreenSerializer $projectScreenSerializer;
    private RenderMediaRepository $renderMediaRepository;

    public function __construct(
        ProcessedMediaRepository $processedMediaRepository,
        SnapshotRepository $snapshotRepository,
        Gate $gate,
        RawMediaRepository $rawMediaRepository,
        ProjectScreenSerializer $projectScreenSerializer,
        ProjectScreenRepository $projectScreenRepository,
        RenderMediaRepository $renderMediaRepository,
    ) {
        $this->processedMediaRepository = $processedMediaRepository;
        $this->snapshotRepository = $snapshotRepository;
        $this->gate = $gate;
        $this->rawMediaRepository = $rawMediaRepository;
        $this->projectScreenSerializer = $projectScreenSerializer;
        $this->projectScreenRepository = $projectScreenRepository;
        $this->renderMediaRepository = $renderMediaRepository;
    }

    public function generate(ProjectScreen $projectScreen): Snapshot
    {
        // Only users that have access to the project can create snapshot
        $this->gate->authorize('create', [Snapshot::class, $projectScreen]);

        $snapshot = $this->snapshotRepository->createSnapshotForProjectScreen(
            $projectScreen,
            $this->projectScreenSerializer->serialize($projectScreen)
        );

        $this->snapshotRepository->attachRenderMedias(
            $snapshot,
            $this->getRenderMediaIdsLinked($projectScreen)
        );

        $snapshot = $this->snapshotRepository->updateStatusFromRenderMediasStatuses($snapshot);

        $this->projectScreenRepository->update($projectScreen, ['render_screen_preview_up_to_date' => true]);

        return $snapshot;
    }

    /**
     * @return int[]
     */
    private function getRenderMediaIdsLinked(ProjectScreen $projectScreen): array
    {
        $processedMediaIds = [
            ...$this->processedMediaRepository->getIdsOfLogosOfAProject($projectScreen->project),
            ...$this->processedMediaRepository
                ->getAllIdsExceptProcessedMediaUsedInMediaAndCutawayShotsParamsOfProjectScreen($projectScreen),
        ];

        $renderMediasIds = [];
        if ($processedMediaIds !== []) {
            $renderMediasIds = array_merge(
                $renderMediasIds,
                $this->renderMediaRepository
                    ->getMaxIdsByParentIdAndParentType($processedMediaIds, ProcessedMedia::DEPRECATED_CLASS)
            );
        }

        $rawMediaIds = $this->rawMediaRepository->getIdsOfCutawayShotsAndMediasUsedInProjectScreen($projectScreen);
        if ($rawMediaIds !== []) {
            $renderMediasIds = array_merge(
                $renderMediasIds,
                $this->renderMediaRepository
                    ->getMaxIdsByParentIdAndParentType($rawMediaIds, RawMedia::DEPRECATED_CLASS)
            );
        }

        return $renderMediasIds;
    }
}
