<?php

namespace App\Application\Http\Requests\Admin\BillingPlan;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BillingPlanUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'ref' => [
                'required',
                'string',
                Rule::unique('billing_plans', 'ref')->ignore(request()->route('billing_plan'))
            ],
            'stripe_plan_id' => [
                'required',
                'string',
                Rule::unique('billing_plans', 'stripe_plan_id')->ignore(request()->route('billing_plan')),
            ],
            'features.*' => ['nullable', 'integer'],
        ];
    }
}
