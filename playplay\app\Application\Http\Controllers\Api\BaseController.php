<?php

namespace App\Application\Http\Controllers\Api;

use Illuminate\Http\JsonResponse;
use Illuminate\Pagination\LengthAwarePaginator as Paginator;
use Illuminate\Routing\Controller;
use Illuminate\Support\Collection;

class BaseController extends Controller
{
    // Cache is invalidated no matter what after 7 days
    public const CACHE_TTL = ********;

    protected function buildJsonResponse(Collection $listOfObjects): array
    {
        return [
            'results' => $listOfObjects->map(function ($obj) {
                if (is_array($obj) || is_string($obj) || !str_contains(get_class($obj), 'App\Models')) {
                    return $obj;
                }

                return $obj->toVue();
            }),
            'nb_results' => $listOfObjects->count(),
        ];
    }

    protected function sendJsonResponse(Collection $listOfObjects, int $statusCode): JsonResponse
    {
        return new JsonResponse($this->buildJsonResponse($listOfObjects), $statusCode);
    }

    protected function sendJsonResponseErrorFromArray(Collection $collectionOfArray, int $statusCode): JsonResponse
    {
        return new JsonResponse([
            'errors' => $collectionOfArray,
            'nb_errors' => count($collectionOfArray),
        ], $statusCode);
    }

    protected function sendJsonResponseFromPaginator(Paginator $paginator, int $statusCode): JsonResponse
    {
        return new JsonResponse([
            'results' => $paginator,
            'nb_results' => $paginator->total(),
        ], $statusCode);
    }
}
