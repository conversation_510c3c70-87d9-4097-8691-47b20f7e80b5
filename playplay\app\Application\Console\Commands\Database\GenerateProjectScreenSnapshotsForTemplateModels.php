<?php

namespace App\Application\Console\Commands\Database;

use App\Domain\Preview\SnapshotRepository;
use App\Domain\ProjectScreen\ProjectScreenRepository;
use App\Domain\ProjectScreen\Serializer\ProjectScreenSerializer;
use App\Models\Project;
use App\Models\ProjectScreen;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use RuntimeException;
use Throwable;

class GenerateProjectScreenSnapshotsForTemplateModels extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:generate-snapshots-for-template-models';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'generate-snapshots-for-template-models';

    private SnapshotRepository $snapshotRepository;
    private ProjectScreenSerializer $projectScreenSerializer;
    private ProjectScreenRepository $projectScreenRepository;

    public function __construct(
        SnapshotRepository $snapshotRepository,
        ProjectScreenSerializer $projectScreenSerializer,
        ProjectScreenRepository $projectScreenRepository
    ) {
        parent::__construct();
        $this->snapshotRepository = $snapshotRepository;
        $this->projectScreenSerializer = $projectScreenSerializer;
        $this->projectScreenRepository = $projectScreenRepository;
    }

    public function handle(): int
    {
        $projectQueryBuilder = Project::has('templateModels')->has('team')->where('version', 3);
        $projectScreenQueryBuilder = ProjectScreen::whereIn('project_id', $projectQueryBuilder->pluck('id'));
        $bar = $this->output->createProgressBar($projectScreenQueryBuilder->count());

        $projectScreenQueryBuilder->chunk(100, function (Collection $projectScreens) use ($bar) {
            $this->invalidateSnapshotsForProjectScreens($projectScreens, $bar);
        });
        $bar->finish();

        return 0;
    }

    private function invalidateSnapshotsForProjectScreens($projectScreens, $bar): void
    {
        foreach ($projectScreens as $projectScreen) {
            try {
                $this->invalidateSnapshots($projectScreen);
                $bar->advance();
            } catch (Throwable) {
                throw new RuntimeException();
            }
        }
    }

    private function invalidateSnapshots(ProjectScreen $projectScreen): void
    {
        $this->snapshotRepository->createSnapshotForProjectScreen(
            $projectScreen,
            $this->projectScreenSerializer->serialize($projectScreen)
        );

        $this->projectScreenRepository->update($projectScreen, ['render_screen_preview_up_to_date' => true]);
    }
}
