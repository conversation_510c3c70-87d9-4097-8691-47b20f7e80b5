<?php

declare(strict_types=1);

namespace App\Domain\Config;

use App\Models\Config;
use App\Models\Project;
use App\Models\Team;

class ConfigService
{
    private const FREE_TRIAL_PROJECT_SIGN_UP_CONFIG_KEY = 'free_trial_project_id';
    public const FREE_TRIAL_TEAM_CONFIG_KEY = 'free_trial_team_id';

    // TODO move to Project domain
    public function retrieveProjectFromConfig(): Project
    {
        $projectModel = Project::find($this->getIdFromConfigKey(self::FREE_TRIAL_PROJECT_SIGN_UP_CONFIG_KEY));

        // Stop creating account if no project model available
        if ($projectModel === null) {
            throw new MissingConfigurationException('Missing config free trial model project id');
        }

        return $projectModel;
    }

    // TODO move to Team domain
    public function retrieveTeamFromConfig(): Team
    {
        /** @var Team|null $teamModel */
        $teamModel = Team::with(['presets', 'features', 'company'])->find(
            $this->getIdFromConfigKey(self::FREE_TRIAL_TEAM_CONFIG_KEY)
        );

        if ($teamModel === null) {
            throw new MissingConfigurationException('Missing config free trial model team id');
        }

        return $teamModel;
    }

    private function getIdFromConfigKey(string $configKey): ?int
    {
        /** @var Config|null $config */
        $config = Config::where('key', $configKey)->first();
        if ($config === null) {
            return null;
        }

        return (int) $config->value;
    }
}
