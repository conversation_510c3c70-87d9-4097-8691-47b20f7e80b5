<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Project;

use App\Application\Http\Controllers\Api\BaseController;
use App\Domain\Project\Duplicator\ProjectDuplicator;
use App\Models\Project;
use App\Models\User;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class DuplicateProjectController extends BaseController
{
    use AuthorizesRequests;

    private Guard $guard;
    private ProjectDuplicator $projectDuplicator;

    public function __construct(
        Guard $guard,
        ProjectDuplicator $projectDuplicator
    ) {
        $this->guard = $guard;
        $this->projectDuplicator = $projectDuplicator;
    }

    public function __invoke(Project $project): JsonResponse
    {
        /** @var User $user */
        $user = $this->guard->user();

        $this->authorize('canAccessRestrictedData', $project->company);
        $this->authorize('duplicate', $project);

        $duplicatedProject = $this->projectDuplicator->duplicateAndSanitize($project, $user);

        return $this->sendJsonResponse(new Collection([$duplicatedProject]), Response::HTTP_CREATED);
    }
}
