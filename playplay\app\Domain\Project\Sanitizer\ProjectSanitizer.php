<?php

declare(strict_types=1);

namespace App\Domain\Project\Sanitizer;

use App\Application\Events\ProjectUpdated;
use App\Models\Project;
use Illuminate\Contracts\Events\Dispatcher;

final class ProjectSanitizer
{
    /**
     * @var ProjectElementsSanitizer[]
     */
    private iterable $sanitizers;
    private Dispatcher $eventDispatcher;

    /**
     * @param ProjectElementsSanitizer[] $sanitizers
     */
    public function __construct(iterable $sanitizers, Dispatcher $eventDispatcher)
    {
        $this->sanitizers = $sanitizers;
        $this->eventDispatcher = $eventDispatcher;
    }

    public function sanitize(Project $project): void
    {
        $hasProjectBeenUpdated = false;
        foreach ($this->sanitizers as $sanitizer) {
            if ($sanitizer->sanitize($project)) {
                $hasProjectBeenUpdated = true;
            }
        }

        if ($hasProjectBeenUpdated) {
            $this->eventDispatcher->dispatch(new ProjectUpdated($project));
        }
    }
}
