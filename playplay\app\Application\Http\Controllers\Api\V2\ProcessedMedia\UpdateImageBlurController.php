<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\ProcessedMedia;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Project\ProcessedMedia\Update\ProcessedMediaImageBlurUpdateRequest;
use App\Domain\Workflow\Config\MediaWorkflow;
use App\Models\ProcessedMedia;
use App\Services\ProcessedMedia\FilterApplier;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

final class UpdateImageBlurController extends BaseController
{
    use AuthorizesRequests;

    private FilterApplier $filterApplier;
    private MediaWorkflow $mediaWorkflow;

    public function __construct(FilterApplier $filterApplier, MediaWorkflow $mediaWorkflow)
    {
        $this->filterApplier = $filterApplier;
        $this->mediaWorkflow = $mediaWorkflow;
    }

    /**
     * @throws AuthorizationException
     */
    public function __invoke(
        ProcessedMedia $processedMedia,
        ProcessedMediaImageBlurUpdateRequest $request
    ): JsonResponse {
        $this->authorize('update', $processedMedia);

        if (!$processedMedia->rawMedia->isImage()) {
            throw new BadRequestHttpException();
        }

        $this->mediaWorkflow->start($processedMedia->rawMedia);

        $this->filterApplier->blurImageBasedOnParam($processedMedia, $request->get('param_id'));

        return $this->sendJsonResponse(new Collection([$processedMedia]), Response::HTTP_OK);
    }
}
