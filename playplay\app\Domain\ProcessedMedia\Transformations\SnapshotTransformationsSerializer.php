<?php

declare(strict_types=1);

namespace App\Domain\ProcessedMedia\Transformations;

use App\Models\ProcessedMedia;

class SnapshotTransformationsSerializer implements TransformationsSerializer
{
    private KeepSizeSerializer $keepSizeSerializer;
    private RelativeCropSerializer $cropSerializer;

    public function __construct(KeepSizeSerializer $keepSizeSerializer, RelativeCropSerializer $cropSerializer)
    {
        $this->keepSizeSerializer = $keepSizeSerializer;
        $this->cropSerializer = $cropSerializer;
    }

    public function serialize(ProcessedMedia $processedMedia): array
    {
        $serializedKeepSize = $this->keepSizeSerializer->serialize($processedMedia);
        $serializedCrop = null;

        if ($serializedKeepSize === null) {
            $serializedCrop = $this->cropSerializer->serialize($processedMedia);
        }

        return [
            'keep_size' => $serializedKeepSize,
            'crop' => $serializedCrop,
        ];
    }
}
