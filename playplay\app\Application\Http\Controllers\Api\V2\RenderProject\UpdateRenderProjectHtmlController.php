<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\RenderProject;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\RenderProject\UpdateRenderProjectHtmlRequest;
use App\Models\Renders\RenderProjectHtml;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class UpdateRenderProjectHtmlController extends BaseController
{
    use AuthorizesRequests;

    public function __invoke(
        RenderProjectHtml $renderProjectHtml,
        UpdateRenderProjectHtmlRequest $request
    ): JsonResponse {
        $this->authorize('update', $renderProjectHtml);

        if ($renderProjectHtml->isNotFinished()) {
            $renderProjectHtml->update(['status' => $request->get('status')]);
        }

        return $this->sendJsonResponse(
            new Collection([$renderProjectHtml]),
            Response::HTTP_OK
        );
    }
}
