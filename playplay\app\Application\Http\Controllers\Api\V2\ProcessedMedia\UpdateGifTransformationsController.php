<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\ProcessedMedia;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\ProcessedMedia\Update\ProcessedMediaGifTransformationsUpdateRequest;
use App\Infrastructure\ProcessedMedia\Transformations\TransformationsFactory;
use App\Models\ProcessedMedia;
use App\Services\Processing\RenderMedia\GifService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

final class UpdateGifTransformationsController extends BaseController
{
    use AuthorizesRequests;

    private GifService $gifService;
    private TransformationsFactory $transformationsFactory;

    public function __construct(GifService $gifService, TransformationsFactory $transformationsFactory)
    {
        $this->gifService = $gifService;
        $this->transformationsFactory = $transformationsFactory;
    }

    public function __invoke(
        ProcessedMedia $processedMedia,
        ProcessedMediaGifTransformationsUpdateRequest $request
    ): JsonResponse {
        $this->authorize('update', $processedMedia);

        if (!$processedMedia->rawMedia->isGif()) {
            throw new BadRequestHttpException();
        }

        $transformations = $this->transformationsFactory->create(
            $processedMedia->project_id,
            $request->get('param_id'),
            $request->get('crop'),
            $request->get('keepSize')
        );

        $this->gifService->applyTransformations($processedMedia, $transformations);

        return $this->sendJsonResponse(new Collection([$processedMedia]), Response::HTTP_OK);
    }
}
