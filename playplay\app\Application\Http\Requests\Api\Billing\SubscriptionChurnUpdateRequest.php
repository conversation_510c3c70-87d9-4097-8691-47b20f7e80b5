<?php

namespace App\Application\Http\Requests\Api\Billing;

use App\Domain\Churn\ChurnReason;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SubscriptionChurnUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'reason' => ['required', 'string', 'max:255', Rule::in(ChurnReason::values())],
            'comment' => ['nullable', 'string'],
        ];
    }
}
