<?php

namespace App\Application\Providers;

use App\Domain\Font\DefaultFontRepository;
use App\Domain\ProcessedMedia\Transformations\RenderingTransformationsSerializer;
use App\Domain\ProcessedMedia\Transformations\SnapshotTransformationsSerializer;
use App\Domain\ProjectScreen\Param\ProjectScreenParamRepository;
use App\Domain\ProjectScreen\Param\Serializer\AudioParameterSerializer;
use App\Domain\ProjectScreen\Param\Serializer\LogoParameterSerializer;
use App\Domain\ProjectScreen\Param\Serializer\OptionListParameterSerializer;
use App\Domain\ProjectScreen\Param\Serializer\ParametersSerializer;
use App\Domain\ProjectScreen\Param\Serializer\RenderingCutawayShotParameterSerializer;
use App\Domain\ProjectScreen\Param\Serializer\RenderingMediaParameterSerializer;
use App\Domain\ProjectScreen\Param\Serializer\SnapshotCutawayShotParameterSerializer;
use App\Domain\ProjectScreen\Param\Serializer\SnapshotMediaParameterSerializer;
use App\Domain\ProjectScreen\Param\Serializer\StrictTextareaParameterSerializer;
use App\Domain\ProjectScreen\Param\Serializer\SubtitleParameterSerializer;
use App\Domain\ProjectScreen\Param\Serializer\TextareaParameterSerializer;
use App\Domain\ProjectScreen\Param\Serializer\TextOptionsSerializer;
use App\Domain\ProjectScreen\Param\Subtitles\SubtitleParser;
use App\Domain\ProjectScreen\Param\TextSizeService;
use App\Domain\ProjectScreen\Serializer\ProjectScreenToParameters;
use App\Domain\Screen\Parameters\OptionRepository;
use App\Domain\Team\TeamOptionsService;
use App\Services\Wysiwyg\WysiwygParser;
use Illuminate\Foundation\Application;
use Illuminate\Support\ServiceProvider;
use Psr\Log\LoggerInterface;

class ParametersSerializerProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(
            'TextOptionsSerializer',
            function (Application $app) {
                return new TextOptionsSerializer($app[TextSizeService::class], $app[DefaultFontRepository::class]);
            }
        );
        $this->app->bind(
            'TextareaParameterSerializer',
            function (Application $app) {
                return new TextareaParameterSerializer($app[WysiwygParser::class], $app[TextOptionsSerializer::class]);
            }
        );
        $this->app->bind(
            'StrictTextareaParameterSerializer',
            function (Application $app) {
                return new StrictTextareaParameterSerializer(
                    $app[WysiwygParser::class],
                    $app[TextOptionsSerializer::class]
                );
            }
        );
        $this->app->bind(
            'SubtitleParameterSerializer',
            function (Application $app) {
                return new SubtitleParameterSerializer(
                    $app[WysiwygParser::class],
                    $app[SubtitleParser::class],
                    $app[TextOptionsSerializer::class]
                );
            }
        );
        $this->app->bind(
            RenderingCutawayShotParameterSerializer::class,
            function (Application $app) {
                return new RenderingCutawayShotParameterSerializer($app[RenderingTransformationsSerializer::class]);
            }
        );
        $this->app->bind(
            'MediaParameterSerializer.rendering',
            function (Application $app) {
                return new RenderingMediaParameterSerializer(
                    $app[ProjectScreenParamRepository::class],
                    $app[RenderingCutawayShotParameterSerializer::class],
                    $app[RenderingTransformationsSerializer::class]
                );
            }
        );
        $this->app->bind(
            'MediaParameterSerializer.snapshot',
            function (Application $app) {
                return new SnapshotMediaParameterSerializer(
                    $app[ProjectScreenParamRepository::class],
                    $app[SnapshotCutawayShotParameterSerializer::class],
                    $app[SnapshotTransformationsSerializer::class]
                );
            }
        );
        $this->app->bind(
            'AudioParameterSerializer',
            function () {
                return new AudioParameterSerializer();
            }
        );
        $this->app->bind(
            'LogoParameterSerializer',
            function () {
                return new LogoParameterSerializer();
            }
        );
        $this->app->bind(
            'OptionListParameterSerializer',
            function (Application $app) {
                return new OptionListParameterSerializer($app[OptionRepository::class], $app[LoggerInterface::class]);
            }
        );

        $sharedSerializers = [
            'SubtitleParameterSerializer',
            'AudioParameterSerializer',
            'LogoParameterSerializer',
            'OptionListParameterSerializer',
        ];

        $this->app->tag(
            array_merge($sharedSerializers, ['TextareaParameterSerializer', 'MediaParameterSerializer.snapshot']),
            'snapshotParametersSerializers'
        );

        $this->app->tag(
            array_merge($sharedSerializers, ['TextareaParameterSerializer', 'MediaParameterSerializer.rendering']),
            'renderingParametersSerializers'
        );

        $this->app->tag(
            array_merge($sharedSerializers, ['StrictTextareaParameterSerializer', 'MediaParameterSerializer.snapshot']),
            'strictParametersSerializers'
        );

        $this->app->bind(
            'ParametersSerializer.default',
            function (Application $app) {
                return new ParametersSerializer($app->tagged('snapshotParametersSerializers'));
            }
        );
        $this->app->bind(
            'ParametersSerializer.strict',
            function (Application $app) {
                return new ParametersSerializer($app->tagged('strictParametersSerializers'));
            }
        );
        $this->app->bind(
            'ParametersSerializer.rendering',
            function (Application $app) {
                return new ParametersSerializer($app->tagged('renderingParametersSerializers'));
            }
        );

        $this->app->bind(
            ProjectScreenToParameters::class,
            function (Application $app) {
                return new ProjectScreenToParameters(
                    $app['ParametersSerializer.default'],
                    $app[TeamOptionsService::class]
                );
            }
        );

        $this->app->bind(
            'ProjectScreenToParameters.rendering',
            function (Application $app) {
                return new ProjectScreenToParameters(
                    $app['ParametersSerializer.rendering'],
                    $app[TeamOptionsService::class]
                );
            }
        );
    }
}
