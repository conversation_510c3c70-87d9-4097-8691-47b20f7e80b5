<?php

declare(strict_types=1);

namespace App\Domain\Outro;

use App\Domain\Config\ConfigRepository;
use App\Domain\ProcessedMedia\Repository\ProcessedMediaRepository;
use App\Domain\Render\RenderMedia\RenderMediaRepository;
use App\Models\Company;
use App\Models\Project;

class OutroService
{
    private ConfigRepository $configRepository;

    private ProcessedMediaRepository $processedMediaRepository;

    private RenderMediaRepository $renderMediaRepository;

    public function __construct(
        ConfigRepository $configRepository,
        ProcessedMediaRepository $processedMediaRepository,
        RenderMediaRepository $renderMediaRepository
    ) {
        $this->configRepository = $configRepository;
        $this->processedMediaRepository = $processedMediaRepository;
        $this->renderMediaRepository = $renderMediaRepository;
    }

    public function getOutrosFor(Project $project): array
    {
        if ($project->isStory()) {
            return [];
        }

        $outros = [];
        if ($this->hasOutro($project)) {
            $lastRenderedMedia = $project->outro->lastRender;

            if ($lastRenderedMedia !== null) {
                $outros[] = [
                    'url' => $lastRenderedMedia->getOutroUrlForGlobalPreview(),
                    'duration' => $this->renderMediaRepository->getOutroDuration($lastRenderedMedia),
                ];
            }
        }

        if ($project->user->company->type === Company::TYPE_FREE_TRIAL && $project->has_watermark) {
            $outros[] = $this->configRepository->getArray('outros_free_trial_html')[$project->format] ?? null;
        }

        return $outros;
    }

    public function transformOutrosByFormatToOutroByIds(array $outrosByFormats): array
    {
        $newOutrosById = [];
        foreach ($outrosByFormats as $format => $ids) {
            foreach ($ids as $id) {
                $newOutrosById[$id] = $format;
            }
        }

        return $newOutrosById;
    }

    public function getForcedOutroRawMediaIdFor(Project $project, string $desiredFormat = null): ?int
    {
        if (!$project->isCreatedFromCustomTemplate()) {
            return null;
        }

        $format = $desiredFormat ?? $project->format;

        if (isset($project->preset->outros['forced'][$format])) {
            return $this->processedMediaRepository
                ->getById($project->preset->outros['forced'][$format])
                ->rawMedia->id;
        }

        return null;
    }

    private function hasOutro(Project $project): bool
    {
        if ($project->outro === null || $project->getHasForcedOutroAttribute()) {
            return false;
        }

        foreach ($project->preset->outros['values'] ?? [] as $presetOutro) {
            if (in_array($project->outro_id, $presetOutro['processed_medias'], true)) {
                return true;
            }
        }

        return false;
    }
}
