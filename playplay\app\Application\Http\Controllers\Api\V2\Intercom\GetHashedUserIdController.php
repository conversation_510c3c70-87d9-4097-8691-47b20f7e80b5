<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Intercom;

use App\Application\Http\Controllers\Api\BaseController;
use App\Infrastructure\Intercom\HashService;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

final class GetHashedUserIdController extends BaseController
{
    private HashService $hashService;
    private Guard $auth;

    public function __construct(HashService $hashService, Guard $auth)
    {
        $this->hashService = $hashService;
        $this->auth = $auth;
    }

    public function __invoke(): JsonResponse
    {
        $userHash = $this->hashService->hash($this->auth->user()->email);

        return $this->sendJsonResponse(new Collection([['user_hash' => $userHash]]), Response::HTTP_OK);
    }
}
