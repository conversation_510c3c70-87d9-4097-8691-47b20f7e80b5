<?php

namespace App\Application\Http\Requests\Api\Project\ProcessedMedia\Store;

use App\Domain\RawMedia\RawMediaSource;
use App\Models\ProcessedMedia;
use Illuminate\Foundation\Http\FormRequest;

class ProcessedMediaImageCropStoreRequest extends FormRequest
{
    public static function getRules()
    {
        return [
            'param_id' => ['required', 'numeric', 'exists:layout_params,id', 'paramIsInProject'],
            'raw_media_id' => ['required', 'exists:raw_medias,id', 'isImage'],
            'project_id' => ['required', 'exists:projects,id'],
            'source' => [
                'required',
                'in:' . implode(',', [
                    RawMediaSource::UPLOAD,
                    RawMediaSource::GETTY,
                    RawMediaSource::GETTY_EDITO,
                    RawMediaSource::UNSPLASH,
                    ProcessedMedia::SOURCE_FAVORITES,
                    ProcessedMedia::SOURCE_RECENTLY_USED,
                    ProcessedMedia::SOURCE_LIBRARY,
                ]),
            ],
            'crop' => ['required', 'crop'],
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return static::getRules();
    }
}
