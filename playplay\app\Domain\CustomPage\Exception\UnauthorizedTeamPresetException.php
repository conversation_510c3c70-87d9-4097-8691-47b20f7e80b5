<?php

declare(strict_types=1);

namespace App\Domain\CustomPage\Exception;

use DomainException;

final class UnauthorizedTeamPresetException extends DomainException
{
    private array $errors = [];

    public function __construct(array $errors)
    {
        parent::__construct();
        $this->errors = $errors;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }
}
