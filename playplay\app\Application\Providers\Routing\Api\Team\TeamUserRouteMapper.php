<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Team;

use App\Application\Http\Controllers\Api\V2\Team\TeamUserController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class TeamUserRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'as' => 'teams.users.',
            'prefix' => '/teams/{team}/users'
        ], static function (Router $router) {
            $router->get('/', [TeamUserController::class, 'index'])->name('index');
            $router->post('/', [TeamUserController::class, 'store'])->name('store');
            $router->put('/{user}', [TeamUserController::class, 'update'])->name('update');
            $router->delete('/{user}', [TeamUserController::class, 'destroy'])->name('destroy');
        });
    }
}
