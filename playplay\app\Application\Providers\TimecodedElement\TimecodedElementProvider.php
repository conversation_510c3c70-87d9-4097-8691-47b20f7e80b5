<?php

declare(strict_types=1);

namespace App\Application\Providers\TimecodedElement;

use App\Application\Http\Controllers\Api\V2\TimecodedElement\TimecodedElementController;
use App\Domain\ProjectScreen\Serializer\ProjectScreenSerializer;
use App\Domain\TimecodedElement\Repositories\TeamTimecodedElementPresetRepository;
use App\Domain\TimecodedElement\Repositories\TeamTimecodedElementPresetScheduledActivationRepository;
use App\Domain\TimecodedElement\Repositories\TeamTimecodedElementsFamilyRepository;
use App\Domain\TimecodedElement\Repositories\TimecodedElementPresetParamRepository;
use App\Domain\TimecodedElement\Repositories\TimecodedElementPresetParamValueRepository;
use App\Domain\TimecodedElement\Repositories\TimecodedElementPresetRepository;
use App\Domain\TimecodedElement\Repositories\TimecodedElementRepository;
use App\Domain\TimecodedElement\Repositories\TimecodedElementsFamilyRepository;
use App\Domain\TimecodedElement\Serializers\TimecodedElementPresetParamValueSerializer;
use App\Domain\TimecodedElement\Serializers\TimecodedElementPresetParamValueSerializerInterface;
use App\Domain\TimecodedElement\Serializers\TimecodedElementsSerializer;
use App\Domain\TimecodedElement\Serializers\TimecodedElementsSerializerInterface;
use App\Domain\TimecodedElement\Specifications\TimecodedElement\TimecodedElementPresetIdSpecification;
use App\Domain\TimecodedElement\Specifications\TimecodedElement\TimecodedElementSpecificationInterface;
use App\Domain\TimecodedElement\Specifications\TimecodedElement\TimecodedElementSpecificationValidator;
use App\Domain\TimecodedElement\Specifications\TimecodedElement\TimecodesMinDurationSpecification;
use App\Domain\TimecodedElement\Strategies\PresetParamDefaultValue\PresetParamColorDefaultValueStrategy;
use App\Domain\TimecodedElement\Strategies\PresetParamDefaultValue\PresetParamDefaultValueStrategy;
use App\Domain\TimecodedElement\Strategies\PresetParamDefaultValue\PresetParamDefaultValueStrategyInterface;
use App\Domain\TimecodedElement\Strategies\PresetParamDefaultValue\PresetParamListTextareaDefaultValueStrategy;
use App\Domain\TimecodedElement\Strategies\PresetParamDefaultValue\PresetParamTextareaDefaultValueStrategy;
use App\Domain\TimecodedElement\Strategies\PresetParamValueSanitizer\PresetParamValueColorSanitizerStrategy;
use App\Domain\TimecodedElement\Strategies\PresetParamValueSanitizer\PresetParamValueListTextareaSanitizerStrategy;
use App\Domain\TimecodedElement\Strategies\PresetParamValueSanitizer\PresetParamValueSanitizerStrategy;
use App\Domain\TimecodedElement\Strategies\PresetParamValueSanitizer\PresetParamValueSanitizerStrategyInterface;
use App\Domain\TimecodedElement\Strategies\PresetParamValueSanitizer\PresetParamValueTextareaSanitizerStrategy;
use App\Infrastructure\TimecodedElement\Repositories\EloquentTeamTimecodedElementPresetRepository;
use App\Infrastructure\TimecodedElement\Repositories\EloquentTeamTimecodedElementPresetScheduledActivationRepository;
use App\Infrastructure\TimecodedElement\Repositories\EloquentTeamTimecodedElementsFamilyRepository;
use App\Infrastructure\TimecodedElement\Repositories\EloquentTimecodedElementPresetParamRepository;
use App\Infrastructure\TimecodedElement\Repositories\EloquentTimecodedElementPresetParamValueRepository;
use App\Infrastructure\TimecodedElement\Repositories\EloquentTimecodedElementPresetRepository;
use App\Infrastructure\TimecodedElement\Repositories\EloquentTimecodedElementRepository;
use App\Infrastructure\TimecodedElement\Repositories\EloquentTimecodedElementsFamilyRepository;
use App\Infrastructure\TimecodedElement\Serializers\Animaniac\AnimaniacTimecodedElementsSerializer;
use App\Infrastructure\TimecodedElement\Strategy\AnimaniacSerializer\ColorPresetParamValueSerializerStrategy;
use App\Infrastructure\TimecodedElement\Strategy\AnimaniacSerializer\ListTextareaPresetParamValueSerializerStrategy;
use App\Infrastructure\TimecodedElement\Strategy\AnimaniacSerializer\PresetParamValueSerializerStrategy;
use App\Infrastructure\TimecodedElement\Strategy\AnimaniacSerializer\PresetParamValueSerializerStrategyInterface;
use App\Infrastructure\TimecodedElement\Strategy\AnimaniacSerializer\TextareaPresetParamValueSerializerStrategy;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;

final class TimecodedElementProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(
            TimecodedElementRepository::class,
            EloquentTimecodedElementRepository::class
        );

        $this->app->bind(
            TimecodedElementPresetParamValueRepository::class,
            EloquentTimecodedElementPresetParamValueRepository::class
        );

        $this->app->bind(
            TimecodedElementPresetParamRepository::class,
            EloquentTimecodedElementPresetParamRepository::class
        );

        $this->app->bind(
            TimecodedElementPresetRepository::class,
            EloquentTimecodedElementPresetRepository::class
        );

        $this->app->bind(
            TimecodedElementsFamilyRepository::class,
            EloquentTimecodedElementsFamilyRepository::class
        );

        $this->app->bind(
            TeamTimecodedElementsFamilyRepository::class,
            EloquentTeamTimecodedElementsFamilyRepository::class
        );

        $this->app->bind(
            TeamTimecodedElementPresetRepository::class,
            EloquentTeamTimecodedElementPresetRepository::class
        );

        $this->app->bind(
            TeamTimecodedElementPresetScheduledActivationRepository::class,
            EloquentTeamTimecodedElementPresetScheduledActivationRepository::class
        );

        $this->app
            ->when(TimecodedElementController::class)
            ->needs(TimecodedElementsSerializerInterface::class)
            ->give(TimecodedElementsSerializer::class);

        $this->app
            ->when(ProjectScreenSerializer::class)
            ->needs(TimecodedElementsSerializerInterface::class)
            ->give(AnimaniacTimecodedElementsSerializer::class);

        $this->app->bind(
            TimecodedElementPresetParamValueSerializerInterface::class,
            TimecodedElementPresetParamValueSerializer::class,
        );

        $timecodedElementSpecificationRules = [
            TimecodedElementPresetIdSpecification::class,
            TimecodesMinDurationSpecification::class,
        ];

        $this->app->tag($timecodedElementSpecificationRules, 'timecodedElementSpecificationRules');

        $this->app->bind(
            TimecodedElementSpecificationInterface::class,
            function (Application $app) {
                return new TimecodedElementSpecificationValidator(
                    ...$app->tagged('timecodedElementSpecificationRules')
                );
            }
        );

        $timecodedElementStrategies = [
            PresetParamColorDefaultValueStrategy::class,
            PresetParamTextareaDefaultValueStrategy::class,
            PresetParamListTextareaDefaultValueStrategy::class,
        ];

        $this->app->tag($timecodedElementStrategies, 'timecodedElementDefaultValueStrategies');

        $this->app->bind(
            PresetParamDefaultValueStrategyInterface::class,
            function (Application $app) {
                return new PresetParamDefaultValueStrategy(
                    ...$app->tagged('timecodedElementDefaultValueStrategies')
                );
            }
        );

        $timecodedElementSanitizerStrategies = [
            PresetParamValueColorSanitizerStrategy::class,
            PresetParamValueTextareaSanitizerStrategy::class,
            PresetParamValueListTextareaSanitizerStrategy::class,
        ];

        $this->app->tag($timecodedElementSanitizerStrategies, 'timecodedElementSanitizerStrategies');

        $this->app->bind(
            PresetParamValueSanitizerStrategyInterface::class,
            function (Application $app) {
                return new PresetParamValueSanitizerStrategy(
                    ...$app->tagged('timecodedElementSanitizerStrategies')
                );
            }
        );

        $timecodedElementPresetParamValueSerializerStrategies = [
            TextareaPresetParamValueSerializerStrategy::class,
            ColorPresetParamValueSerializerStrategy::class,
            ListTextareaPresetParamValueSerializerStrategy::class,
        ];

        $this->app->tag(
            $timecodedElementPresetParamValueSerializerStrategies,
            'timecodedElementPresetParamValueSerializerStrategies'
        );

        $this->app->bind(
            PresetParamValueSerializerStrategyInterface::class,
            function (Application $app) {
                return new PresetParamValueSerializerStrategy(
                    ...$app->tagged('timecodedElementPresetParamValueSerializerStrategies')
                );
            }
        );
    }

    public function boot(): void
    {
        ConvertEmptyStringsToNull::skipWhen(static function (Request $request) {
            return $request->routeIs('api.v2.project-screens.timecoded-elements.update');
        });
    }
}
