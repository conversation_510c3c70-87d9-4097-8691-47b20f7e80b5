<?php

declare(strict_types=1);

namespace App\Domain\Notification;

final class NotificationContent
{
    public readonly string $content;

    private function __construct(array $content)
    {
        $this->content = json_encode($content, JSON_THROW_ON_ERROR);
    }

    public static function createForSharelinkCommentCreated(string $projectTitle): self
    {
        return new self(['project_title' => $projectTitle]);
    }

    public static function createForUploadedFiles(string $projectTitle, int $uploadedFilesCount): self
    {
        return new self([
            'project_title' => $projectTitle,
            'uploaded_files_count' => $uploadedFilesCount,
        ]);
    }
}
