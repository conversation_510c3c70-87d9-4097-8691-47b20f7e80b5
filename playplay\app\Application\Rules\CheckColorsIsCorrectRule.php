<?php

namespace App\Application\Rules;

use Illuminate\Contracts\Validation\Rule;

class CheckColorsIsCorrectRule implements Rule
{
    public function passes($attribute, $value): bool
    {
        $values = array_keys($value ?? []);

        return $value === null
            || (
                in_array('text', $values) && in_array('main', $values) && in_array('word', $values)
            );
    }

    public function message()
    {
        return 'CheckColorsIsCorrectRule is not valid';
    }
}
