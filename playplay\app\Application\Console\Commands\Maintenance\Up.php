<?php

namespace App\Application\Console\Commands\Maintenance;

use App\Domain\Maintenance\MaintenanceRepository;
use Illuminate\Foundation\Console\UpCommand as BaseUpCommand;
use Psr\Log\LoggerInterface;

class Up extends BaseUpCommand
{
    private MaintenanceRepository $maintenanceRepository;
    private LoggerInterface $logger;

    public function __construct(
        MaintenanceRepository $maintenanceRepository,
        LoggerInterface $logger,
    ) {
        parent::__construct();
        $this->maintenanceRepository = $maintenanceRepository;
        $this->logger = $logger;
    }

    public function handle(): int
    {
        $currentMaintenance = $this->maintenanceRepository->getCurrent();
        if ($currentMaintenance === null) {
            $this->comment('Application was not in maintenance mode.');

            return 0;
        }

        $this->maintenanceRepository->stopNow($currentMaintenance);

        $this->info('Application is now live.');

        $this->logger->debug('Maintenance mode off');

        return 0;
    }
}
