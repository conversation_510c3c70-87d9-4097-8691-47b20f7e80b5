<?php

namespace App\Application\Http\Controllers\Api\V2\LiraryItem;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\CorporateLibraryItem\CorporateLibraryItemUpdateRequest;
use App\Application\Http\Requests\Api\CorporateLibraryItem\LibraryItemDeleteRequest;
use App\Application\Http\Requests\Api\CorporateLibraryItem\LibraryItemIndexRequest;
use App\Application\Http\Requests\Api\CorporateLibraryItem\LibraryItemMoveRequest;
use App\Application\Http\Requests\Api\CorporateLibraryItem\LibraryItemStoreRequest;
use App\Domain\CorporateLibrary\CorporateLibraryItemRepository;
use App\Domain\Render\RenderMedia\RenderMediaStatus;
use App\Models\CorporateLibraryItem;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;

class LibraryItemController extends BaseController
{
    use AuthorizesRequests;

    private CorporateLibraryItemRepository $repository;
    private Guard $auth;

    public function __construct(CorporateLibraryItemRepository $repository, Guard $auth)
    {
        $this->repository = $repository;
        $this->auth = $auth;
    }

    public function destroyMany(LibraryItemDeleteRequest $request): JsonResponse
    {
        $libraryItemIds = $request->get('ids', []);
        $this->authorize('delete', [CorporateLibraryItem::class, $libraryItemIds]);

        $this->repository->deleteItems($libraryItemIds);

        return $this->sendJsonResponse(new Collection([]), JsonResponse::HTTP_NO_CONTENT);
    }

    public function folders()
    {
        $folders = $this->repository->getFoldersOfCompany($this->auth->user()->company_id);

        return $this->sendJsonResponse(new Collection($folders), JsonResponse::HTTP_OK);
    }

    public function index(LibraryItemIndexRequest $request)
    {
        $page = $request->get('page', 1);
        $perPage = $request->get('per_page', 20);
        $folderId = $request->get('folder_id');
        $orderBy = $request->get('order_by');
        $orderDirection = $request->get('order_direction', 'ASC');
        $orderTable = $orderBy === 'name' ? 'corporate_library_items' : 'raw_medias';

        // TODO: Move into dedicated repository
        $paginator = CorporateLibraryItem::where('company_id', $this->auth->user()->company_id)
            ->where('folder_id', $folderId)
            ->where(
                function (Builder $query) {
                    $query->where('is_folder', '=', true)
                        ->orWhereHas(
                            'rawMedia.lastRender',
                            function (Builder $query) {
                                $query->where('status', '=', RenderMediaStatus::PROCESSED);
                            }
                        );
                }
            )
            ->with('items')
            ->orderBy('is_folder', 'DESC')
            ->when(
                $orderTable === 'raw_medias',
                function (Builder $query) {
                    return $query->leftJoin(
                        'raw_medias',
                        'corporate_library_items.raw_media_id',
                        '=',
                        'raw_medias.id'
                    )
                        ->select('corporate_library_items.*');
                }
            )
            ->when(
                $orderBy,
                function (Builder $query) use ($orderBy, $orderDirection, $orderTable) {
                    return $query->orderBy("{$orderTable}.{$orderBy}", $orderDirection);
                }
            )
            ->orderBy('corporate_library_items.id', 'DESC')
            ->paginate($perPage)
            ->appends(
                [
                    'page' => $page,
                    'per_page' => $perPage,
                ]
            );

        $paginator->getCollection()->transform(
            function (CorporateLibraryItem $libraryItem) {
                return $libraryItem->toVue();
            }
        );

        return $this->sendJsonResponse(new Collection([$paginator]), JsonResponse::HTTP_OK);
    }

    public function move(LibraryItemMoveRequest $request)
    {
        $this->authorize('move', CorporateLibraryItem::class);

        $destinationFolderId = $request->input('items.0.folder_id');
        $mediaToMove = $request->input('items.*.id', []);

        $this->repository->moveMediaToFolder($mediaToMove, $destinationFolderId);

        return $this->sendJsonResponse(new Collection([]), JsonResponse::HTTP_OK);
    }

    public function store(LibraryItemStoreRequest $request): JsonResponse
    {
        $rawMediaId = $request->get('raw_media_id');
        $this->authorize('create', [CorporateLibraryItem::class, $rawMediaId]);

        $libraryItemAttributes = [
            'name' => $request->get('name'),
            'is_folder' => $request->get('is_folder'),
            'folder_id' => $request->get('folder_id'),
            'raw_media_id' => $rawMediaId,
            'company_id' => $this->auth->user()->company_id,
        ];
        $libraryItem = CorporateLibraryItem::create($libraryItemAttributes);

        return $this->sendJsonResponse(new Collection([$libraryItem]), JsonResponse::HTTP_OK);
    }

    public function update(
        CorporateLibraryItem $corporateLibraryItem,
        CorporateLibraryItemUpdateRequest $request
    ): JsonResponse {
        $this->authorize('update', $corporateLibraryItem);
        $corporateLibraryItem->update([
            'name' => $request->input('name'),
        ]);

        return $this->sendJsonResponse(new Collection([$corporateLibraryItem]), JsonResponse::HTTP_OK);
    }
}
