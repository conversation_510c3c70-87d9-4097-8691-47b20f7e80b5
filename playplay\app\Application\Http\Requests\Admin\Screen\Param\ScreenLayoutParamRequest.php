<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\Screen\Param;

use App\Domain\Font\FontSize;
use App\Models\ScreenParams\BaseParam;
use App\Models\ScreenParams\ParamSubtitle;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;
use Illuminate\Routing\Route;
use Illuminate\Validation\Factory;
use Illuminate\Validation\Rule;

class ScreenLayoutParamRequest extends FormRequest
{
    public const RULES_TEXTAREA = [
        'textarea.horizontal_regular_number_line_max' => 'required|numeric|min:1',
        'textarea.vertical_regular_number_line_max' => 'required|numeric|min:1',
        'textarea.square_regular_number_line_max' => 'required|numeric|min:1',
        'textarea.number_line_displayed' => 'required|numeric|min:1',
        'textarea.characters_max_total' => 'required|numeric|min:1',
        'textarea.square_regular_characters_max' => 'required|numeric|min:1',
        'textarea.horizontal_regular_characters_max' => 'required|numeric|min:1',
        'textarea.vertical_regular_characters_max' => 'required|numeric|min:1',
        'textarea.has_wysiwyg_underline' => 'required|numeric|min:0|max:1',
        'textarea.has_wysiwyg_background' => 'required|numeric|min:0|max:1',
        'textarea.has_wysiwyg_color' => 'required|numeric|min:0|max:1',
        'textarea.has_wysiwyg_italic' => 'required|numeric|min:0|max:1',
    ];

    public const RULES_MEDIA = [
        'media.square_max_height' => 'required|numeric|min:1',
        'media.square_max_width' => 'required|numeric|min:1',
        'media.horizontal_max_height' => 'required|numeric|min:1',
        'media.horizontal_max_width' => 'required|numeric|min:1',
        'media.vertical_max_height' => 'required|numeric|min:1',
        'media.vertical_max_width' => 'required|numeric|min:1',
        'media.number_media_max' => 'required|numeric|min:1',
        'media.is_alterable' => 'required|numeric|min:0|max:1',
    ];

    public const RULES_LOGO = [
        'logo.max_dimension' => ['required', 'numeric', 'min:1'],
        'logo.target_dimension' => ['required', 'numeric', 'min:1', 'lessThanMaxDimension'],
    ];

    private const RULES_OPTION_LIST = [
        'option_list.option_list_category_id' => [
            // TODO required once OptionList custom at scale is done
            'sometimes',
            'exists:screen_parameters_option_list_categories,id',
        ],
    ];

    private const RULES_BACKGROUND = [
        'background.option_list_category_id' => ['required', 'exists:screen_parameters_option_list_categories,id'],
    ];

    private const RULES_AUDIO = [];

    public const RULES_SUBTITLE = [
        'subtitle.horizontal_number_line_max' => 'required|numeric|min:1',
        'subtitle.vertical_number_line_max' => 'required|numeric|min:1',
        'subtitle.square_number_line_max' => 'required|numeric|min:1',
        'subtitle.number_lines_per_group' => 'required|numeric|min:1',
        'subtitle.characters_max_total' => 'required|numeric|min:1',
        'subtitle.square_characters_max' => 'required|numeric|min:1',
        'subtitle.horizontal_characters_max' => 'required|numeric|min:1',
        'subtitle.vertical_characters_max' => 'required|numeric|min:1',
        'subtitle.has_wysiwyg_underline' => 'required|numeric|min:0|max:1',
        'subtitle.has_wysiwyg_background' => 'required|numeric|min:0|max:1',
        'subtitle.has_wysiwyg_color' => 'required|numeric|min:0|max:1',
        'subtitle.has_wysiwyg_italic' => 'required|numeric|min:0|max:1',
        'subtitle.apply_on' => 'required|string|in:' . ParamSubtitle::TYPE_MEDIA . ',' . ParamSubtitle::TYPE_AUDIO,
    ];

    public const POST_COMMON_RULES = [
        'param.animaniac_ref' => ['required', 'max:120'],
        'param.required' => ['sometimes', 'boolean'],
        'param.is_premium_only' => ['sometimes', 'boolean'],
        'param.placeholder' => ['max:120'],
        'param.label' => ['max:120'],
        'param.type' => ['required'],
        'param.layout_section' => ['required', 'numeric', 'min:1', 'max:4'],
    ];

    private const PUT_COMMON_RULES = [
        'param.animaniac_ref' => ['required', 'max:120'],
        'param.required' => ['sometimes', 'boolean'],
        'param.is_premium_only' => ['sometimes', 'boolean'],
        'param.placeholder' => ['max:120'],
        'param.label' => ['max:120'],
    ];

    public function __construct(Factory $validationFactory)
    {
        // Check that screen exists
        $this->checkIconExists($validationFactory);
        // Check that target is less or equal than max
        $this->checkLessThanMaxDimension($validationFactory);
        // Check that option list are unique
        $this->checkAreOptionListUnique($validationFactory);
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        $rules = [];

        if ($this->method() === 'POST') {
            $rules = self::POST_COMMON_RULES;
        } elseif ($this->method() === 'PUT') {
            $rules = self::PUT_COMMON_RULES;
        }

        return array_merge($rules, $this->addRulesForParamType());
    }

    private function addRulesForParamType(): array
    {
        /** @var Request $request */
        $request = request();
        if ($this->method() === 'POST') {
            $paramType = $request->get('param')['type'];
        } elseif ($this->method() === 'PUT') {
            /** @var Route $route */
            $route = $request->route();
            /** @var BaseParam $screenParam */
            $screenParam = $route->parameter('screen_param');
            $paramType = $screenParam->type;
        } else {
            return [];
        }

        switch ($paramType) {
            case BaseParam::TYPE_TEXTAREA:
                $rules = self::RULES_TEXTAREA;
                $rules['textarea.horizontal_regular_number_line_max'] = 'required|numeric|min:1';
                $rules['textarea.vertical_regular_number_line_max'] = 'required|numeric|min:1';
                $rules['textarea.square_regular_number_line_max'] = 'required|numeric|min:1';
                $rules['textarea.square_size_regular'] = [
                    'required',
                    'string',
                    Rule::in(FontSize::AVAILABLE_SIZES_FOR_REGULAR),
                ];
                $rules['textarea.horizontal_size_regular'] = [
                    'required',
                    'string',
                    Rule::in(FontSize::AVAILABLE_SIZES_FOR_REGULAR),
                ];
                $rules['textarea.vertical_size_regular'] = [
                    'required',
                    'string',
                    Rule::in(FontSize::AVAILABLE_SIZES_FOR_REGULAR),
                ];

                return $rules;
            case BaseParam::TYPE_SUBTITLE:
                $rules = self::RULES_SUBTITLE;
                $rules['subtitle.square_size_regular'] = [
                    'required',
                    'string',
                    Rule::in(FontSize::AVAILABLE_SIZES_FOR_REGULAR),
                ];
                $rules['subtitle.horizontal_size_regular'] = [
                    'required',
                    'string',
                    Rule::in(FontSize::AVAILABLE_SIZES_FOR_REGULAR),
                ];
                $rules['subtitle.vertical_size_regular'] = [
                    'required',
                    'string',
                    Rule::in(FontSize::AVAILABLE_SIZES_FOR_REGULAR),
                ];

                return $rules;
            case BaseParam::TYPE_MEDIA:
                return self::RULES_MEDIA;
            case BaseParam::TYPE_LOGO:
                return self::RULES_LOGO;
            case BaseParam::TYPE_OPTION_LIST:
                return self::RULES_OPTION_LIST;
            case 'background':
                return self::RULES_BACKGROUND;
            case BaseParam::TYPE_AUDIO:
                return self::RULES_AUDIO;
            default:
                return [];
        }
    }

    private function checkAreOptionListUnique(Factory $validationFactory): void
    {
        $validationFactory->extendImplicit(
            'areOptionListUnique',
            function ($attribute, $value, $parameters, $validator) {
                return count($value) === collect($value)->unique()->count();
            }
        );
    }

    private function checkIconExists(Factory $validationFactory): void
    {
        $validationFactory->extendImplicit(
            'iconExists',
            function ($attribute, $value, $parameters) {
                return array_key_exists('icon', $value) || array_key_exists('icon_url', $value);
            }
        );
    }

    private function checkLessThanMaxDimension(Factory $validationFactory): void
    {
        $validationFactory->extendImplicit(
            'lessThanMaxDimension',
            function ($attribute, $value, $parameters, $validator) {
                $maxDimension = $validator->getData()[BaseParam::TYPE_LOGO]['max_dimension'];

                return $value <= $maxDimension;
            }
        );
    }
}
