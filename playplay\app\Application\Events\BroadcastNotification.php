<?php

namespace App\Application\Events;

use App\Models\Notification;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Queue\SerializesModels;

final class BroadcastNotification implements ShouldBroadcast
{
    use SerializesModels;

    public Notification $notification;

    public function __construct(Notification $notification)
    {
        $this->notification = $notification;
    }

    public function broadcastAs(): string
    {
        return 'broadcast-notification';
    }

    public function broadcastOn(): Channel
    {
        return new PrivateChannel("user.{$this->notification->user_id}");
    }

    public function broadcastWith(): array
    {
        return [
            'data' => [
                'id' => $this->notification->id,
                'type' => $this->notification->type,
                'content' => json_decode($this->notification->content, true, 512, JSON_THROW_ON_ERROR),
                'username' => $this->notification->username,
                'url' => $this->notification->url,
                'viewed' => $this->notification->viewed,
                'created_at' => $this->notification->created_at->format('Y-m-d H:i:s'),
            ],
        ];
    }
}
