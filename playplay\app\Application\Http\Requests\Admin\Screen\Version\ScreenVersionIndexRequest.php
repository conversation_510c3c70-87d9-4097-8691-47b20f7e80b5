<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\Screen\Version;

use Illuminate\Foundation\Http\FormRequest;

final class ScreenVersionIndexRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'is_generic' => ['sometimes', 'boolean'],
        ];
    }

    public function authorize(): bool
    {
        return true;
    }
}
