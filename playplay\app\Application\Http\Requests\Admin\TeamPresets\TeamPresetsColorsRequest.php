<?php

namespace App\Application\Http\Requests\Admin\TeamPresets;

use Illuminate\Foundation\Http\FormRequest;

class TeamPresetsColorsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'colors.text.display' => ['boolean'],
            'colors.text.values' => ['required', 'min:1'],
            'colors.text.values.*' => ['rgbColor'],
            'colors.text.default' => ['required', 'in_array:colors.text.values.*', 'rgbColor'],

            'colors.main.display' => ['boolean'],
            'colors.main.values' => ['required', 'min:1'],
            'colors.main.values.*' => ['rgbColor'],
            'colors.main.default' => ['required', 'in_array:colors.main.values.*', 'rgbColor'],

            'colors.word.display' => ['boolean'],
            'colors.word.values' => ['required', 'min:1'],
            'colors.word.values.*' => ['rgbColor'],
            'colors.word.default' => ['required', 'in_array:colors.word.values.*', 'rgbColor'],
        ];
    }
}
