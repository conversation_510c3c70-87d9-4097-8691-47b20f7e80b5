<?php

namespace App\Application\Console\Commands\Database\ClearForLightDB;

use Illuminate\Support\Facades\DB;

class DeleteDataForLightDatabaseService
{
    private DeleteCompanyDataService $deleteCompanyDataService;
    private DeleteMotionDataService $deleteMotionDataService;
    private const TABLES_TO_TRUNCATE = [
        'comments',
        'corporate_library_items',
        'daily_usages',
        'deprecated_raw_medias',
        'email_invitations',
        'folders',
        'heavy_media_errors',
        'history_v1_videos',
        'history_v2_render_projects',
        'notifications',
        'project_getty_asset_usages',
        'render_medias_render_screens_html',
        'render_medias_snapshots',
        'render_project_html_render_screens_html',
        'render_project_music_render_media',
        'render_project_voiceover_render_media',
        'render_projects_html',
        'render_screens_html',
        'render_screen_cutaway_shot_render_media',
        'render_stories',
        'render_stories_legacy',
        'render_subtitles',
        'shareable_links',
        'snapshots',
        'translation_jobs',
        'two_factor_auth_codes',
        'user_raw_media_favorites',
        'user_raw_medias',
        'users_permissions',
        'video_downloads',
        'video_downloads_legacy',
        'team_timecoded_element_preset_scheduled_activations',
        'password_resets',
    ];
    private const TEMPLATE_CATEGORY_TO_KEEP = 192;

    public function __construct(
        DeleteCompanyDataService $deleteCompanyDataService,
        DeleteMotionDataService $deleteMotionDataService,
    ) {
        $this->deleteCompanyDataService = $deleteCompanyDataService;
        $this->deleteMotionDataService = $deleteMotionDataService;
    }

    public function deleteUselessData(): void
    {
        DB::statement('SET foreign_key_checks = 0;');
        $this->deleteCompanyDataService->deleteCompanyDataThatIsNotLinkedToPlayPlayOrCypress();
        $this->deleteMotionDataService->deleteScreenAndTemplateDataThatIsNotGenericExceptThoseOf(self::TEMPLATE_CATEGORY_TO_KEEP);
        $this->deleteMotionDataService->deleteTimecodedElementDataThatIsNotGeneric();
        $this->truncateTables();

        DB::statement('SET foreign_key_checks = 1;');
    }

    private function truncateTables(): void
    {
        foreach (self::TABLES_TO_TRUNCATE as $tableToTruncate) {
            DB::table($tableToTruncate)->truncate();
        }
    }
}
