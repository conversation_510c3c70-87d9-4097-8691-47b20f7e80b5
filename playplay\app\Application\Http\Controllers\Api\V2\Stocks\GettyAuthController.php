<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Stocks;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Stock\Getty\GettyAuthorizationRequest;
use App\Domain\Stock\GettyTokenRepository;
use App\Infrastructure\Stock\Getty\GettyAuthorizationService;
use App\Infrastructure\Stock\Getty\GettyCodeGenerator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

class GettyAuthController extends BaseController
{
    /** @var GettyCodeGenerator */
    private $codeGenerator;
    /** @var Redirector */
    private $redirector;
    /** @var GettyAuthorizationService */
    private $authorizationService;
    /** @var GettyTokenRepository */
    private $gettyTokenRepository;

    public function __construct(
        GettyCodeGenerator $codeGenerator,
        Redirector $redirector,
        GettyAuthorizationService $authorizationService,
        GettyTokenRepository $gettyTokenRepository
    ) {
        $this->codeGenerator = $codeGenerator;
        $this->redirector = $redirector;
        $this->authorizationService = $authorizationService;
        $this->gettyTokenRepository = $gettyTokenRepository;
    }

    public function authorizationCallback(GettyAuthorizationRequest $request): RedirectResponse
    {
        $gettyToken = $this->authorizationService->getAccessTokenForUserFromCode(
            $request->user(),
            $request->get('code'),
            $request->session()->get('code_verifier')
        );

        $this->gettyTokenRepository->insertOrUpdate($gettyToken);

        return $this->redirector->to('app/profile');
    }

    public function authorizationUrl(Request $request): JsonResponse
    {
        $codeVerifier = $this->codeGenerator->getCode();
        $request->session()->put('code_verifier', $codeVerifier);
        $codeChallenge = $this->codeGenerator->encode($codeVerifier);

        return $this->sendJsonResponse(
            new Collection([
                ['authorization_url' => $this->authorizationService->getAuthorizationUrl($codeChallenge)],
            ]),
            Response::HTTP_OK
        );
    }
}
