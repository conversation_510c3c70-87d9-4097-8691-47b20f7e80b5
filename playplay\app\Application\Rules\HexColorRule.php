<?php

namespace App\Application\Rules;

use Illuminate\Contracts\Validation\Rule;

class HexColorRule implements Rule
{
    /**
     * Create a new rule instance.
     */
    public function __construct()
    {
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('validation.hex-color');
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed  $value
     *
     * @return bool
     */
    public function passes($attribute, $value)
    {
        return preg_match('/^#[a-fA-F0-9]{6}/m', $value) === 1;
    }
}
