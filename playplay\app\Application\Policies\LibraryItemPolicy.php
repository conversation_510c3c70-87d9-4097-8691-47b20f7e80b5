<?php

namespace App\Application\Policies;

use App\Domain\UserTeam\UserTeamAppPermissionService;
use App\Models\CorporateLibraryItem;
use App\Models\Permissions\AppPermission;
use App\Models\RawMedia;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class LibraryItemPolicy extends DefaultPolicy
{
    use HandlesAuthorization;

    private UserTeamAppPermissionService $userTeamAppPermissionService;

    public function __construct(UserTeamAppPermissionService $userTeamAppPermissionService)
    {
        $this->userTeamAppPermissionService = $userTeamAppPermissionService;
    }

    public function create(User $user, ?int $rawMediaId): bool
    {
        $canManageCorpMedia = $this->userTeamAppPermissionService->userHasAppPermissionInAtLeastOneTeam(
            $user,
            $user->teams->pluck('id')->toArray(),
            AppPermission::CAN_MANAGE_CORP_MEDIA
        );
        if (!$canManageCorpMedia) {
            return false;
        }

        if (!$user->hasMediaEnterpriseFeature($user->company_id)) {
            return false;
        }

        if ($rawMediaId === null) {
            return true;
        }

        return $user->can('view', RawMedia::find($rawMediaId));
    }

    public function delete(User $user, array $ids): bool
    {
        $canManageCorpMedia = $this->userTeamAppPermissionService->userHasAppPermissionInAtLeastOneTeam(
            $user,
            $user->teams->pluck('id')->toArray(),
            AppPermission::CAN_MANAGE_CORP_MEDIA
        );
        if (!$canManageCorpMedia) {
            return false;
        }

        return $user->hasMediaEnterpriseFeature($user->company_id)
            && CorporateLibraryItem::whereIn('id', $ids)
                ->where('company_id', $user->company_id)
                ->count() === count($ids);
    }

    public function move(User $user): bool
    {
        $canManageCorpMedia = $this->userTeamAppPermissionService->userHasAppPermissionInAtLeastOneTeam(
            $user,
            $user->teams->pluck('id')->toArray(),
            AppPermission::CAN_MANAGE_CORP_MEDIA
        );
        if (!$canManageCorpMedia) {
            return false;
        }

        return $user->hasMediaEnterpriseFeature($user->company_id);
    }

    public function update(User $user, CorporateLibraryItem $corporateLibraryItem): bool
    {
        if ($corporateLibraryItem->company_id !== $user->company_id) {
            return false;
        }

        $canManageCorpMedia = $this->userTeamAppPermissionService->userHasAppPermissionInAtLeastOneTeam(
            $user,
            $user->teams->pluck('id')->toArray(),
            AppPermission::CAN_MANAGE_CORP_MEDIA
        );
        if (!$canManageCorpMedia) {
            return false;
        }

        return $user->hasMediaEnterpriseFeature($user->company_id);
    }
}
