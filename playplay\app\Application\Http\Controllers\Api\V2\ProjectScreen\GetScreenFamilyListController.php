<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\ProjectScreen;

use App\Application\Http\Controllers\Api\BaseController;
use App\Models\Project;
use App\Models\ProjectScreen;
use App\Services\ProjectScreen\ScreenFamilyNormalizer;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use InvalidArgumentException;
use Symfony\Component\HttpFoundation\Response;

final class GetScreenFamilyListController extends BaseController
{
    use AuthorizesRequests;

    public function __invoke(
        Project $project,
        ProjectScreen $projectScreen,
        ScreenFamilyNormalizer $screenFamilyNormalizer
    ): JsonResponse {
        if ($projectScreen->project_id !== $project->id) {
            throw new InvalidArgumentException('Non matching project screen and project');
        }

        $this->authorize('view', $projectScreen);

        return $this->sendJsonResponse(
            new Collection($screenFamilyNormalizer->normalize($projectScreen)),
            Response::HTTP_OK
        );
    }
}
