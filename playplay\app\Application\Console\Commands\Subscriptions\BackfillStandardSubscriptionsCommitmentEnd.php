<?php

namespace App\Application\Console\Commands\Subscriptions;

use App\Domain\Billing\SubscriptionService as BillingSubscriptionService;
use App\Domain\Subscription\SubscriptionRepository;
use App\Models\Subscription;
use Exception;
use Illuminate\Console\Command;

final class BackfillStandardSubscriptionsCommitmentEnd extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscriptions:backfill-commitment-end';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Backfills the commitment_end value of standard annual subscriptions that don't have any";

    private SubscriptionRepository $subscriptionRepository;
    private BillingSubscriptionService $billingSubscriptionService;

    public function __construct(
        SubscriptionRepository $subscriptionRepository,
        BillingSubscriptionService $billingSubscriptionService
    ) {
        parent::__construct();

        $this->subscriptionRepository = $subscriptionRepository;
        $this->billingSubscriptionService = $billingSubscriptionService;
    }

    /**
     * @throws Exception
     */
    public function handle(): int
    {
        $standardSubscriptions = $this->subscriptionRepository->getAllAnnualStandardSubscriptionsWithoutCommitmentEnd();
        /** @var Subscription $subscription */
        foreach ($standardSubscriptions as $subscription) {
            $startedAt = $subscription->started_at->getTimestamp();

            $subscription->update([
                'commitment_end' => $this->billingSubscriptionService->getAnnualPlanCommitmentEndDate($startedAt),
            ]);
        }

        return 0;
    }
}
