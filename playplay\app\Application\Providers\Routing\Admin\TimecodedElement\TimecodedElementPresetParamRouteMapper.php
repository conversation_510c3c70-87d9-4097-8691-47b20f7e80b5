<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Admin\TimecodedElement;

use App\Application\Http\Controllers\Admin\TimecodedElement\TimecodedElementPresetParamController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class TimecodedElementPresetParamRouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/timecoded-element-preset-params',
            'as' => 'timecoded-element-preset-params.',
        ], static function (Router $router) {
            $router->get('/create', [TimecodedElementPresetParamController::class, 'create'])->name('create');
            $router->post('/', [TimecodedElementPresetParamController::class, 'store'])->name('store');
            $router->group(
                ['prefix' => '/{timecodedElementPresetParam}'],
                static function () use ($router) {
                    $router
                        ->delete('/', [TimecodedElementPresetParamController::class, 'destroy'])
                        ->name('destroy');
                    $router
                        ->put('/', [TimecodedElementPresetParamController::class, 'update'])
                        ->name('update');
                    $router
                        ->get('/edit', [TimecodedElementPresetParamController::class, 'edit'])
                        ->name('edit');
                }
            );
        });
    }
}
