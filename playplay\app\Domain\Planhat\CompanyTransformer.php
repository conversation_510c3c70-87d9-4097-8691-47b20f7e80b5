<?php

declare(strict_types=1);

namespace App\Domain\Planhat;

use App\Models\Company;

final class CompanyTransformer
{
    public function transform(Company $company): array
    {
        $speechToTextFeature = $company->features->firstWhere('name', 'has_speech_to_text');
        $gettyFeature = $company->features->firstWhere('name', 'has_getty');
        $gettyEditoFeature = $company->features->firstWhere('name', 'has_getty_edito');

        return [
            'externalId' => $company->id,
            'name' => $company->name,
            'custom' => [
                'Type' => $company->type,
                'Arabic' => $company->arabic > 0,
                'Speech-to-text' => (bool) ($speechToTextFeature !== null ? $speechToTextFeature->value : 0),
                'Getty' => (bool) ($gettyFeature !== null ? $gettyFeature->value : 0),
                'Getty Editor' => (bool) ($gettyEditoFeature !== null ? $gettyEditoFeature->value : 0),
                'Activity Status' => $company->activity_status,
            ],
        ];
    }
}
