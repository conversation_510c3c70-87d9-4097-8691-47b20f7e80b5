<?php

declare(strict_types=1);

namespace App\Domain\CutawayShot;

use App\Domain\RawMedia\RawMediaType;
use App\Domain\Render\RenderMedia\Crop\Crop;
use App\Domain\Render\RenderMedia\TargetDimension;
use App\Domain\Render\RenderMedia\Trim;
use JsonSerializable;

final class CutawayShotVideoRenderMediaData implements JsonSerializable
{
    public readonly Crop $crop;
    public readonly float $duration;
    public readonly ?string $keepSize;
    public readonly float $start;
    public readonly string $rawMediaType;
    public readonly TargetDimension $targetDimension;
    public readonly Trim $trim;

    public function __construct(
        Crop $crop,
        ?string $keepSize,
        float $start,
        TargetDimension $targetDimension,
        Trim $trim,
    ) {
        $this->crop = $crop;
        $this->keepSize = $keepSize;
        $this->start = $start;
        $this->targetDimension = $targetDimension;
        $this->trim = $trim;
        $this->duration = $trim->end - $trim->start;
        $this->rawMediaType = RawMediaType::VIDEO;
    }

    public function isEqualTo(
        CutawayShotImageRenderMediaData|CutawayShotVideoRenderMediaData|null $renderMediaData
    ): bool {
        if ($renderMediaData === null) {
            return false;
        }

        return $this->crop->isEqualTo($renderMediaData->crop)
            && $this->duration === $renderMediaData->duration
            && $this->keepSize === $renderMediaData->keepSize
            && $this->start === $renderMediaData->start
            && $this->targetDimension->isEqualTo($renderMediaData->targetDimension)
            && $this->trim->isEqualTo($renderMediaData->trim);
    }

    public function jsonSerialize(): array
    {
        return [
            'type' => $this->rawMediaType,
            'trimStart' => $this->trim->start,
            'trimEnd' => $this->trim->end,
            'crop' => $this->crop->toArray(),
            'start' => $this->start,
            'keepSize' => $this->keepSize,
            'duration' => $this->duration,
            'targetDimension' => $this->targetDimension->jsonSerialize(),
        ];
    }
}
