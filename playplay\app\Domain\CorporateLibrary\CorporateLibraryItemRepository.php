<?php

declare(strict_types=1);

namespace App\Domain\CorporateLibrary;

use App\Models\CorporateLibraryItem;
use Illuminate\Support\Collection;

interface CorporateLibraryItemRepository
{
    public function deleteItems(array $ids): void;

    /**
     * @return Collection|CorporateLibraryItem[]
     */
    public function getFoldersOfCompany(int $companyId): Collection;

    public function moveMediaToFolder(array $mediaIds, ?int $folderId): void;
}
