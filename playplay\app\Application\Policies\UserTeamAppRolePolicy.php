<?php

declare(strict_types=1);

namespace App\Application\Policies;

use App\Models\Permissions\AppRole;
use App\Models\User;
use App\Models\UserTeamAppRole;

final class UserTeamAppRolePolicy
{
    public function before(User $authUser): ?bool
    {
        return $authUser->is_admin ?: null;
    }

    public function updateAppPermissions(User $authUser, UserTeamAppRole $userTeamAppRole): bool
    {
        return $authUser->userTeamAppRoles
                ->firstWhere('team_id', $userTeamAppRole->team_id)->appRole->name === AppRole::ROLE_OWNER;
    }
}
