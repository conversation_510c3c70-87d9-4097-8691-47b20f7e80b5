<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\HealthCheck\HealthzService;
use App\Domain\Queueing\QueueMetadataService;
use App\Infrastructure\HealthCheck\AmazonSqsQueueCheck;
use App\Infrastructure\HealthCheck\EloquentDatabaseCheck;
use App\Infrastructure\HealthCheck\GoogleCloudStorageCheck;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Support\ServiceProvider;

final class HealthCheckServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(
            AmazonSqsQueueCheck::class,
            static fn(Application $app) => new AmazonSqsQueueCheck(
                $app->makeWith(QueueMetadataService::class, ['queueName' => 'queue_processing'])
            )
        );

        $this->app->tag([
            EloquentDatabaseCheck::class,
            AmazonSqsQueueCheck::class,
            GoogleCloudStorageCheck::class,
        ], 'infrastructureCheckers');

        $this->app->bind(HealthzService::class, function (Application $app) {
            return new HealthzService(
                $app->tagged('infrastructureCheckers')
            );
        });
    }
}
