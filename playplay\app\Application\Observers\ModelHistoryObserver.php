<?php

namespace App\Application\Observers;

use App\Domain\Logs\ModelHistory\ModelHistorizables;
use App\Domain\Logs\ModelHistory\ModelHistoryFactory;
use App\Domain\Logs\ModelHistory\ModelHistoryService;
use App\Domain\Logs\ModelHistory\UnableToHistorizeModelException;
use App\Domain\Request\RequestService;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Database\Eloquent\Model;
use Throwable;

class ModelHistoryObserver
{
    /**
     * Handle events after all transactions are committed.
     *
     * @var bool
     */
    public bool $afterCommit = true;

    private RequestService $requestService;

    private ModelHistorizables $modelHistorizables;

    private ModelHistoryFactory $modelHistoryFactory;

    private ModelHistoryService $modelHistoryService;

    private Dispatcher $eventDispatcher;

    public function __construct(
        RequestService $requestService,
        ModelHistorizables $modelHistorizables,
        ModelHistoryFactory $modelHistoryFactory,
        ModelHistoryService $modelHistoryService,
        Dispatcher $eventDispatcher
    ) {
        $this->requestService = $requestService;
        $this->modelHistorizables = $modelHistorizables;
        $this->modelHistoryFactory = $modelHistoryFactory;
        $this->modelHistoryService = $modelHistoryService;
        $this->eventDispatcher = $eventDispatcher;
    }

    public function created(Model $model): void
    {
        $this->logModelChange($model, 'created');
    }

    public function updated(Model $model): void
    {
        $this->logModelChange($model, 'updated');
    }

    public function deleted(Model $model): void
    {
        $this->logModelChange($model, 'deleted');
    }

    public function restored(Model $model): void
    {
        $this->logModelChange($model, 'restored');
    }

    public function forceDeleted(Model $model): void
    {
        $this->logModelChange($model, 'force-deleted');
    }

    public function pivotAttached(Model $model, string $relationName): void
    {
        if ($this->modelHistorizables->isHistorizableRelation($model, $relationName)) {
            $relationNotLoaded = !$model->relationLoaded($relationName)
                || $model->getRelationValue($relationName)->isEmpty();

            if ($relationNotLoaded) {
                // Apparently when attaching new data through a model's relation,
                // this data is not loaded into the model itself, and we have to load it in order to log it
                // However we do load it only if it was not already done
                $model->load($relationName);
            }

            $this->logModelChange($model, 'updated');

            if ($relationNotLoaded) {
                $model->unsetRelation($relationName);
            }
        }
    }

    public function pivotDetached(Model $model, string $relationName): void
    {
        if ($this->modelHistorizables->isHistorizableRelation($model, $relationName)) {
            $this->logModelChange($model->load($relationName), 'updated');
        }
    }

    public function pivotUpdated(Model $model, string $relationName): void
    {
        if ($this->modelHistorizables->isHistorizableRelation($model, $relationName)) {
            $this->logModelChange($model->load($relationName), 'updated');
        }
    }

    private function logModelChange(Model $model, string $actionName): void
    {
        if ($this->requestService->isBackofficeRoute()) {
            try {
                $modelHistory = $this->modelHistoryFactory->create(
                    $this->requestService->getId(),
                    $actionName,
                    $model
                );
                $this->modelHistoryService->addModelHistory($modelHistory);
            } catch (Throwable $e) {
                $this->eventDispatcher->dispatch(
                    new UnableToHistorizeModelException($e->getMessage(), $e->getCode())
                );
            }
        }
    }
}
