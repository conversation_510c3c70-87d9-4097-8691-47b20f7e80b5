<?php

declare(strict_types=1);

namespace App\Domain\Project\RawMedia;

enum RelationType: int
{
    case LIBRARY = 0;
    case VOICEOVER = 1;
    case MUSIC = 2;

    private const TYPES_AS_STRING = [
        'library' => self::LIBRARY,
        'voiceover' => self::VOICEOVER,
        'music' => self::MUSIC,
    ];

    public static function from<PERSON>ey(?string $key): self
    {
        $value = self::TYPES_AS_STRING[$key]->value ?? self::LIBRARY->value;

        return static::from($value);
    }

    public static function getAllKeys(): array
    {
        return array_keys(self::TYPES_AS_STRING);
    }
}
