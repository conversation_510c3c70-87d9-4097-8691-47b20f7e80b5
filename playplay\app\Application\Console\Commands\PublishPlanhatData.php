<?php

namespace App\Application\Console\Commands;

use App\Domain\Planhat\PlanhatAPIException;
use App\Domain\Planhat\PlanhatService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use Psr\Log\LoggerInterface;

class PublishPlanhatData extends Command
{
    protected $signature = 'bi:planhat {date_start? : Date start for usage data default only yesterday}';
    private PlanhatService $planhatService;

    public function __construct(PlanhatService $planhatService)
    {
        parent::__construct();
        $this->planhatService = $planhatService;
    }

    public function handle(): int
    {
        if (!App::environment('production')) {
            $this->error('This command can only be used in production');

            return 1;
        }

        $dateStart = $this->argument('date_start') ?? today()->subDay()->format('Y-m-d');

        $this->planhatService->upsertUsers();
        $this->planhatService->upsertCompanies();
        $this->planhatService->insertCompanyUsage($dateStart);
        $this->planhatService->insertUserUsage($dateStart);

        return 0;
    }
}
