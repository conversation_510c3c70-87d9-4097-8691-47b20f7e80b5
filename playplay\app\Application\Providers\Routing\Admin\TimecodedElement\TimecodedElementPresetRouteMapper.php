<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Admin\TimecodedElement;

use App\Application\Http\Controllers\Admin\TimecodedElement\TimecodedElementPresetController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class TimecodedElementPresetRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/timecoded-element-presets',
            'as' => 'timecoded-element-presets.',
        ], static function (Router $router) {
            $router->get('/create', [TimecodedElementPresetController::class, 'create'])->name('create');
            $router->post('/', [TimecodedElementPresetController::class, 'store'])->name('store');

            Route::group([
                'prefix' => '/{timecodedElementPreset}',
            ], static function (Router $router) {
                $router->get('/edit', [TimecodedElementPresetController::class, 'edit'])->name('edit');
                $router->put('/', [TimecodedElementPresetController::class, 'update'])->name('update');
                $router->delete('/', [TimecodedElementPresetController::class, 'destroy'])->name('destroy');
                $router
                    ->get('/danger-zone', [TimecodedElementPresetController::class, 'showDangerZone'])
                    ->name('danger-zone');

                (new TimecodedElementPresetParamRouteMapper())->map();
            });
        });
    }
}
