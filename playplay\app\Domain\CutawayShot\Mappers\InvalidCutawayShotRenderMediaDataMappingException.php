<?php

declare(strict_types=1);

namespace App\Domain\CutawayShot\Mappers;

use DomainException;
use Exception;
use TypeError;

final class InvalidCutawayShotRenderMediaDataMappingException extends DomainException
{
    public function __construct(Exception | TypeError $e = null)
    {
        parent::__construct(
            $e?->getMessage() ?? '',
            $e?->getCode() ?? 0,
            $e
        );
    }
}
