<?php

namespace App\Application\Rules;

use Illuminate\Contracts\Validation\Rule;

class CountryISO3166Alpha2Rule implements Rule
{
    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('validation.country_iso3166_alpha2');
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed  $value
     *
     * @return bool
     */
    public function passes($attribute, $value)
    {
        return is_string($value) && array_key_exists($value, config('countries.ISO_3166_ALPHA2_CODES'));
    }
}
