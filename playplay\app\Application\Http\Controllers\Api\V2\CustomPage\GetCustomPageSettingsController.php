<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\CustomPage;

use App\Application\Http\Controllers\Api\BaseController;
use App\Domain\User\Action\UserAction;
use App\Domain\User\Action\UserActionService;
use App\Infrastructure\CustomPage\Serializers\CustomPageSettingsSerializer;
use App\Models\CustomPage;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

final class GetCustomPageSettingsController extends BaseController
{
    use AuthorizesRequests;

    private CustomPageSettingsSerializer $customPageSettingsSerializer;
    private UserActionService $userActionService;

    public function __construct(
        CustomPageSettingsSerializer $customPageSettingsSerializer,
        UserActionService $userActionService,
    ) {
        $this->customPageSettingsSerializer = $customPageSettingsSerializer;
        $this->userActionService = $userActionService;
    }

    public function __invoke(CustomPage $customPage): JsonResponse
    {
        $this->authorize('view', [CustomPage::class, $customPage]);

        if ($customPage->project->trashed() === true) {
            throw new NotFoundHttpException();
        }

        $this->userActionService->addUserAction(
            new UserAction(
                'custom-page-settings-accessed',
                [],
                $customPage->project?->team_id,
                $customPage->project_id
            )
        );

        return $this->sendJsonResponse(
            new Collection([$this->customPageSettingsSerializer->serialize($customPage)]),
            Response::HTTP_OK
        );
    }
}
