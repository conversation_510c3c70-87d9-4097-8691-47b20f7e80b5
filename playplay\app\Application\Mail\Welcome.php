<?php

declare(strict_types=1);

namespace App\Application\Mail;

use App\Models\User;
use Illuminate\Mail\Mailable;

final class Welcome extends Mailable
{
    private User $user;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    public function build(): self
    {
        return $this->from('<EMAIL>', __('email.from'))
            ->subject(__('email.welcome.subject'))
            ->markdown('emails.welcome', ['name' => $this->user->first_name]);
    }
}
