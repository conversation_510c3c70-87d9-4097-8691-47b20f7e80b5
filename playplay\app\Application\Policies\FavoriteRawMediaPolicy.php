<?php

namespace App\Application\Policies;

use App\Models\FavoriteRawMedia;
use App\Models\RawMedia;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class FavoriteRawMediaPolicy extends DefaultPolicy
{
    use HandlesAuthorization;

    public function create(User $user, RawMedia $rawMedia): bool
    {
        if ($rawMedia->stock_id !== null) {
            return true;
        }

        return $rawMedia->users->pluck('company_id')->contains($user->company_id);
    }

    public function destroy(User $user, FavoriteRawMedia $favoriteRawMedia): bool
    {
        return $favoriteRawMedia->user_id === $user->id;
    }
}
