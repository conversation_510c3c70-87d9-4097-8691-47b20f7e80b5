<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Company\Repositories\CompanyHistoryRepository;
use App\Domain\Company\Repositories\CompanyRepository;
use App\Infrastructure\Company\Repositories\EloquentCompanyHistoryRepository;
use App\Infrastructure\Company\Repositories\EloquentCompanyRepository;
use Illuminate\Support\ServiceProvider;

final class CompanyServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(CompanyRepository::class, EloquentCompanyRepository::class);
        $this->app->bind(CompanyHistoryRepository::class, EloquentCompanyHistoryRepository::class);
    }
}
