<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Request\RequestService;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;
use Ramsey\Uuid\UuidFactory;

final class RequestServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->singleton(RequestService::class, function ($app) {
            /* @var Request $request */
            $request = $app->make(Request::class);

            return new RequestService(
                $app->make(UuidFactory::class),
                $request->path()
            );
        });
    }
}
