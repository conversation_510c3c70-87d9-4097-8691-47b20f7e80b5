<?php

namespace App\Application\Events;

use App\Models\ProjectScreen;
use Illuminate\Queue\SerializesModels;

class ProjectScreenUpdated
{
    use SerializesModels;

    private ProjectScreen $projectScreen;
    private bool $shouldRefreshPreview;

    public function __construct(ProjectScreen $projectScreen, bool $shouldRefreshPreview = true)
    {
        $this->projectScreen = $projectScreen;
        $this->shouldRefreshPreview = $shouldRefreshPreview;
    }

    public function getProjectScreen(): ?ProjectScreen
    {
        return $this->projectScreen;
    }

    public function getShouldRefreshPreview(): bool
    {
        return $this->shouldRefreshPreview;
    }
}
