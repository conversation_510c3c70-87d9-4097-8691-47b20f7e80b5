<?php

namespace App\Application\Providers;

use App\Domain\ProjectScreen\Param\Subtitles\SRTSubtitleTransformer as Transformer;
use App\Infrastructure\ProjectScreen\Param\Subtitles\CaptioningSRTSubtitleTransformer;
use Illuminate\Support\ServiceProvider;

final class ProjectScreenSubtitleProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(Transformer::class, CaptioningSRTSubtitleTransformer::class);
    }
}
