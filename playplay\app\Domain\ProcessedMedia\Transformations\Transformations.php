<?php

namespace App\Domain\ProcessedMedia\Transformations;

use App\Domain\Render\RenderMedia\Crop\Crop;
use App\Domain\Render\RenderMedia\Crop\CropFactory;

class Transformations
{
    private ?Crop $crop;
    private ?string $keepSize;
    private ?bool $legacyKeepSize;
    /** @var array<string, int>|null */
    private ?array $targetDimension;
    /** @var array<string, float>|null */
    private ?array $trim;

    public static function createFromKeepSizeData(
        string $keepSize,
        array $targetDimension,
        ?array $crop = null,
        ?float $trimStart = null,
        ?float $trimEnd = null
    ): Transformations {
        return new self(
            $crop,
            $keepSize,
            null,
            $targetDimension,
            [
                'start' => $trimStart,
                'end' => $trimEnd,
            ]
        );
    }

    public static function createFromLegacyKeepSizeData(
        bool $legacyKeepSize,
        array $targetDimension,
        ?array $crop = null
    ): Transformations {
        return new self(
            $crop,
            null,
            $legacyKeepSize,
            $targetDimension
        );
    }

    public static function createFromCropData(
        array $crop,
        array $targetDimension,
        ?float $trimStart = null,
        ?float $trimEnd = null
    ): Transformations {
        return new self(
            $crop,
            null,
            null,
            $targetDimension,
            [
                'start' => $trimStart,
                'end' => $trimEnd,
            ]
        );
    }

    private function __construct(
        ?array $crop,
        ?string $keepSize,
        ?bool $legacyKeepSize,
        ?array $targetDimension,
        ?array $trim = null
    ) {
        $this->crop = CropFactory::createCropFromArray($crop ?? []);
        $this->keepSize = $keepSize;
        $this->legacyKeepSize = $legacyKeepSize;
        $this->targetDimension = $targetDimension;
        $this->trim = $trim;
    }

    public function toArray(): array
    {
        return [
            'crop' => $this->crop?->toArray() ?: null,
            'keepSize' => $this->keepSize ?? null,
            'legacyKeepSize' => $this->legacyKeepSize ?? null,
            'targetDimension' => $this->targetDimension,
            'trimStart' => $this->trim['start'] ?? null,
            'trimEnd' => $this->trim['end'] ?? null,
        ];
    }
}
