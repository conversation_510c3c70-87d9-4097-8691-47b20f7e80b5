<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V3\Auth;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Auth\LoginWithCaptchaRequest;
use App\Domain\Captcha\CaptchaValidationInterface;
use App\Domain\User\UserRepository;
use Illuminate\Contracts\Cache\Repository as CacheRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotAcceptableHttpException;

final class LoginWithCaptchaController extends BaseController
{
    private CacheRepository $cacheRepository;

    private CaptchaValidationInterface $captchaValidation;

    private UserRepository $userRepository;

    public function __construct(
        UserRepository $userRepository,
        CacheRepository $cacheRepository,
        CaptchaValidationInterface $captchaValidation
    ) {
        $this->userRepository = $userRepository;
        $this->cacheRepository = $cacheRepository;
        $this->captchaValidation = $captchaValidation;
    }

    public function __invoke(LoginWithCaptchaRequest $request): JsonResponse
    {
        $credentials = $request->only('email', 'password');
        $remember = $request->input('remember', false);
        $captchaToken = $request->input('captcha_token');

        $user = $this->userRepository->getUserFromEmail($request->input('email'));

        if ($this->userRepository->userMustLoginWithSSO($user?->email)) {
            throw new NotAcceptableHttpException('email.sso_mandatory');
        }

        $captchaValidated = $this->captchaValidation->validate($captchaToken);

        if ($captchaValidated && Auth::attempt($credentials, $remember)) {
            $token = (string) Str::uuid();

            $this->cacheRepository->forever($token, Auth::id());

            return $this->sendJsonResponse(new Collection([compact('token')]), Response::HTTP_OK);
        }

        throw new NotAcceptableHttpException('password.not_acceptable');
    }
}
