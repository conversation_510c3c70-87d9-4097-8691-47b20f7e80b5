<?php

namespace App\Application\Http\Requests\Admin\Category;

use Illuminate\Foundation\Http\FormRequest;

class CategoryStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => 'required',
            'backoffice_name' => 'required',
            'sub_categories_ids' => 'array',
            'is_generic' => ['required', 'boolean'],
        ];
    }
}
