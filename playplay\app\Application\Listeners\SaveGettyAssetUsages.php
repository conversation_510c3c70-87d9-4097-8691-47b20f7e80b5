<?php

declare(strict_types=1);

namespace App\Application\Listeners;

use App\Application\Events\ProjectDownloaded;
use App\Domain\AssetUsages\AssetUsageRepository;
use App\Models\Company;
use App\Models\Project;

final class SaveGettyAssetUsages
{
    private AssetUsageRepository $assetUsageRepository;
    private Company $company;

    public function __construct(AssetUsageRepository $assetUsageRepository, Company $company)
    {
        $this->assetUsageRepository = $assetUsageRepository;
        $this->company = $company;
    }

    public function handle(ProjectDownloaded $event): void
    {
        $project = $event->getProject();

        if (!$this->isPlayPlayCompany($project)) {
            $this->assetUsageRepository->insertOrIgnoreUsagesForAProject($project);
        }
    }

    private function isPlayPlayCompany(Project $project): bool
    {
        $companyId = $this->company->whereName(Company::PLAYPLAY_COMPANY_NAME)->first()->id ?? null;

        if ($companyId === null) {
            return false;
        }

        return $project->company_id === $companyId;
    }
}
