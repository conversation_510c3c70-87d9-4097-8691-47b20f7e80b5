<?php

namespace App\Application\Providers;

use Illuminate\Auth\Passwords\DatabaseTokenRepository;
use Illuminate\Auth\Passwords\PasswordBroker;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;

class PasswordCreationServiceProvider extends ServiceProvider
{
    public function register()
    {
        $config = $this->app['config']['auth.passwords.users'];
        $this->app->bind('auth.password.broker.new_users', function () use ($config) {
            return new PasswordBroker(
                $this->createTokenRepository($config),
                $this->app['auth']->createUserProvider($config['provider'] ?? null)
            );
        });
    }

    /**
     * Create a token repository instance based on the given configuration.
     *
     * @param array $config
     *
     * @return \Illuminate\Auth\Passwords\TokenRepositoryInterface
     */
    protected function createTokenRepository(array $config)
    {
        $key = $this->app['config']['app.key'];

        if (Str::startsWith($key, 'base64:')) {
            $key = base64_decode(substr($key, 7));
        }

        $connection = $config['connection'] ?? null;

        return new class (
            $this->app['db']->connection($connection),
            $this->app['hash'],
            $config['table'],
            $key,
            -1
        ) extends DatabaseTokenRepository {
            protected function tokenExpired($createdAt)
            {
                return false;
            }
        };
    }
}
