<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Project;

use App\Application\Events\ProjectSettingsUpdated;
use App\Application\Events\ProjectUpdated;
use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Project\ProjectUpdateRequest;
use App\Domain\Project\ProjectService;
use App\Domain\Project\Sanitizer\ProjectScreensSanitizer;
use App\Domain\Project\Services\ProjectSanitizer;
use App\Models\Project;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class UpdateProjectController extends BaseController
{
    use AuthorizesRequests;

    private ProjectService $projectService;
    private ProjectScreensSanitizer $projectScreensSanitizer;
    private ProjectSanitizer $projectSanitizer;
    private Dispatcher $dispatcher;

    public function __construct(
        ProjectService $projectService,
        ProjectScreensSanitizer $projectScreenSanitizer,
        ProjectSanitizer $projectSanitizerService,
        Dispatcher $dispatcher,
    ) {
        $this->projectService = $projectService;
        $this->projectScreensSanitizer = $projectScreenSanitizer;
        $this->projectSanitizer = $projectSanitizerService;
        $this->dispatcher = $dispatcher;
    }

    public function __invoke(Project $project, ProjectUpdateRequest $request): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $project->company);

        // Check that user can access this template
        $processedMediaIds = array_filter($request->input('logos.*.processed_media_id', []));

        $this->authorize('update', [$project, $processedMediaIds]);

        $shouldUpdateScreens = false;
        $updatedSetting = [];
        if ($request->has('format') && $project->format != $request->get('format')) {
            $project->format = $request->get('format');
            $this->projectScreensSanitizer->sanitizeUnavailableScreensInProjectFormat($project);
            $project = $this->projectService->changeFormat($project);
            $shouldUpdateScreens = true;
        }

        if ($request->has('settings')) {
            // Checking updated settings (except music and transitions)
            $settingsRequest = $request->except('settings.music');
            $settingsProject = (new Collection($project->settings))
                ->except('music');
            $newSettings = $settingsProject->diffAssoc($settingsRequest['settings']);
            if ($newSettings->isNotEmpty()) {
                $shouldUpdateScreens = true;
                $updatedSetting['settings'] = $newSettings->keys()->toArray();
            }

            $project->settings = $request->get('settings', []);
        }

        if ($request->has('colors')) {
            $colorsRequest = $request->get('colors', []);
            $newColors = (new Collection($project->colors))->diffAssoc($colorsRequest);
            if ($newColors->isNotEmpty()) {
                $shouldUpdateScreens = true;
                $updatedSetting['colors'] = $newColors->keys()->toArray();
            }

            $project->colors = $colorsRequest;
        }

        $hasOnlyTitleInRequest = count($request->all()) === 1 && $request->has('title');
        if ($request->has('title')) {
            $project->timestamps = !$hasOnlyTitleInRequest;
            $project->title = $request->get('title');
        }

        if ($request->has('logos')) {
            // We check if show_logos has changed
            if ($project->show_logos !== $request->input('logos.show_logos')) {
                $updatedSetting['settings'][] = 'show_logos';
            }

            $project->logos = $request->get('logos');
            $shouldUpdateScreens = true;
        }

        if ($request->has('outro')) {
            $project->outro_id = $request->get('outro');
        }

        if ($request->has('preset_id')) {
            $project->setPresetIdAttribute($request->input('preset_id'));
            $this->projectService->changePreset($project);
            $project->save();
            $this->projectScreensSanitizer->sanitize($project);
            $shouldUpdateScreens = true;
        }

        if ($request->has('font_id')) {
            $project->font_id = $request->get('font_id');
            $this->projectSanitizer->sanitizeFont($project, false);
            $shouldUpdateScreens = true;
        }

        if ($request->has('fade_durations')) {
            $project->fade_in_duration = $request->input('fade_durations.in');
            $project->fade_between_duration = $request->input('fade_durations.between');
            $project->fade_out_duration = $request->input('fade_durations.out');

            $shouldUpdateScreens = true;
        }

        $project->save();

        if ($shouldUpdateScreens) {
            $this->dispatcher->dispatch(new ProjectSettingsUpdated($project, $updatedSetting));
        }

        $this->dispatcher->dispatch(new ProjectUpdated($project, !$hasOnlyTitleInRequest));

        return $this->sendJsonResponse(new Collection([$project->fresh()]), Response::HTTP_OK);
    }
}
