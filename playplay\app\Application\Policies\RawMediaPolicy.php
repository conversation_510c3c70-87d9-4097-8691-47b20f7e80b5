<?php

namespace App\Application\Policies;

use App\Models\Project;
use App\Models\RawMedia;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class RawMediaPolicy
{
    use HandlesAuthorization;

    public function before($user, $ability): ?bool
    {
        return $user->is_admin ?: null;
    }

    /*
     * $rawMedia param must be kept even if it's not used here
     * It is used to identify against which eloquent model the Policy applies
     */
    public function destroy(User $user, RawMedia $rawMedia, Project $project): bool
    {
        return $user->can('view', $project);
    }

    public function index(User $user, array $ids): bool
    {
        return $user->rawMedias()->whereIn('raw_medias.id', $ids)->count() === count($ids);
    }

    public function view(User $user, RawMedia $rawMedia): bool
    {
        return $user->rawMedias()->where('raw_medias.id', $rawMedia->id)->exists();
    }
}
