<?php

namespace App\Application\Rules;

use App\Models\Project;
use App\Models\ProjectScreen;
use Illuminate\Contracts\Validation\Rule;

class CheckOrderIsCorrectRule implements Rule
{
    public function passes($attribute, $value)
    {
        /** @var Project $project */
        $project = request()->route('project');
        /** @var ProjectScreen $projectScreen */
        $projectScreen = request()->route('projectScreen');
        if ($value > $project->projectScreens->count()) {
            return false;
        }

        if ($projectScreen->order > $value && $projectScreen->order - $value != 1) {
            return false;
        }

        if ($projectScreen->order < $value && $value - $projectScreen->order != 1) {
            return false;
        }

        return true;
    }

    public function message()
    {
        return 'CheckOrderIsCorrectRule is not valid';
    }
}
