<?php
declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Stock\TransformerInterface;
use App\Services\Stock\Getty\GettyImageStock;
use App\Services\Stock\Getty\GettyVideoStock;
use App\Services\Stock\Giphy\GiphyStock;
use App\Services\Stock\Storyblocks\StoryblocksVideoStock;
use App\Services\Stock\Unsplash\UnsplashImageStock;
use Illuminate\Support\ServiceProvider;

final class StockServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->when(GettyImageStock::class)
            ->needs(TransformerInterface::class)
            ->give(\App\Infrastructure\Stock\Getty\Image\Transformer::class);

        $this->app->when(GettyVideoStock::class)
            ->needs(TransformerInterface::class)
            ->give(\App\Infrastructure\Stock\Getty\Video\Transformer::class);

        $this->app->when(GiphyStock::class)
            ->needs(TransformerInterface::class)
            ->give(\App\Infrastructure\Stock\Giphy\Transformer::class);

        $this->app->when(StoryblocksVideoStock::class)
            ->needs(TransformerInterface::class)
            ->give(\App\Infrastructure\Stock\Storyblocks\Transformer::class);

        $this->app->when(UnsplashImageStock::class)
            ->needs(TransformerInterface::class)
            ->give(\App\Infrastructure\Stock\Unsplash\Transformer::class);
    }
}
