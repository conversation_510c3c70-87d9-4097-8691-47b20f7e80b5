<?php

declare(strict_types=1);

namespace App\Application\Console\Commands\GoogleCloudStorage;

use Carbon\Carbon;
use InvalidArgumentException;

final class DatesService
{
    private const DEFAULT_DELETION_INTERVAL_IN_DAYS = 1;
    private const DEFAULT_FILE_RETENTION_IN_MONTHS = 3;

    /**
     * @throw InvalidArgumentException
     * @throw InvalidFormatException
     */
    public function getDateRangeToRemoveGcsUselessFiles(?string $startDateTime, ?string $endDateTime): array
    {
        if ($endDateTime === null && $startDateTime === null) {
            return $this->getDefaultDateRange();
        }

        if ($startDateTime === null) {
            return $this->getDateRangeFromEndDate($endDateTime);
        }

        if ($endDateTime === null) {
            return $this->getDateRangeFromStartDate($startDateTime);
        }

        return $this->getDateRangeFromStartAndEndDates($startDateTime, $endDateTime);
    }

    /**
     * @throw InvalidArgumentException
     * @throw InvalidFormatException
     */
    private function getDateRangeFromEndDate(string $endDateTime): array
    {
        $endDate = Carbon::parse($endDateTime)->startOfDay();
        $this->validateThatEndDateIsLessThanRetentionDate($endDate);

        return [
            Carbon::parse($endDateTime)->subDays(self::DEFAULT_DELETION_INTERVAL_IN_DAYS)->startOfDay(),
            $endDate,
        ];
    }

    /**
     * @throw InvalidArgumentException
     */
    private function getDateRangeFromStartAndEndDates(string $startDateTime, string $endDateTime): array
    {
        $startDate = Carbon::parse($startDateTime);
        $endDate = Carbon::parse($endDateTime);

        $this->validateThatEndDateIsGreaterThanStartDate($startDate, $endDate);
        $this->validateThatEndDateIsLessThanRetentionDate($endDate);

        return [$startDate, $endDate];
    }

    /**
     * @throw InvalidArgumentException
     * @throw InvalidFormatException
     */
    private function getDateRangeFromStartDate(string $startDateTime): array
    {
        $endDate = Carbon::parse($startDateTime)->addDays(self::DEFAULT_DELETION_INTERVAL_IN_DAYS)->startOfDay();
        $this->validateThatEndDateIsLessThanRetentionDate($endDate);

        return [
            Carbon::parse($startDateTime)->startOfDay(),
            $endDate,
        ];
    }

    private function getDefaultDateRange(): array
    {
        return [
            $this->retentionDate()->subDays(self::DEFAULT_DELETION_INTERVAL_IN_DAYS),
            $this->retentionDate(),
        ];
    }

    private function retentionDate(): Carbon
    {
        return Carbon::now()->subMonths(self::DEFAULT_FILE_RETENTION_IN_MONTHS)->startOfDay();
    }

    /**
     * @throw InvalidArgumentException
     */
    private function validateThatEndDateIsGreaterThanStartDate(Carbon $startDate, Carbon $endDate): void
    {
        if ($startDate->gt($endDate)) {
            throw new InvalidArgumentException("Start date ({$startDate}) is greater than end date ({$endDate}).");
        }
    }

    /**
     * @throw InvalidArgumentException
     */
    private function validateThatEndDateIsLessThanRetentionDate(Carbon $endDate): void
    {
        $retentionDate = $this->retentionDate();

        if ($endDate->gte($retentionDate)) {
            throw new InvalidArgumentException(
                "End date ({$endDate}) should be before retention date ({$retentionDate})"
            );
        }
    }
}
