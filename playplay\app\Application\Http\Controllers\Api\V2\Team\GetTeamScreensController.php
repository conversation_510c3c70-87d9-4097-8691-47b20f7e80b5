<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Team;

use App\Application\Http\Controllers\Api\BaseController;
use App\Domain\TeamScreen\TeamScreenService;
use App\Models\Team;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\Cache\Repository as CacheRepository;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

class GetTeamScreensController extends BaseController
{
    use AuthorizesRequests;

    private CacheRepository $cacheRepository;
    private TeamScreenService $teamScreenService;

    public function __construct(CacheRepository $cacheRepository, TeamScreenService $teamScreenService)
    {
        $this->cacheRepository = $cacheRepository;
        $this->teamScreenService = $teamScreenService;
    }

    /**
     * @throws AuthorizationException
     */
    public function __invoke(Request $request, Team $team): JsonResponse
    {
        $this->authorize('view', $team);

        $taggedCache = $this->cacheRepository->tags(['api', 'screens']);
        $cacheKey = $request->url();
        if ($taggedCache->has($cacheKey)) {
            return new JsonResponse($taggedCache->get($cacheKey), Response::HTTP_OK);
        }

        $response = $this->buildJsonResponse(new Collection([$this->teamScreenService->getScreensByTeam($team)]));
        $taggedCache->put($cacheKey, $response, self::CACHE_TTL);

        return new JsonResponse($response, Response::HTTP_OK);
    }
}
