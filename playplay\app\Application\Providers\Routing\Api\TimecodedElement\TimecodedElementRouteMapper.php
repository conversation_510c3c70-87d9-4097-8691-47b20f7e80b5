<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\TimecodedElement;

use App\Application\Http\Controllers\Api\V2\TimecodedElement\DeleteAllTimecodedElementsByProjectScreenController;
use App\Application\Http\Controllers\Api\V2\TimecodedElement\DuplicateTimecodedElementController;
use App\Application\Http\Controllers\Api\V2\TimecodedElement\TimecodedElementController;
use App\Application\Http\Controllers\Api\V2\TimecodedElement\TimecodedElementSwitchPresetController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class TimecodedElementRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::prefix('/project-screens/{projectScreen}/timecoded-elements')
            ->as('project-screens.timecoded-elements.')
            ->group(static function (Router $router) {
                $router->get('', [TimecodedElementController::class, 'index'])->name('index');
                $router->post('', [TimecodedElementController::class, 'store'])->name('store');
                $router->put(
                    '/{timecodedElement}',
                    [TimecodedElementController::class, 'update']
                )
                    ->name('update');
                $router->delete(
                    '{timecodedElement}',
                    [TimecodedElementController::class, 'destroy']
                )
                    ->name('destroy');
                $router->put(
                    '/{timecodedElement}/switch-timecoded-element-preset',
                    TimecodedElementSwitchPresetController::class
                )->name('switch-preset');
                $router->post(
                    '/delete-all',
                    DeleteAllTimecodedElementsByProjectScreenController::class
                )->name('delete-all');
                $router->post('/{timecodedElement}/duplicate', DuplicateTimecodedElementController::class)->name(
                    'duplicate'
                );
            });
    }
}
