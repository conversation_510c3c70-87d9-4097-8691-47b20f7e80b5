<?php

namespace App\Application\Http\Requests\Api\Project;

use App\Domain\RawMedia\RawMediaType;
use App\Domain\Screen\ScreenService;
use App\Models\Media;
use App\Models\ProcessedMedia;
use App\Models\Project;
use App\Models\TeamPreset;
use Illuminate\Config\Repository;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Collection;
use Illuminate\Validation\Factory;

class ProjectUpdateRequest extends FormRequest
{
    private ScreenService $screenService;
    private Repository $configRepository;

    public function __construct(ScreenService $screenService, Repository $configRepository)
    {
        $validationFactory = app(Factory::class);
        $this->screenService = $screenService;
        $this->configRepository = $configRepository;

        $this->checkMediaIsAudio($validationFactory);
        $this->checkMediaIsImage($validationFactory);
        $this->checkFormatIsAvailable($validationFactory);
        $this->checkOutro($validationFactory);
        $this->checkLogo($validationFactory);
        $this->checkIfUserHasPreset($validationFactory);
        $this->checkIfFontIsAvailableForUser($validationFactory);
        $this->checkPosition($validationFactory);
        $this->checkMediaAudioIsCorrect($validationFactory);
        $this->checkScreenDurationIsValid($validationFactory);

        parent::__construct();
        $this->configRepository = $configRepository;
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $settings = $this->configRepository->get('project-settings.settings');
        $colors = $this->configRepository->get('team-preset-colors.colors');

        $rules = [];
        foreach ($settings as $key => $setting) {
            if ($key === 'music') {
                $rules['settings.music'] = [
                    'sometimes',
                    'nullable',
                    'exists:medias,id',
                    'mediaIsAudio',
                    'mediaAudioIsCorrect',
                ];
            } elseif ($key === 'screen_duration') {
                $rules['settings.' . $key] = [
                    'sometimes',
                    'required',
                    'screenDurationIsValid',
                ];
            } else {
                $rules['settings.' . $key] = [
                    'sometimes',
                    'required',
                    'integer',
                    'between:0,100',
                ];
            }
        }

        foreach ($colors as $key => $setting) {
            $rules['colors.' . $key] = ['rgbColor'];
        }

        $rules['format'] = ['sometimes', 'required', 'formatAvailableInProject'];
        $rules['title'] = ['sometimes', 'required', 'string', 'min:1', 'max:255'];
        $rules['outro'] = ['sometimes', 'outroExists'];
        $rules['logos.*.processed_media_id'] = ['sometimes', 'logoIsValid'];
        $rules['logos.*.position'] = ['sometimes', 'positionIsValid'];
        $rules['preset_id'] = ['sometimes', 'required', 'presetAvailableForUser'];
        $rules['font_id'] = ['sometimes', 'nullable', 'fontAvailableForUser'];
        $rules['fade_durations'] = ['array:in,out,between'];
        $rules['fade_durations.in'] = ['required_with:fade_durations',];
        $rules['fade_durations.between'] = ['required_with:fade_durations',];
        $rules['fade_durations.out'] = ['required_with:fade_durations',];
        $rules['fade_durations.*'] = ['integer', 'min:0'];

        return $rules;
    }

    private function checkFormatIsAvailable(Factory $validationFactory): void
    {
        $validationFactory->extendImplicit(
            'formatAvailableInProject',
            function ($attribute, $format) {
                /** @var Project $project */
                $project = request()?->route('project');

                return in_array($format, $this->getAvailableFormats($project));
            }
        );
    }

    private function checkIfFontIsAvailableForUser(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'fontAvailableForUser',
            function ($attribute, $value) {
                if ($value === null) {
                    return true;
                }

                /** @var Project $project */
                $project = request()?->route('project');

                return $project->preset->hasFont($value);
            }
        );
    }

    private function checkIfUserHasPreset(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'presetAvailableForUser',
            function ($attribute, $value) {
                return is_null($value) ?: auth()->user()->canSeePreset($value);
            }
        );
    }

    /**
     * Logo is valid if :
     *      - display is true (for secondary logos only)
     *      - media id exists in medias
     *      - if can upload is false :
     *          - media_id is in team preset logos.
     *
     * @param Factory $validationFactory
     */
    private function checkLogo(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'logoIsValid',
            function ($attribute, $value, $parameters) {
                if ($value === null) {
                    return true;
                }

                $processedMedia = ProcessedMedia::find($value);
                if (!$processedMedia) {
                    return false;
                }

                if ($processedMedia->type !== RawMediaType::IMAGE) {
                    return false;
                }

                $logoType = explode('.', $attribute)[1];
                /** @var Project $project */
                $project = request()?->route('project');
                // if secondary logo is hidden in BO, secondary logo should not be set
                if ($logoType === 'secondary' && !$project->preset->logos['secondary']['display']) {
                    return false;
                }

                // if user can upload a new logo there is no validation
                if ($project->preset->logos[$logoType]['canUpload']) {
                    return true;
                }

                // if user cannot upload a new logo, media_id must be in team preset logos
                $projectLogos = [];
                if (isset($project->preset->logos_vue[$logoType][$project->format])) {
                    $projectLogos = (new Collection($project->preset->logos_vue[$logoType][$project->format]))->pluck(
                        'id'
                    )->toArray();
                }

                return in_array($value, $projectLogos);
            }
        );
    }

    private function checkMediaAudioIsCorrect(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'mediaAudioIsCorrect',
            function ($attribute, $value, $parameters) {
                if (is_null($value)) {
                    return true;
                }

                /** @var Project $project */
                $project = request()?->route('project');
                $preset = $project->preset;
                $media = Media::query()->find($value);

                if ($media === null && $value !== null) {
                    return false;
                }

                if (is_null($media->parent_id)
                    && isset($preset->musics['canUpload'])
                    && !$preset->musics['canUpload']
                ) {
                    return false;
                }

                if (!is_null($media->parent_id)) {
                    return false;
                }

                if (!is_null($media->parent_id)
                    && (!$preset->musicListsMusics->contains($media->parent_id))
                ) {
                    return false;
                }

                return true;
            }
        );
    }

    private function checkMediaIsAudio(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'mediaIsAudio',
            function ($attribute, $value, $parameters) {
                if (is_null($value)) {
                    return true;
                }

                $media = Media::find($value);
                if (is_null($media) || !$media->isAudio()) {
                    return false;
                }

                return true;
            }
        );
    }

    private function checkMediaIsImage(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'mediaIsImage',
            function ($attribute, $value, $parameters) {
                $media = Media::find($value);
                if (is_null($media) || !$media->isImage()) {
                    return false;
                }

                return true;
            }
        );
    }

    private function checkOutro(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'outroExists',
            function ($attribute, $value, $parameters) {
                if ($value === null) {
                    return true;
                }

                $processedMedia = ProcessedMedia::find($value);
                if ($processedMedia === null) {
                    return false;
                }

                // Check that outro sent exists in preset for project format
                /** @var Project $project */
                $project = request()?->route('project');
                foreach ($project->preset->outros['values'] ?? [] as $optionnalOutros) {
                    if (isset($optionnalOutros['processed_medias'][$project->format])
                        && $optionnalOutros['processed_medias'][$project->format] === $value
                    ) {
                        return true;
                    }
                }

                return false;
            }
        );
    }

    /**
     * Position is valid if :
     *    - position is available for users
     *    - position exists in default available positions.
     *
     * @param Factory $validationFactory
     */
    private function checkPosition(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'positionIsValid',
            function ($attribute, $value, $parameters) {
                $positionType = explode('.', $attribute)[1];

                /** @var Project $project */
                $project = request()?->route('project');

                if (!$project->preset->logos[$positionType]['position']['display']) {
                    return false;
                }

                return array_key_exists($value, TeamPreset::LOGOS_POSITIONS);
            }
        );
    }

    public function checkScreenDurationIsValid(Factory $validationFactory): void
    {
        $settings = $this->configRepository->get('project-settings.settings');

        $validationFactory->extendImplicit(
            'screenDurationIsValid',
            function ($attribute, $format) use ($settings) {
                if ($attribute === 'settings.screen_duration') {
                    return in_array($format, $settings['screen_duration']['options']);
                }

                return true;
            }
        );
    }

    /** @TODO Move to ProjectService */
    private function getAvailableFormats(Project $project): array
    {
        return $this->screenService->getAvailableFormatsByProject($project);
    }
}
