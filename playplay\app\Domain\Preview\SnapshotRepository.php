<?php

declare(strict_types=1);

namespace App\Domain\Preview;

use App\Models\Project;
use App\Models\ProjectScreen;
use App\Models\Snapshot;

interface SnapshotRepository
{
    /**
     * @param int[] $renderMediasIds
     */
    public function attachRenderMedias(Snapshot $snapshot, array $renderMediasIds): void;

    public function createSnapshotForProject(Project $project, array $serializedData): Snapshot;

    public function createSnapshotForProjectScreen(
        ProjectScreen $projectScreen,
        array $serializedData
    ): Snapshot;

    public function hasAllRenderMediasProcessed(Snapshot $snapshot): bool;

    public function updateStatus(Snapshot $snapshot, string $status): void;

    public function updateStatusAndData(Snapshot $snapshot, SnapshotStatus $status, array $data): void;

    public function updateStatusFromRenderMediasStatuses(Snapshot $snapshot): Snapshot;
}
