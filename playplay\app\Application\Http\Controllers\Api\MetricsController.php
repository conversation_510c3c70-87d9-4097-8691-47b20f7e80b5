<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api;

use App\Domain\Metrics\MetricsReader;
use App\Domain\Metrics\PublishMetricsService;
use Illuminate\Routing\Controller;

final class MetricsController extends Controller
{
    private PublishMetricsService $metricsPublisher;
    private MetricsReader $metricsReader;

    public function __construct(PublishMetricsService $metricsPublisher, MetricsReader $read)
    {
        $this->metricsPublisher = $metricsPublisher;
        $this->metricsReader = $read;
    }

    public function get(): string
    {
        $this->metricsPublisher->publish();

        return $this->metricsReader->readMetrics();
    }
}
