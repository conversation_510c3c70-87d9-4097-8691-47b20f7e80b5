<?php

namespace App\Application\Providers;

use App\Domain\ProjectScreen\Layout\ProjectScreenLayoutRepository;
use App\Infrastructure\ProjectScreen\Repositories\EloquentProjectScreenLayoutRepository;
use Illuminate\Support\ServiceProvider;

final class ProjectScreenLayoutServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->bind(
            ProjectScreenLayoutRepository::class,
            EloquentProjectScreenLayoutRepository::class
        );
    }
}
