<?php

declare(strict_types=1);

namespace App\Application\Http\Mappers\CutawayShot;

use App\Application\Exceptions\InvalidFormRequestMappingException;
use App\Application\Http\Requests\Api\Project\ProjectScreen\Param\CutawayShot\UpdateCutawayShotTimelineRequest;
use App\Domain\CutawayShot\CutawayShotImageRenderMediaData;
use App\Domain\CutawayShot\CutawayShotVideoRenderMediaData;
use App\Domain\RawMedia\RawMediaType;
use App\Domain\Render\RenderMedia\Crop\CropFactory;
use App\Domain\Render\RenderMedia\TargetDimension;
use App\Domain\Render\RenderMedia\Trim;
use App\Models\ProjectScreen;
use App\Models\ScreenParams\ParamCutawayShot;
use Exception;
use Illuminate\Routing\Route;
use InvalidArgumentException;
use TypeError;

final class CutawayShotTimelineRenderMediaDataMapper
{
    /**
     * @return array<int, CutawayShotImageRenderMediaData | CutawayShotVideoRenderMediaData>
     */
    public function fromRequest(UpdateCutawayShotTimelineRequest $request): array
    {
        $targetDimension = $this->getTargetDimension($request);
        $cutawayShotRenderMediaDataList = [];
        try {
            foreach ($request->input('items') as $item) {
                /** @var RawMediaType::IMAGE|RawMediaType::VIDEO $itemType */
                $itemType = $item['type'];
                $cutawayShotRenderMediaDataList[$item['processed_media_id']] = match ($itemType) {
                    RawMediaType::IMAGE => $this->getCutawayShotImageRenderMediaData($item, $targetDimension),
                    RawMediaType::VIDEO => $this->getCutawayShotVideoRenderMediaData($item, $targetDimension),
                };
            }

            return $cutawayShotRenderMediaDataList;
        } catch (TypeError | Exception) {
            throw new InvalidFormRequestMappingException($request, self::class);
        }
    }

    private function getCutawayShotImageRenderMediaData(
        array $item,
        TargetDimension $targetDimension,
    ): CutawayShotImageRenderMediaData {
        return new CutawayShotImageRenderMediaData(
            CropFactory::createCropFromArray($item['crop']),
            $item['duration'],
            $item['keep_size'],
            $item['start'],
            $targetDimension
        );
    }

    private function getCutawayShotVideoRenderMediaData(
        array $item,
        TargetDimension $targetDimension
    ): CutawayShotVideoRenderMediaData {
        return new CutawayShotVideoRenderMediaData(
            CropFactory::createCropFromArray($item['crop']),
            $item['keep_size'],
            $item['start'],
            $targetDimension,
            new Trim(
                $item['trim_start'],
                $item['trim_end'],
            )
        );
    }

    private function getTargetDimension(UpdateCutawayShotTimelineRequest $request): TargetDimension
    {
        /** @var Route $route */
        $route = $request->route();
        /** @var ?ParamCutawayShot $param */
        $param = $route->parameter('param');
        if ($param === null) {
            throw new InvalidArgumentException('Route parameter "param" is missing');
        }

        /** @var ?ProjectScreen $projectScreen */
        $projectScreen = $route->parameter('projectScreen');
        if ($projectScreen === null) {
            throw new InvalidArgumentException('Route parameter "projectScreen" is missing');
        }

        $format = $projectScreen->project->normalized_format;

        return new TargetDimension(
            $param->maxHeight($format),
            $param->maxWidth($format),
        );
    }
}
