<?php

namespace App\Domain\CutawayShot\Validators;

use App\Domain\Time\Duration;

final class CutawayShotTimelineRuleValidator
{
    public function isValid(array $items): bool
    {
        usort($items, static function (array $item1, array $item2) {
            return $item1['start'] <=> $item2['start'];
        });

        for ($index = 0; $index < count($items) - 1; $index++) {
            $itemDuration = $this->getDuration($items[$index]);

            if ($itemDuration === null) {
                return false;
            }

            $itemEndInMs = (new Duration($items[$index]['start'] + $itemDuration))->toMilliseconds()->getValue();
            $nextItemStartInMs = (new Duration($items[$index + 1]['start']))->toMilliseconds()->getValue();
            if ($itemEndInMs > $nextItemStartInMs) {
                return false;
            }
        }

        return true;
    }

    private function getDuration(array $data): ?float
    {
        if (array_key_exists('duration', $data)) {
            return $data['duration'];
        }

        if (array_key_exists('trim_start', $data) && array_key_exists('trim_end', $data)) {
            return $data['trim_end'] - $data['trim_start'];
        }

        return null;
    }
}
