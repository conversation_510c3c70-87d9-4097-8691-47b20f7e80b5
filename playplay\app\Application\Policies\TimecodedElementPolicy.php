<?php

declare(strict_types=1);

namespace App\Application\Policies;

use App\Domain\TimecodedElement\Repositories\TeamTimecodedElementPresetRepository;
use App\Models\ProjectScreen;
use App\Models\TimecodedElement\TimecodedElement;
use App\Models\User;

final class TimecodedElementPolicy extends DefaultPolicy
{
    private TeamTimecodedElementPresetRepository $teamTimecodedElementPresetRepository;

    public function __construct(TeamTimecodedElementPresetRepository $teamTimecodedElementPresetRepository)
    {
        $this->teamTimecodedElementPresetRepository = $teamTimecodedElementPresetRepository;
    }

    public function before(User $user, string $ability): ?bool
    {
        $companyHasTimecodedElementsFeature = $user->company?->hasTimecodedElementsFeature() ?? false;

        if (!$companyHasTimecodedElementsFeature) {
            return false;
        }

        return null;
    }

    public function view(User $user, ProjectScreen $projectScreen): bool
    {
        return $user->can('view', $projectScreen);
    }

    public function create(User $user, ProjectScreen $projectScreen, int $timecodedElementPresetId): bool
    {
        return $user->can('view', $projectScreen)
            && $projectScreen->screen->has_timecoded_elements
            && $this->teamTimecodedElementPresetRepository->exists(
                $timecodedElementPresetId,
                $projectScreen->project->team_id,
            );
    }

    public function update(User $user, TimecodedElement $timecodedElement, ProjectScreen $projectScreen): bool
    {
        return $timecodedElement->project_screen_id === $projectScreen->id
            && $user->can('view', $projectScreen)
            && $projectScreen->screen->has_timecoded_elements;
    }

    public function switchPreset(
        User $user,
        TimecodedElement $timecodedElement,
        ProjectScreen $projectScreen,
        int $newTimecodedElementPresetId,
    ): bool {
        return $this->update($user, $timecodedElement, $projectScreen)
            && $this->teamTimecodedElementPresetRepository->exists(
                $newTimecodedElementPresetId,
                $projectScreen->project->team_id,
            );
    }

    public function destroy(User $authUser, TimecodedElement $timecodedElement, ProjectScreen $projectScreen): bool
    {
        if ($timecodedElement->project_screen_id !== $projectScreen->id) {
            return false;
        }

        return $authUser->can('view', $projectScreen);
    }

    public function deleteAll(User $user, ProjectScreen $projectScreen): bool
    {
        return $user->can('view', $projectScreen) && $projectScreen->screen->has_timecoded_elements;
    }

    public function duplicate(User $user, TimecodedElement $timecodedElement, ProjectScreen $projectScreen): bool
    {
        return $this->update($user, $timecodedElement, $projectScreen);
    }
}
