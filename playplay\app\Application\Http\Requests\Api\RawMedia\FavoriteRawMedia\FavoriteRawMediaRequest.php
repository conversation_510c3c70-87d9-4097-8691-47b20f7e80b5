<?php

namespace App\Application\Http\Requests\Api\RawMedia\FavoriteRawMedia;

use Illuminate\Foundation\Http\FormRequest;

class FavoriteRawMediaRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function getRules()
    {
        return [
            'page' => ['sometimes', 'numeric', 'min:1'],
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return $this->getRules();
    }
}
