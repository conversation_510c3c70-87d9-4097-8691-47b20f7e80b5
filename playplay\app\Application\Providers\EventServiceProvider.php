<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Application\Events\AdminCreatedUser;
use App\Application\Events\CompanyFeatureUpdated;
use App\Application\Events\NewGenericScreenCreated;
use App\Application\Events\NewGenericTemplateCategoryIsCreated;
use App\Application\Events\ProjectDownloaded;
use App\Application\Events\ProjectSilentlyUpdated;
use App\Application\Events\ProjectUpdated;
use App\Application\Events\RenderInError;
use App\Application\Events\Renders\RenderMediaCanceled;
use App\Application\Events\Renders\RenderMediaProcessed;
use App\Application\Events\Renders\RenderProjectUpdated;
use App\Application\Events\ShareableLinkCommentCreated;
use App\Application\Events\SignedUpUser;
use App\Application\Events\SsoEnabledForCompany;
use App\Application\Events\SsoEnabledForUser;
use App\Application\Events\SubscriptionCreated;
use App\Application\Events\TeamCreated;
use App\Application\Events\UserGenerateVideoOrRender;
use App\Application\Listeners\AddNewGenericTemplateCategoryToTeams;
use App\Application\Listeners\AlertRenderInError;
use App\Application\Listeners\CreateOrUpdateHubspotContact;
use App\Application\Listeners\EnableGenericScreenForV3ScaleTeams;
use App\Application\Listeners\Renders\UpdateProjectThumbnailWithRenderMedia;
use App\Application\Listeners\Renders\UpdateProjectWithRenderUrls;
use App\Application\Listeners\Renders\UpdateShareableLinkWithLastRender;
use App\Application\Listeners\Renders\UpdateSnapshotOnRenderMediaCanceled;
use App\Application\Listeners\Renders\UpdateSnapshotOnRenderMediaProcessed;
use App\Application\Listeners\ReportExceptionToSentry;
use App\Application\Listeners\SaveGettyAssetUsages;
use App\Application\Listeners\SendFirstRenderEmail;
use App\Application\Listeners\SendOnboardingUserNotification;
use App\Application\Listeners\SendShareableLinkCommentNotification;
use App\Application\Listeners\Sso\SendSsoEnabledNotificationToUser;
use App\Application\Listeners\Sso\SendSsoEnabledNotificationToUsersOfTheComapny;
use App\Application\Listeners\Subscription\DisableMultipleFonts;
use App\Application\Listeners\Subscription\DisableMusicUpload;
use App\Application\Listeners\Subscription\UpdateCompanyFeatures;
use App\Application\Listeners\TimecodedElement\CompanyFeatureUpdated\AttachGenericFamilies;
use App\Application\Listeners\TimecodedElement\TeamCreated\AttachGenericFamiliesToTeam;
use App\Application\Listeners\UpdateProject;
use App\Application\Listeners\UpdateProjectScreenRender;
use App\Application\Listeners\UpdateProjectSnapshotAndRender;
use App\Application\Listeners\UserAction\SendUserActionLogOnLogin;
use App\Application\Listeners\UserAction\SendUserActionLogOnProjectDownload;
use App\Application\Listeners\UserAction\SendUserActionLogOnProjectRender;
use App\Application\Listeners\UserAction\SendUserActionLogOnSignUp;
use App\Application\Listeners\UserLoggedAt;
use App\Domain\Config\MissingConfigurationException;
use App\Domain\Localization\UnableToGetCountryCodeException;
use App\Domain\Logs\ModelHistory\UnableToHistorizeModelException;
use App\Domain\Logs\ModelHistory\UnableToLogModelHistoryException;
use App\Domain\User\Action\CriticalUserAction\UnableToTriggerAlertException;
use App\Domain\User\Action\UnableToLogUserActionException;
use Illuminate\Auth\Events\Login;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        UserGenerateVideoOrRender::class => [
            SendFirstRenderEmail::class,
            SendUserActionLogOnProjectRender::class,
        ],
        RenderInError::class => [
            AlertRenderInError::class,
        ],
        Login::class => [
            UserLoggedAt::class,
            SendUserActionLogOnLogin::class,
        ],
        ProjectUpdated::class => [
            UpdateProject::class,
        ],
        SignedUpUser::class => [
            CreateOrUpdateHubspotContact::class,
            SendUserActionLogOnSignUp::class,
        ],
        RenderMediaProcessed::class => [
            UpdateSnapshotOnRenderMediaProcessed::class,
            UpdateProjectThumbnailWithRenderMedia::class,
        ],
        RenderMediaCanceled::class => [
            UpdateSnapshotOnRenderMediaCanceled::class,
        ],
        ShareableLinkCommentCreated::class => [
            SendShareableLinkCommentNotification::class,
        ],
        RenderProjectUpdated::class => [
            UpdateProjectWithRenderUrls::class,
            UpdateShareableLinkWithLastRender::class,
        ],
        ProjectDownloaded::class => [
            SaveGettyAssetUsages::class,
            SendUserActionLogOnProjectDownload::class,
        ],
        AdminCreatedUser::class => [
            SendOnboardingUserNotification::class,
        ],
        SsoEnabledForCompany::class => [
            SendSsoEnabledNotificationToUsersOfTheComapny::class,
        ],
        SsoEnabledForUser::class => [
            SendSsoEnabledNotificationToUser::class,
        ],
        ProjectSilentlyUpdated::class => [
            UpdateProjectSnapshotAndRender::class,
        ],
        SubscriptionCreated::class => [
            DisableMusicUpload::class,
            UpdateCompanyFeatures::class,
            DisableMultipleFonts::class,
        ],
        NewGenericScreenCreated::class => [
            EnableGenericScreenForV3ScaleTeams::class,
        ],
        NewGenericTemplateCategoryIsCreated::class => [
            AddNewGenericTemplateCategoryToTeams::class,
        ],
        MissingConfigurationException::class => [
            ReportExceptionToSentry::class,
        ],
        UnableToGetCountryCodeException::class => [
            ReportExceptionToSentry::class,
        ],
        UnableToLogUserActionException::class => [
            ReportExceptionToSentry::class,
        ],
        UnableToTriggerAlertException::class => [
            ReportExceptionToSentry::class,
        ],
        UnableToLogModelHistoryException::class => [
            ReportExceptionToSentry::class,
        ],
        UnableToHistorizeModelException::class => [
            ReportExceptionToSentry::class,
        ],
        TeamCreated::class => [
            AttachGenericFamiliesToTeam::class,
        ],
        CompanyFeatureUpdated::class => [
            AttachGenericFamilies::class,
        ],
    ];
    protected $subscribe = [
        UpdateProjectScreenRender::class,
    ];
}
