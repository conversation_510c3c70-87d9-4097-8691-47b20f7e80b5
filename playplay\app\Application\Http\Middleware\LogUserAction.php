<?php

declare(strict_types=1);

namespace App\Application\Http\Middleware;

use App\Domain\Request\RequestService;
use App\Domain\User\Action\UserAction;
use App\Domain\User\Action\UserActionContext;
use App\Domain\User\Action\UserActionService;
use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Routing\Route;
use Illuminate\Session\Store;
use Symfony\Component\HttpFoundation\Response;

final class LogUserAction
{
    private UserActionService $userActionService;

    private Store $session;

    private RequestService $requestService;

    public function __construct(
        RequestService $requestService,
        UserActionService $userActionService,
        Store $session
    ) {
        $this->requestService = $requestService;
        $this->userActionService = $userActionService;
        $this->session = $session;
    }

    public function handle(Request $request, Closure $next)
    {
        return $next($request);
    }

    public function terminate(Request $request, Response $response): void
    {
        $adminUser = $this->session->has('orig_user') ? $this->session->get('orig_user') : null;
        $currentUser = $adminUser ?? $request->user();
        /** @var User|null $loggedInAsUser is the target user the adminUser loggedAs feature */
        $loggedInAsUser = $adminUser ? $request->user() : null;

        /** @var Route $route */
        $route = $request->route();

        if ($route !== null && $this->requestService->isBackofficeRoute()) {
            $this->userActionService->addUserAction(
                new UserAction(
                    $route->getName(),
                    // $route->parameters holds all the model that are bound to this route `$route`
                    $route->parameters ?? [],
                    null,
                    null,
                    true
                )
            );
        }

        $this->userActionService->flushUserActions(
            new UserActionContext(
                $this->requestService->getId(),
                $request->method(),
                $request->fullUrl(),
                $response->getStatusCode(),
                $currentUser,
                $loggedInAsUser,
                $request->ip()
            )
        );
    }
}
