<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\ProjectScreen\Param\CutawayShot;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Mappers\CutawayShot\CutawayShotVideoRenderMediaDataMapper;
use App\Application\Http\Requests\Api\Project\ProjectScreen\Param\CutawayShot\StoreCutawayShotVideoRequest;
use App\Application\Policies\CutawayShotPolicy;
use App\Domain\CutawayShot\CutawayShotService;
use App\Domain\RawMedia\RawMediaRepository;
use App\Models\ProjectScreen;
use App\Models\RawMedia;
use App\Models\ScreenParams\ParamCutawayShot;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class CreateCutawayShotVideoController extends BaseController
{
    use AuthorizesRequests;

    private CutawayShotService $cutawayShotService;
    private CutawayShotVideoRenderMediaDataMapper $cutawayShotVideoRenderMediaDataMapper;
    private RawMediaRepository $rawMediaRepository;

    public function __construct(
        CutawayShotService $cutawayShotService,
        CutawayShotVideoRenderMediaDataMapper $cutawayShotVideoRenderMediaDataMapper,
        RawMediaRepository $rawMediaRepository,
    ) {
        $this->cutawayShotService = $cutawayShotService;
        $this->cutawayShotVideoRenderMediaDataMapper = $cutawayShotVideoRenderMediaDataMapper;
        $this->rawMediaRepository = $rawMediaRepository;
    }

    /**
     * @throws AuthorizationException
     */
    public function __invoke(
        ProjectScreen $projectScreen,
        ParamCutawayShot $param,
        StoreCutawayShotVideoRequest $request
    ): JsonResponse {
        $rawMedia = $this->rawMediaRepository->getById($request->get('raw_media_id'));
        $project = $projectScreen->project;
        $source = $request->get('source');
        $this->authorize('create', [CutawayShotPolicy::RESOURCE_NAME, $rawMedia, $project, $source]);
        $cutawayShotVideoRenderMediaData = $this->cutawayShotVideoRenderMediaDataMapper->fromRequest($request);

        $processedMedia = $this->cutawayShotService->create(
            $projectScreen->project_id,
            $rawMedia->id,
            $source,
            $cutawayShotVideoRenderMediaData
        );

        return $this->sendJsonResponse(new Collection([$processedMedia]), Response::HTTP_CREATED);
    }
}
