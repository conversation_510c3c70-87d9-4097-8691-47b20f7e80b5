<?php

namespace App\Application\Http\Requests\Admin\User;

use App\Domain\Localization\SupportedLanguages;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UserUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        /** @var User $user */
        $user = $this->route('user');
        $rules = [
            'first_name' => ['sometimes', 'required_without:last_name', 'max:50'],
            'last_name' => ['sometimes', 'required_without:first_name', 'max:50'],
            'email' => ['required', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'phone' => ['sometimes', 'max:30'],
            'language' => ['string', Rule::in(SupportedLanguages::values())],
            'is_active' => ['sometimes', 'boolean'],
            'user_from' => ['nullable', 'date'],
            'user_until' => ['nullable', 'date'],
        ];

        if ($this->get('user_from')) {
            $rules['user_until'][] = 'after:user_from';
        }

        return $rules;
    }
}
