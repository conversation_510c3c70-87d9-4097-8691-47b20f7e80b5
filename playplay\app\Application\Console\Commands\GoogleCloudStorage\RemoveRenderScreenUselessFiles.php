<?php

declare(strict_types=1);

namespace App\Application\Console\Commands\GoogleCloudStorage;

use App\Domain\ProjectScreen\ProjectScreenRepository;
use App\Models\Renders\RenderScreenHtml;
use DateTimeInterface;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Log\Logger;
use Symfony\Component\Console\Helper\ProgressBar;
use Throwable;

final class RemoveRenderScreenUselessFiles extends Command
{
    private const CHUNK_SIZE = 100;
    protected $signature = 'cleaning:remove-useless-files
                            {--S|start-date= : Date from which the deletion starts. Default : 7 days before end date}
                            {--E|end-date=   : Date from which the deletion stops. Default : 3 months before today}
                            {--dry-run : Simulate the behavior.}
                            ';
    protected $description = 'Remove render screens useless files from Google Cloud Storage.';

    private DatesService $datesService;
    private StorageCleanerInterface $storageCleaner;
    private ProjectScreenRepository $projectScreenRepository;
    private Logger $logger;

    public function __construct(
        DatesService $datesService,
        StorageCleanerInterface $storageCleaner,
        ProjectScreenRepository $projectScreenRepository,
        Logger $logger
    ) {
        parent::__construct();
        $this->datesService = $datesService;
        $this->storageCleaner = $storageCleaner;
        $this->projectScreenRepository = $projectScreenRepository;
        $this->logger = $logger;
    }

    public function handle(): int
    {
        $dryRun = $this->option('dry-run');
        $startDateTime = $this->option('start-date');
        $endDateTime = $this->option('end-date');

        try {
            [$startDate, $endDate] = $this->datesService->getDateRangeToRemoveGcsUselessFiles(
                $startDateTime,
                $endDateTime
            );
        } catch (Exception $exception) {
            $this->logger->error("Given dates are invalid: {$exception->getMessage()}");

            return 1;
        }

        $this->info('--------------------------------------------------------------------------');
        $this->logAndDisplayOnConsole(
            "Removing useless files from ({$startDate->format('d-m-Y')}) to ({$endDate->format('d-m-Y')})"
        );
        $this->info('--------------------------------------------------------------------------');

        $renderScreensToClean = $this->getAllCreatedBetweenDatesQuery($startDate, $endDate);
        if ($dryRun) {
            $this->executeDryRun($renderScreensToClean);
        } else {
            $this->executeDeletion($renderScreensToClean, $this->storageCleaner);
        }

        return 0;
    }

    /**
     * @return string
     */
    public function getStorageFolderPath(RenderScreenHtml $renderScreen): string
    {
        $companyId = $renderScreen->projectScreen()->withTrashed()->first()
            ->project()
            ->withTrashed()
            ->first()
            ->company_id;

        return "companies/{$companyId}/render-screens/{$renderScreen->id}/{$renderScreen->folder}";
    }

    public function getAllCreatedBetweenDatesQuery(
        DateTimeInterface $startDate,
        DateTimeInterface $endDate
    ): EloquentBuilder {
        return RenderScreenHtml::query()->select()
            ->with('projectScreen')
            ->whereBetween('created_at', [$startDate, $endDate]);
    }

    protected function getProgressBarSize(int $nbRenderScreenToDelete): int
    {
        return $nbRenderScreenToDelete > self::CHUNK_SIZE ? intval(
            ceil(
                $nbRenderScreenToDelete / self::CHUNK_SIZE
            )
        ) : 1;
    }

    private function logAndDisplayOnConsole(string $message): void
    {
        $this->info($message);
        $this->logger->info($message);
    }

    private function executeDryRun(EloquentBuilder $renderScreensToClean): void
    {
        $this->info('### You are performing a dry run command. It will not remove anything. ###');

        $nbErroredRenderScreens = 0;
        $renderScreensToClean->chunk(
            self::CHUNK_SIZE,
            function (Collection $renderScreensToClean) use ($nbErroredRenderScreens): void {
                foreach ($renderScreensToClean as $renderScreen) {
                    try {
                        $this->info("Render screen ({$renderScreen->id}) created at ({$renderScreen->created_at}).");
                        $this->info("\tFolder path ({$this->getStorageFolderPath($renderScreen)})");
                    } catch (Throwable $e) {
                        $this->warn("\tPotential issue with Render screen ({$renderScreen->id}): {$e->getMessage()}");
                        $nbErroredRenderScreens++;
                    }
                }
            }
        );
        $this->info("{$renderScreensToClean->count()} render screens will be affected.");
        $this->warn("{$nbErroredRenderScreens} render screens will not be removed.");
    }

    private function executeDeletion(
        EloquentBuilder $renderScreensToClean,
        StorageCleanerInterface $storageCleaner
    ): void {
        $nbRenderScreenToDelete = $renderScreensToClean->count();

        if ($nbRenderScreenToDelete === 0) {
            $this->logAndDisplayOnConsole('No render screens to delete for the selected range.');

            return;
        }

        $globalProgressBar = $this->initializeAndCustomizeProgressBar(
            $nbRenderScreenToDelete
        );
        $startTime = microtime(true);
        $renderScreensToClean->chunk(
            self::CHUNK_SIZE,
            function (Collection $chunkOfRenderScreensToClean) use ($globalProgressBar, $storageCleaner): void {
                $this->deleteUselessFilesFor($chunkOfRenderScreensToClean->all(), $storageCleaner);
                $globalProgressBar->advance();
            }
        );

        $globalProgressBar->finish();
        $duration = round(microtime(true) - $startTime, 3);
        $this->logAndDisplayOnConsole("{$nbRenderScreenToDelete} render screens processed in : {$duration} secs.");
    }

    private function initializeAndCustomizeProgressBar(int $nbRenderScreenToDelete): ProgressBar
    {

        $count = $this->getProgressBarSize($nbRenderScreenToDelete);
        $progressBar = $this->output->createProgressBar($count);
        $progressBar->setFormat('[<fg=green>%bar%</>] <info>%percent:3s%% %elapsed:6s% / %estimated:-6s%</info>');

        return $progressBar;
    }

    /**
     * @param RenderScreenHtml[]      $renderScreensToClean
     * @param StorageCleanerInterface $storageCleaner
     *
     * @return void
     */
    private function deleteUselessFilesFor(
        array $renderScreensToClean,
        StorageCleanerInterface $storageCleaner
    ): void {

        foreach ($renderScreensToClean as $renderScreen) {
            try {
                $this->logger->info(
                    "Render screen {$renderScreen->id}) created at ({$renderScreen->created_at->format('Y-m-d')})"
                );
                $folderPath = $this->getStorageFolderPath($renderScreen);
                $this->logger->info("Removing useless files in the folder ({$folderPath})");
                $storageCleaner->deleteFilesIn($folderPath);
                $renderScreen->delete();
                if (!$renderScreen->projectScreen->trashed()) {
                    $this->projectScreenRepository->markRenderScreenNotUpToDate($renderScreen->projectScreen->id);
                }
            } catch (Throwable $e) {
                $this->logger->warning(
                    "Cannot delete useless files for render screen ({$renderScreen->id}): {$e->getMessage()}"
                );
            }
        }
    }
}
