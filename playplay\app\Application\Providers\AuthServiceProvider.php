<?php

namespace App\Application\Providers;

use App\Application\Policies\BillingPlanPolicy;
use App\Application\Policies\CompanyPolicy;
use App\Application\Policies\ConfigPolicy;
use App\Application\Policies\CustomPagePolicy;
use App\Application\Policies\CutawayShotPolicy;
use App\Application\Policies\DeprecatedRawMediaPolicy;
use App\Application\Policies\FavoriteRawMediaPolicy;
use App\Application\Policies\FolderPolicy;
use App\Application\Policies\HomePolicy;
use App\Application\Policies\LayoutParamPolicy;
use App\Application\Policies\LibraryItemPolicy;
use App\Application\Policies\MaintenancePolicy;
use App\Application\Policies\MediaPolicy;
use App\Application\Policies\MusicListPolicy;
use App\Application\Policies\MusicPolicy;
use App\Application\Policies\NotificationPolicy;
use App\Application\Policies\OptionListCategoryPolicy;
use App\Application\Policies\OptionListPolicy;
use App\Application\Policies\OptionPolicy;
use App\Application\Policies\PermissionPolicy;
use App\Application\Policies\PlanPolicy;
use App\Application\Policies\ProcessedMediaPolicy;
use App\Application\Policies\ProjectPolicy;
use App\Application\Policies\ProjectScreenParamPolicy;
use App\Application\Policies\ProjectScreenPolicy;
use App\Application\Policies\RawMediaPolicy;
use App\Application\Policies\RenderErrorPolicy;
use App\Application\Policies\RenderJobPolicy;
use App\Application\Policies\RenderMediaPolicy;
use App\Application\Policies\RenderProjectHtmlPolicy;
use App\Application\Policies\RenderSubtitlePolicy;
use App\Application\Policies\RolePolicy;
use App\Application\Policies\ScreenCategoryPolicy;
use App\Application\Policies\ScreenFamilyPolicy;
use App\Application\Policies\ScreenLayoutPolicy;
use App\Application\Policies\ScreenPolicy;
use App\Application\Policies\ShareableLinkPolicy;
use App\Application\Policies\SnapshotPolicy;
use App\Application\Policies\SubscriptionPolicy;
use App\Application\Policies\TeamParamPolicy;
use App\Application\Policies\TeamPolicy;
use App\Application\Policies\TeamPresetPolicy;
use App\Application\Policies\TeamTemplatePolicy;
use App\Application\Policies\TemplateCategoryPolicy;
use App\Application\Policies\TemplatePolicy;
use App\Application\Policies\TemplateSubCategoryPolicy;
use App\Application\Policies\TimecodedElementPolicy;
use App\Application\Policies\TimecodedElementPresetPolicy;
use App\Application\Policies\UserPolicy;
use App\Application\Policies\UserTeamAppRolePolicy;
use App\Domain\TwoFactorAuth\CodeRepository as CodeRepositoryInterface;
use App\Infrastructure\TwoFactorAuth\CodeRepository;
use App\Models\BillingPlan;
use App\Models\Category;
use App\Models\Company;
use App\Models\Config;
use App\Models\CorporateLibraryItem;
use App\Models\CustomPage;
use App\Models\DeprecatedRawMedia;
use App\Models\FavoriteRawMedia;
use App\Models\Folder;
use App\Models\Maintenance;
use App\Models\Media;
use App\Models\Music\Music;
use App\Models\Music\MusicList;
use App\Models\Notification;
use App\Models\Plan;
use App\Models\ProcessedMedia;
use App\Models\Project;
use App\Models\ProjectScreen;
use App\Models\ProjectScreenParam;
use App\Models\RawMedia;
use App\Models\RenderError;
use App\Models\RenderJob;
use App\Models\Renders\RenderMedia;
use App\Models\Renders\RenderProjectHtml;
use App\Models\Renders\RenderSubtitle;
use App\Models\Screen;
use App\Models\Screen\Parameters\Option;
use App\Models\Screen\Parameters\OptionList;
use App\Models\Screen\Parameters\OptionListCategory;
use App\Models\Screen\ScreenFamily;
use App\Models\ScreenCategory;
use App\Models\ScreenLayout;
use App\Models\ScreenParams\BaseParam;
use App\Models\ShareableLink;
use App\Models\Snapshot;
use App\Models\SubCategory;
use App\Models\Subscription;
use App\Models\Team;
use App\Models\TeamPreset;
use App\Models\TeamTemplate;
use App\Models\Template;
use App\Models\TimecodedElement\TimecodedElement;
use App\Models\TimecodedElement\TimecodedElementPreset;
use App\Models\User;
use App\Models\UserTeamAppRole;
use App\Services\Auth\DoubleAuthGuard;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        User::class => UserPolicy::class,
        Role::class => RolePolicy::class,
        Permission::class => PermissionPolicy::class,
        Company::class => CompanyPolicy::class,
        RenderError::class => RenderErrorPolicy::class,
        Option::class => OptionPolicy::class,
        OptionListCategory::class => OptionListCategoryPolicy::class,
        MusicList::class => MusicListPolicy::class,
        OptionList::class => OptionListPolicy::class,
        Music::class => MusicPolicy::class,
        Media::class => MediaPolicy::class,
        CorporateLibraryItem::class => LibraryItemPolicy::class,
        Team::class => TeamPolicy::class,
        TeamTemplate::class => TeamTemplatePolicy::class,
        Maintenance::class => MaintenancePolicy::class,
        Config::class => ConfigPolicy::class,
        Template::class => TemplatePolicy::class,
        BaseParam::class => LayoutParamPolicy::class,
        TeamParamPolicy::class,
        RenderProjectHtml::class => RenderProjectHtmlPolicy::class,
        RenderMedia::class => RenderMediaPolicy::class,
        TeamPreset::class => TeamPresetPolicy::class,
        Project::class => ProjectPolicy::class,
        ProcessedMedia::class => ProcessedMediaPolicy::class,
        HomePolicy::class,
        BillingPlan::class => BillingPlanPolicy::class,
        Plan::class => PlanPolicy::class,
        RenderJob::class => RenderJobPolicy::class,
        Subscription::class => SubscriptionPolicy::class,
        ScreenCategory::class => ScreenCategoryPolicy::class,
        ScreenFamily::class => ScreenFamilyPolicy::class,
        RenderSubtitle::class => RenderSubtitlePolicy::class,
        RawMedia::class => RawMediaPolicy::class,
        DeprecatedRawMedia::class => DeprecatedRawMediaPolicy::class,
        Snapshot::class => SnapshotPolicy::class,
        ShareableLink::class => ShareableLinkPolicy::class,
        FavoriteRawMedia::class => FavoriteRawMediaPolicy::class,
        Notification::class => NotificationPolicy::class,
        ProjectScreen::class => ProjectScreenPolicy::class,
        ProjectScreenParam::class => ProjectScreenParamPolicy::class,
        Folder::class => FolderPolicy::class,
        Category::class => TemplateCategoryPolicy::class,
        SubCategory::class => TemplateSubCategoryPolicy::class,
        ScreenLayout::class => ScreenLayoutPolicy::class,
        Screen::class => ScreenPolicy::class,
        TimecodedElement::class => TimecodedElementPolicy::class,
        TimecodedElementPreset::class => TimecodedElementPresetPolicy::class,
        CustomPage::class => CustomPagePolicy::class,
        UserTeamAppRole::class => UserTeamAppRolePolicy::class,
        CutawayShotPolicy::RESOURCE_NAME => CutawayShotPolicy::class,
    ];

    public function boot(): void
    {
        $this->registerPolicies();
        $this->registerCustomPolicies();

        Auth::extend('double-auth', function ($app, $name, array $config) {
            $provider = Auth::createUserProvider($config['provider']);

            return new DoubleAuthGuard($name, $provider, $app['session.store'], request());
        });
    }

    private function registerCustomPolicies(): void
    {
        Gate::define('canAccessRestrictedData', CompanyPolicy::class);
    }

    public function getPolicies(): array
    {
        return $this->policies;
    }

    public function register(): void
    {
        $this->app->bind(CodeRepositoryInterface::class, CodeRepository::class);
    }
}
