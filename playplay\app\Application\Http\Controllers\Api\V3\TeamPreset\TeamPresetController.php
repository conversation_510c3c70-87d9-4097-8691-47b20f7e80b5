<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V3\TeamPreset;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Font\UpdateFontsRequest;
use App\Domain\TeamPreset\TeamPresetFontService;
use App\Domain\TeamPreset\TeamPresetRepository;
use App\Models\Team;
use App\Models\TeamPreset;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

class TeamPresetController extends BaseController
{
    use AuthorizesRequests;

    private TeamPresetRepository $teamPresetRepository;
    private TeamPresetFontService $teamPresetFontService;

    public function __construct(
        TeamPresetRepository $teamPresetRepository,
        TeamPresetFontService $teamPresetFontService
    ) {
        $this->teamPresetRepository = $teamPresetRepository;
        $this->teamPresetFontService = $teamPresetFontService;
    }

    public function show(Team $team): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('view', $team);

        $teamPreset = $this->teamPresetRepository->getByTeamId($team->id);

        return $this->sendJsonResponse(new Collection([$teamPreset]), Response::HTTP_OK);
    }

    public function updateFonts(Team $team, UpdateFontsRequest $request): JsonResponse
    {
        $fontIds = array_merge(
            [$request->input('default_font')],
            $request->input('additional_fonts', [])
        );

        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('update-fonts', [$team, $fontIds]);

        /** @var TeamPreset $teamPreset */
        $teamPreset = $team->presets()->first();

        $this->teamPresetFontService->updateFontsFromIds($teamPreset, $fontIds);
        $this->teamPresetFontService->setDefaultFont($teamPreset, $request->input('default_font'));

        return $this->sendJsonResponse(new Collection(), Response::HTTP_OK);
    }
}
