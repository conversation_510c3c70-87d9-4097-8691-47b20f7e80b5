<?php

declare(strict_types=1);

namespace App\Application\Listeners\UserAction;

use App\Application\Events\UserGenerateVideoOrRender;
use App\Domain\User\Action\UserAction;
use App\Domain\User\Action\UserActionService;

final class SendUserActionLogOnProjectRender
{
    private UserActionService $userActionService;

    public function __construct(UserActionService $userActionService)
    {
        $this->userActionService = $userActionService;
    }

    public function handle(UserGenerateVideoOrRender $userGenerateVideoOrRender): void
    {
        $render = $userGenerateVideoOrRender->getRenderProject();
        $project = $render->project;
        $projectTeamId = $project->team_id;
        $projectId = $project->id;

        $this->userActionService->addUserAction(
            new UserAction(
                'project-render-generated',
                [],
                $projectTeamId,
                $projectId
            )
        );
    }
}
