<?php

namespace App\Application\Http\Requests\Api\Project;

use App\Domain\Project\RawMedia\RelationType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProjectUploadedFilesIndexRequest extends FormRequest
{
    public static function rules(): array
    {
        return [
            'relation_type_key' => [
                'nullable',
                Rule::in(RelationType::getAllKeys()),
            ],
        ];
    }

    public function authorize(): bool
    {
        return true;
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'relation_type_key' => $this->route('relationTypeKey'),
        ]);
    }
}
