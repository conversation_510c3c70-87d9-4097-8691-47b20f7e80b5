<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\TeamUser;

use App\Application\Http\Requests\Api\Permission\UpdateUserTeamAppPermissionRequest;
use App\Domain\Permissions\AppRoleAppPermissionService;
use App\Domain\User\Serializer\UserWithTeamsAppRolesSerializer;
use App\Domain\UserTeam\UserTeamAppRoleRepository;
use App\Domain\UserTeam\UserTeamService;
use App\Models\Team;
use App\Models\User;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

final class UpdateUserTeamAppPermissionController extends Controller
{
    use AuthorizesRequests;

    private UserTeamService $userTeamService;
    private AppRoleAppPermissionService $appRoleAppPermissionService;
    private UserTeamAppRoleRepository $userTeamAppRoleRepository;
    private UserWithTeamsAppRolesSerializer $userWithTeamsAppRolesSerializer;

    public function __construct(
        UserTeamService $userTeamService,
        AppRoleAppPermissionService $appRoleAppPermissionService,
        UserTeamAppRoleRepository $userTeamAppRoleRepository,
        UserWithTeamsAppRolesSerializer $userWithTeamsAppRolesSerializer
    ) {
        $this->userTeamService = $userTeamService;
        $this->appRoleAppPermissionService = $appRoleAppPermissionService;
        $this->userTeamAppRoleRepository = $userTeamAppRoleRepository;
        $this->userWithTeamsAppRolesSerializer = $userWithTeamsAppRolesSerializer;
    }

    /**
     * @throws ValidationException|AuthorizationException
     */
    public function __invoke(UpdateUserTeamAppPermissionRequest $request, Team $team, User $user): JsonResponse
    {
        $appPermissionIds = $request->get('app_permission_ids');
        $userTeamAppRole = $this->userTeamAppRoleRepository->getOneByUserAndTeam($user, $team);

        $this->authorize('update-app-permissions', [$userTeamAppRole]);

        if (!$this->appRoleAppPermissionService
                ->appPermissionIdsBelongToAppRole($appPermissionIds, $userTeamAppRole->appRole)
        ) {
            throw ValidationException::withMessages(
                [['app_permission_ids' => 'App permissions do not belong to app role.']]
            );
        }

        if (!$this->appRoleAppPermissionService
                ->mandatoryAppPermissionIdsArePresentForAppRole($appPermissionIds, $userTeamAppRole->appRole)
        ) {
            throw ValidationException::withMessages(
                [['app_permission_ids' => 'Mandatory app permissions are missing for app role.']]
            );
        }

        $this->userTeamService->updateAppPermissionsOfUserInTeam($user, $team, $appPermissionIds);

        return new JsonResponse($this->userWithTeamsAppRolesSerializer->serialize([$user->fresh()]), Response::HTTP_OK);
    }
}
