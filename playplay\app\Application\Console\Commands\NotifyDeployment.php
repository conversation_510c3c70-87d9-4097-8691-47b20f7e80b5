<?php

namespace App\Application\Console\Commands;

use App\Application\Events\NewDeployment;
use Illuminate\Console\Command;

class NotifyDeployment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notify:new-deployment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Broadcasts an event to warn the SPA about a new deployment';

    public function handle(): int
    {
        // TODO replace by dependency injection
        event(new NewDeployment);

        return 0;
    }
}
