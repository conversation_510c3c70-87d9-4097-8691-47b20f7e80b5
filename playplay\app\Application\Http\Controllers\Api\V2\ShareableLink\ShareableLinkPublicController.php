<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\ShareableLink;

use App\Application\Http\Controllers\Api\BaseController;
use App\Domain\Cdn\CdnService;
use App\Domain\Project\ProjectFormat;
use App\Domain\Render\RenderProject\RenderStoryDownloadService;
use App\Domain\Render\RenderStorageService;
use App\Domain\ShareableLink\ShareableLinkSerializerInterface;
use App\Domain\User\Action\UserAction;
use App\Domain\User\Action\UserActionService;
use App\Models\Renders\ARender;
use App\Models\ShareableLink;
use App\Models\User;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\NotAcceptableHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

final class ShareableLinkPublicController extends BaseController
{
    private ShareableLinkSerializerInterface $serializer;
    private UserActionService $userActionService;
    private CdnService $cdnService;
    private Guard $guard;
    private RenderStorageService $renderStorageService;
    private RenderStoryDownloadService $renderStoryDownloadService;

    public function __construct(
        ShareableLinkSerializerInterface $shareableLinkSerializer,
        UserActionService $userActionService,
        RenderStoryDownloadService $renderStoryDownloadService,
        RenderStorageService $renderStorageService,
        CdnService $cdnService,
        Guard $guard
    ) {
        $this->userActionService = $userActionService;
        $this->serializer = $shareableLinkSerializer;
        $this->renderStoryDownloadService = $renderStoryDownloadService;
        $this->cdnService = $cdnService;
        $this->guard = $guard;
        $this->renderStorageService = $renderStorageService;
    }

    public function download(ShareableLink $shareableLink, Request $request): JsonResponse
    {
        $render = $shareableLink->renderProjectHtml;
        if ($shareableLink->isDeleted() || !$shareableLink->isExistingCompany()) {
            throw new NotFoundHttpException();
        }

        $canDownloadOnSharelink = $render->project->company?->getValueOfFeature('can_download_on_sharelink') ?? 0;
        if (!$shareableLink->can_download || $canDownloadOnSharelink === 0) {
            throw new NotAcceptableHttpException('The download on sharelink is disabled');
        }

        /** As a sharelink view can be password protected, the download route must be protected as well.
         * The download route now expects that the application provide the download URL.
         * This ensures that the user already have been authorized to view the video.
         */
        $downloadUrl = $request->get('url');
        if ($downloadUrl !== $this->cdnService->getCdnUrlFromUrl($render->rendered_url)) {
            throw new AccessDeniedHttpException('Invalid video URL');
        }

        if ($render->format === ProjectFormat::STORY) {
            $render = $render->renderStories->first();

            [$url] = $this->renderStoryDownloadService->getStoryRawUrls($render);
        } else {
            $url = $render->rendered_url;
        }

        $responseData = ['status' => $render->status];
        if ($render->status === ARender::STATUS_PROCESSED) {
            if (!$this->renderStorageService->urlExists($url)) {
                throw new NotFoundHttpException('Video link not found');
            }

            $responseData['url'] = $this->renderStorageService->getTemporaryVideoDownloadUrl($render, $url);

            $this->userActionService->addUserAction(
                new UserAction(
                    'shareable-link-downloaded',
                    [],
                    $render->project->team_id,
                    $render->project_id
                )
            );
        }

        return $this->sendJsonResponse(new Collection([$responseData]), Response::HTTP_OK);
    }

    /** @todo split into invokable controllers */
    public function show(ShareableLink $shareableLink, Request $request): JsonResponse
    {
        if ($shareableLink->isDeleted() || !$shareableLink->isExistingCompany()) {
            throw new NotFoundHttpException();
        }

        $companyHasPasswordOnShareLink = $shareableLink->renderProjectHtml->project->company->has_password_on_sharelink;
        if ($companyHasPasswordOnShareLink
            && !$shareableLink->isAllowedWithPassword($request->get('password') ?? '')
        ) {
            throw new NotAcceptableHttpException('Bad password');
        }

        /** @var User $user */
        $user = $this->guard->user();
        $serializedShareableLink = $this->serializer->serialize($shareableLink, $user);

        $project = $shareableLink->renderProjectHtml->project;

        $this->userActionService->addUserAction(
            (new UserAction(
                ($companyHasPasswordOnShareLink && $shareableLink->has_password)
                    ? 'shareable-link-visited-with-password'
                    : 'shareable-link-visited',
                [],
                $project->team_id,
                $project->id
            ))
        );

        return $this->sendJsonResponse(new Collection([$serializedShareableLink]), Response::HTTP_OK);
    }
}
