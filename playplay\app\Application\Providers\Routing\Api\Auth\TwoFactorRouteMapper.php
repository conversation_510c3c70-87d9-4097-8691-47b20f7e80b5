<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Auth;

use App\Application\Http\Controllers\Api\V2\Auth\TwoFactorController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class TwoFactorRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::get('verification-code', TwoFactorController::class)->name('verification-code');
    }
}
