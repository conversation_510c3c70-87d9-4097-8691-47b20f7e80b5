<?php

namespace App\Application\Http\Controllers\Auth;

use App\Application\Http\Controllers\Admin\BaseController;
use Illuminate\Foundation\Auth\SendsPasswordResetEmails;
use Illuminate\Http\Request;

class ForgotPasswordController extends BaseController
{
    use SendsPasswordResetEmails;

    public function showLinkRequestForm(Request $request)
    {
        return redirect(url("app/password/forgot?email={$request->get('email')}"));
    }
}
