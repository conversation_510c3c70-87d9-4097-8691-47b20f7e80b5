<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\TimecodedElement;

use Illuminate\Foundation\Http\FormRequest;

final class TimecodedElementPresetUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'backoffice_name' => ['required', 'string', 'max:120'],
            'animaniac_ref' => ['required', 'string', 'max:120'],
            'default_duration' => ['required', 'integer', 'min:0'],
            'thumbnail_url' => ['required', 'url'],
            'preview_url' => ['sometimes', 'url'],
            'activated_at' => ['sometimes', 'date'],
        ];
    }
}
