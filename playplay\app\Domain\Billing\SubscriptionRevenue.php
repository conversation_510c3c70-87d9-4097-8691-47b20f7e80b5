<?php

declare(strict_types=1);

namespace App\Domain\Billing;

final class SubscriptionRevenue
{
    public readonly int $monthlyRecurringRevenue;

    public readonly CurrencySign $currencySign;

    public function __construct(int $monthlyRecurringRevenue, string $currencyISOCode)
    {
        $this->monthlyRecurringRevenue = $monthlyRecurringRevenue;
        $this->currencySign = CurrencySign::fromISOCode($currencyISOCode);
    }

    public function getFormattedAnnualRecurringRevenue(): string
    {
        return ($this->monthlyRecurringRevenue * 12) . $this->currencySign->value;
    }

    public function getFormattedMonthlyRecurringRevenue(): string
    {
        return $this->monthlyRecurringRevenue . $this->currencySign->value;
    }
}
