<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\FavoriteRawMedia\FavoriteRawMediaRepository;
use App\Infrastructure\FavoriteRawMedia\EloquentFavoriteRawMediaRepository;
use Illuminate\Support\ServiceProvider;

class FavoriteRawMediaServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(FavoriteRawMediaRepository::class, EloquentFavoriteRawMediaRepository::class);
    }
}
