<?php

namespace App\Application\Rules;

use App\Models\User;
use Illuminate\Contracts\Validation\Rule;

class UniqueInactiveRule implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('validation.unique_user_inactive');
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed  $value
     *
     * @return bool
     */
    public function passes($attribute, $value)
    {
        /** @var ?User $user */
        $user = User::query()
            ->where('email', '=', $value)
            ->withTrashed()
            ->first();
        $company = $user?->company;

        if ($user !== null && ($company === null || $user->deleted_at)) {
            return false;
        }

        return true;
    }
}
