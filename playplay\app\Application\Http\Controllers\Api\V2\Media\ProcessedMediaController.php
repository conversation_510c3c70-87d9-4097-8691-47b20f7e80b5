<?php

namespace App\Application\Http\Controllers\Api\V2\Media;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Project\ProcessedMedia\Store\ParamProcessedMediaAudioStoreRequest;
use App\Application\Http\Requests\Api\Project\ProcessedMedia\Store\ProcessedMediaOutroStoreRequest;
use App\Application\Http\Requests\Api\Project\ProcessedMedia\Update\ParamProcessedMediaAudioUpdateRequest;
use App\Domain\Project\ProcessedMedia\RelationType;
use App\Domain\Render\RenderMedia\Crop\CropFactory;
use App\Domain\Video\Format\VideoFormatRepository;
use App\Models\ProcessedMedia;
use App\Models\RawMedia;
use App\Services\ProcessedMedia\AccessChecker;
use App\Services\ProcessedMedia\Factory;
use App\Services\ProcessedMedia\FilterApplier;
use App\Services\Processing\RenderMediaService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

final class ProcessedMediaController extends BaseController
{
    use AuthorizesRequests;

    private RenderMediaService $renderService;
    private Factory $processedMediaFactory;
    private FilterApplier $filterApplier;
    private AccessChecker $accessChecker;
    private VideoFormatRepository $videoFormatRepository;

    public function __construct(
        RenderMediaService $renderService,
        AccessChecker $accessChecker,
        Factory $processedMediaFactory,
        FilterApplier $filterApplier,
        VideoFormatRepository $videoFormatRepository,
    ) {
        $this->renderService = $renderService;
        $this->accessChecker = $accessChecker;
        $this->processedMediaFactory = $processedMediaFactory;
        $this->filterApplier = $filterApplier;
        $this->videoFormatRepository = $videoFormatRepository;
    }

    public function show(ProcessedMedia $processedMedia): JsonResponse
    {
        $this->authorize('view', $processedMedia);

        return $this->sendJsonResponse(new Collection([$processedMedia]), Response::HTTP_OK);
    }

    public function storeParamProcessedMediaAudio(ParamProcessedMediaAudioStoreRequest $request): JsonResponse
    {
        $rawMediaId = $request->get('raw_media_id');
        $source = $request->get('source');
        $projectId = $request->get('project_id');

        $this->accessChecker->checkIfCanCreateProcessedMediaFromRawMediaAndSourceAndProject(
            $rawMediaId,
            $source,
            $projectId
        );
        $processedMedia = $this->createAudioProcessedMedia(
            $rawMediaId,
            $request,
            RelationType::LIBRARY
        );

        return $this->sendJsonResponse(new Collection([$processedMedia]), Response::HTTP_CREATED);
    }

    public function storeProcessedMediaOutro(ProcessedMediaOutroStoreRequest $request): JsonResponse
    {
        $rawMediaId = $request->get('raw_media_id');
        $rawMedia = RawMedia::find($rawMediaId);
        $this->authorize('createBranding', [ProcessedMedia::class, $rawMedia]);
        $processedMedia = $this->processedMediaFactory->create(
            $rawMediaId,
            $request->get('source'),
            $request->get('project_id')
        );
        $videoFormat = $this->videoFormatRepository->getByFormat($request->get('format'));

        $this->renderService->outro(
            $processedMedia,
            [
                'crop' => CropFactory::createAnchoredCropFromArray([
                    'width' => $videoFormat->getWidth(),
                    'height' => $videoFormat->getHeigth(),
                ])->toArray(),
            ]
        );

        return $this->sendJsonResponse(new Collection([$processedMedia]), Response::HTTP_CREATED);
    }

    public function updateParamProcessedMediaAudioTrim(
        ProcessedMedia $processedMedia,
        ParamProcessedMediaAudioUpdateRequest $request
    ): JsonResponse {
        $this->authorize('update', $processedMedia);

        if (!$processedMedia->rawMedia->isAudio()) {
            throw new BadRequestHttpException();
        }

        $this->filterApplier->trimAudio(
            $processedMedia,
            $request->get('trimStart'),
            $request->get('trimEnd')
        );

        return $this->sendJsonResponse(new Collection([$processedMedia]), Response::HTTP_OK);
    }

    private function createAudioProcessedMedia(
        int $rawMediaId,
        Request $request,
        RelationType $relationType
    ): ProcessedMedia {
        $processedMedia = $this->processedMediaFactory->create(
            $rawMediaId,
            $request->get('source'),
            $request->get('project_id'),
            $relationType
        );

        $this->filterApplier->trimAudio(
            $processedMedia,
            $request->get('trimStart'),
            $request->get('trimEnd')
        );

        return $processedMedia;
    }
}
