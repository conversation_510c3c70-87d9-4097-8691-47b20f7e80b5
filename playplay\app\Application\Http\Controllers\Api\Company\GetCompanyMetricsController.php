<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\Company;

use App\Application\Http\Controllers\Api\BaseController;
use App\Domain\Company\Repositories\CompanyRepository;
use App\Models\Company;
use App\Models\User;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;

final class GetCompanyMetricsController extends BaseController
{
    use AuthorizesRequests;

    private Guard $guard;
    private CompanyRepository $companyRepository;

    public function __construct(Guard $guard, CompanyRepository $companyRepository)
    {
        $this->guard = $guard;
        $this->companyRepository = $companyRepository;
    }

    public function __invoke(Company $company): JsonResponse
    {
        /** @var User $user */
        $user = $this->guard->user();

        $this->authorize('canAccessRestrictedData', $company);

        if ($user->company_id !== $company->id) {
            throw new AccessDeniedHttpException('Trying to access to another company data');
        }

        $companyMetrics = $this->companyRepository->getCompanyMetrics($company);

        return $this->sendJsonResponse(new Collection([$companyMetrics]), Response::HTTP_OK);
    }
}
