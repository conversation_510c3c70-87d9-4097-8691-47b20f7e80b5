<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Template\Repositories\CategoryRepository;
use App\Domain\Template\Repositories\HomepageTemplateRepository;
use App\Domain\Template\Repositories\SubCategoryRepository;
use App\Domain\Template\Repositories\TemplateCoverRepository;
use App\Domain\Template\Repositories\TemplateProjectRepository;
use App\Domain\Template\Repositories\TemplateRepository;
use App\Infrastructure\Template\Repositories\EloquentCategoryRepository;
use App\Infrastructure\Template\Repositories\EloquentHomepageTemplateRepository;
use App\Infrastructure\Template\Repositories\EloquentSubCategoryRepository;
use App\Infrastructure\Template\Repositories\EloquentTemplateCoverRepository;
use App\Infrastructure\Template\Repositories\EloquentTemplateProjectRepository;
use App\Infrastructure\Template\Repositories\EloquentTemplateRepository;
use Illuminate\Support\ServiceProvider;

class TemplateServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->bind(TemplateProjectRepository::class, EloquentTemplateProjectRepository::class);
        $this->app->bind(TemplateCoverRepository::class, EloquentTemplateCoverRepository::class);
        $this->app->bind(SubCategoryRepository::class, EloquentSubCategoryRepository::class);
        $this->app->bind(CategoryRepository::class, EloquentCategoryRepository::class);
        $this->app->bind(TemplateRepository::class, EloquentTemplateRepository::class);
        $this->app->bind(HomepageTemplateRepository::class, EloquentHomepageTemplateRepository::class);
    }
}
