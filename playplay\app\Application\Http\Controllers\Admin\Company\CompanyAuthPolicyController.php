<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Company;

use App\Application\Events\SsoEnabledForCompany;
use App\Application\Http\Controllers\Admin\BaseController;
use App\Models\Company;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

final class CompanyAuthPolicyController extends BaseController
{
    private Dispatcher $eventDispatcher;

    public function __construct(Dispatcher $eventDispatcher)
    {
        $this->authorizeResource(Company::class);
        $this->eventDispatcher = $eventDispatcher;
    }

    public function enable2FA(Request $request, Company $company): RedirectResponse
    {
        $this->authorize('canAccessRestrictedData', $company);
        if ($company->sso_enabled) {
            return redirect()->back()->withErrors("Can't activate 2FA with SSO enabled");
        }

        $company->update(['two_fa_enabled' => $request->get('2fa_enabled')]);

        return redirect()->back();
    }

    public function enableSso(Request $request, Company $company): RedirectResponse
    {
        $this->authorize('canAccessRestrictedData', $company);
        if ($company->two_fa_enabled) {
            return redirect()->back()->withErrors("Can't activate SSO with 2FA enabled");
        }

        if ($this->isSsoEnabledForACompanyRightNow($request, $company)) {
            $this->eventDispatcher->dispatch(new SsoEnabledForCompany($company));
        }

        $organizationId = $request->get('workos_organization_id');
        $company->update(
            [
                'sso_enabled' => $request->boolean('sso_enabled'),
                'workos_organization_id' => $organizationId === '' ? null : $organizationId
            ]
        );

        return redirect()->back();
    }

    private function isSsoEnabledForACompanyRightNow(Request $request, Company $company): bool
    {
        return $request->get('sso_enabled') && !$company->getOriginal('sso_enabled');
    }
}
