<?php

namespace App\Application\Console\Commands\Database\ClearForLightDB;

use Illuminate\Console\Command;
use Illuminate\Log\Logger;
use Illuminate\Support\Facades\App;
use RuntimeException;

class ClearForLightDB extends Command
{
    protected $signature = 'db:clear-for-light-db';
    protected $description = 'Clear data for light db';
    private CleanDatabaseForUserService $cleanDatabaseForUserService;
    private DeleteDataForLightDatabaseService $deleteDataForLightDatabaseService;
    private Logger $logger;

    public function __construct(
        DeleteDataForLightDatabaseService $deleteDataForLightDatabaseService,
        CleanDatabaseForUserService $cleanDatabaseForUserService,
        Logger $logger
    ) {
        parent::__construct();
        $this->deleteDataForLightDatabaseService = $deleteDataForLightDatabaseService;
        $this->cleanDatabaseForUserService = $cleanDatabaseForUserService;
        $this->logger = $logger;
    }

    public function handle(): int
    {
        if (App::environment() === 'production') {
            $this->logger->warning('This cannot be run in production');
            return 1;
        }

        $this->deleteDataForLightDatabaseService->deleteUselessData();
        $this->cleanDatabaseForUserService->deactivateSsoForPlayPlay();
        $this->cleanDatabaseForUserService->changeNullPasswordsToRandomValue();

        return 0;
    }
}
