<?php

declare(strict_types=1);

namespace App\Domain\Anonymizer;

use Throwable;

class DataAnonymizer
{
    /**
     * @var iterable<AnonymizableInterface>
     */
    private iterable $anonymizers;

    public function __construct(iterable $anonymizers)
    {
        $this->anonymizers = $anonymizers;
    }

    /**
     * @throws DataAnonymizerException
     */
    public function anonymize(): void
    {
        try {
            foreach ($this->anonymizers as $anonymizer) {
                $anonymizer->anonymize();
            }
        } catch (Throwable $e) {
            throw new DataAnonymizerException($e->getMessage());
        }
    }
}
