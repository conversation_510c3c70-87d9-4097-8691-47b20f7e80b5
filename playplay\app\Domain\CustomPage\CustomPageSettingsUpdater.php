<?php

namespace App\Domain\CustomPage;

use App\Models\CustomPage;

class CustomPageSettingsUpdater
{
    private CustomPageRepository $customPageRepository;

    private TeamPresetValidator $teamPresetValidator;

    public function __construct(
        CustomPageRepository $customPageRepository,
        TeamPresetValidator $teamPresetValidator,
    ) {
        $this->customPageRepository = $customPageRepository;
        $this->teamPresetValidator = $teamPresetValidator;
    }

    public function update(
        CustomPage $customPage,
        CustomPageSettingsUpdaterDTO $customPageSettingsUpdaterDTO,
    ): void {
        $mainLogoHasChanged = $customPageSettingsUpdaterDTO->mainLogoProcessedMediaId
            !== $customPage->main_logo_processed_media_id;
        $fontHasChanged = $customPageSettingsUpdaterDTO->fontId !== $customPage->font_id;

        $this->teamPresetValidator->validate(
            $customPage->project->team_id,
            $customPageSettingsUpdaterDTO->primaryColor,
            $fontHasChanged ? $customPageSettingsUpdaterDTO->fontId : null,
            $mainLogoHasChanged ? $customPageSettingsUpdaterDTO->mainLogoProcessedMediaId : null
        );

        $this->customPageRepository->updateSettings($customPage, $customPageSettingsUpdaterDTO);
    }
}
