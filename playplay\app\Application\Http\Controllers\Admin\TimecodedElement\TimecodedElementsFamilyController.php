<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\TimecodedElement;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\TimecodedElement\TimecodedElementsFamilyIndexRequest;
use App\Application\Http\Requests\Admin\TimecodedElement\TimecodedElementsFamilyStoreRequest;
use App\Application\Http\Requests\Admin\TimecodedElement\TimecodedElementsFamilyUpdateRequest;
use App\Domain\TimecodedElement\Repositories\TimecodedElementsFamilyRepository;
use App\Domain\TimecodedElement\TimecodedElementsFamilyService;
use App\Domain\TimecodedElement\TimecodedElementsFamilyType;
use App\Models\TimecodedElement\TimecodedElementsFamily;
use Illuminate\Contracts\Routing\UrlGenerator;
use Illuminate\Contracts\View\Factory as ViewFactory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Redirector;

final class TimecodedElementsFamilyController extends BaseController
{
    public const TIMECODED_ELEMENTS_FAMILIES_PER_PAGE = 50;

    private TimecodedElementsFamilyRepository $timecodedElementsFamilyRepository;
    private TimecodedElementsFamilyService $timecodedElementsFamilyService;
    private ViewFactory $viewFactory;
    private Redirector $redirectService;
    private UrlGenerator $urlGenerator;

    public function __construct(
        TimecodedElementsFamilyRepository $timecodedElementFamilyRepository,
        TimecodedElementsFamilyService $timecodedElementsFamilyService,
        ViewFactory $viewFactory,
        Redirector $redirectService,
        UrlGenerator $urlGenerator
    ) {
        $this->timecodedElementsFamilyRepository = $timecodedElementFamilyRepository;
        $this->timecodedElementsFamilyService = $timecodedElementsFamilyService;
        $this->viewFactory = $viewFactory;
        $this->redirectService = $redirectService;
        $this->urlGenerator = $urlGenerator;
    }

    public function index(TimecodedElementsFamilyIndexRequest $request): View
    {
        $timecodedElementsFamiliesType = $request->get('type', TimecodedElementsFamilyType::TYPE_KEYWORDS->value);
        $paginatedTimecodedElementsFamilies = $this->timecodedElementsFamilyRepository
            ->getPaginatedByTypeWithPresetsOrderedByBackofficeName(
                $timecodedElementsFamiliesType,
                self::TIMECODED_ELEMENTS_FAMILIES_PER_PAGE
            );

        $paginatedTimecodedElementsFamilies->appends(['type' => $timecodedElementsFamiliesType]);

        return $this->viewFactory->make('admin.timecoded-elements-family.index', [
            'tabActive' => $timecodedElementsFamiliesType,
            'allowedTypes' => TimecodedElementsFamilyType::values(),
            'timecodedElementsFamilies' => $paginatedTimecodedElementsFamilies,
        ]);
    }

    public function create(): View
    {
        $timecodedElementsFamily = new TimecodedElementsFamily();

        return $this->viewFactory->make('admin.timecoded-elements-family.create', [
            'timecodedElementsFamily' => $timecodedElementsFamily,
            'allowedTypes' => TimecodedElementsFamilyType::values(),
        ]);
    }

    public function edit(TimecodedElementsFamily $timecodedElementsFamily): View
    {
        return $this->viewFactory->make('admin.timecoded-elements-family.edit', [
            'timecodedElementsFamily' => $timecodedElementsFamily,
            'timecodedElementPresets' => $timecodedElementsFamily->timecodedElementPresets->sortBy('order'),
            'allowedTypes' => TimecodedElementsFamilyType::values(),
        ]);
    }

    public function store(TimecodedElementsFamilyStoreRequest $request): RedirectResponse
    {
        $timecodedElementsFamilyType = $request->get('type');
        $this->timecodedElementsFamilyRepository->create(
            $request->get('backoffice_name'),
            $timecodedElementsFamilyType,
            (bool) $request->get('is_generic'),
        );

        return $this->redirectService->route(
            'admin.timecoded-elements-family.index',
            ['type' => $timecodedElementsFamilyType]
        );
    }

    public function update(
        TimecodedElementsFamily $timecodedElementsFamily,
        TimecodedElementsFamilyUpdateRequest $request
    ): RedirectResponse {
        $timecodedElementsFamilyType = $request->get('type');
        $this->timecodedElementsFamilyRepository->update(
            $timecodedElementsFamily,
            $request->get('backoffice_name'),
            $timecodedElementsFamilyType
        );

        return $this->redirectService->route(
            'admin.timecoded-elements-family.index',
            ['type' => $timecodedElementsFamilyType]
        );
    }

    public function destroy(TimecodedElementsFamily $timecodedElementsFamily): JsonResponse
    {
        $this->timecodedElementsFamilyService->delete($timecodedElementsFamily);

        return new JsonResponse([
            'success' => true,
            'redirect' => $this->urlGenerator->route(
                'admin.timecoded-elements-family.index',
                ['type' => $timecodedElementsFamily->type]
            )
        ]);
    }

    public function showDangerZone(TimecodedElementsFamily $timecodedElementsFamily): View
    {
        return $this->viewFactory->make('admin.timecoded-elements-family.danger-zone', [
            'timecodedElementsFamily' => $timecodedElementsFamily
        ]);
    }
}
