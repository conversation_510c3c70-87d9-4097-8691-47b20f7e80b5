<?php

namespace App\Application\Policies;

use App\Models\ProjectScreenParam;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class ProjectScreenParamPolicy extends DefaultPolicy
{
    use HandlesAuthorization;

    public function export(User $user, ProjectScreenParam $projectScreenParam): bool
    {
        return $user->company->getValueOfFeature('import_export_subtitles') === 1
            && $user->can(
                'view',
                $projectScreenParam->project
            );
    }
}
