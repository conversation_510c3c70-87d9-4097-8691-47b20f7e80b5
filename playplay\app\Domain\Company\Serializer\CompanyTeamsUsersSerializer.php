<?php

namespace App\Domain\Company\Serializer;

use App\Domain\Team\Serializer\TeamFeaturesSerializer;
use App\Domain\User\Serializer\UserWithTeamsAppRolesSerializer;
use App\Domain\User\UserRepository;
use App\Models\Company;
use App\Models\Team;

class CompanyTeamsUsersSerializer
{
    private CompanySerializer $companySerializer;

    private TeamFeaturesSerializer $teamFeaturesSerializer;

    private UserWithTeamsAppRolesSerializer $userWithTeamsAppRolesSerializer;

    private UserRepository $userRepository;

    public function __construct(
        CompanySerializer $companySerializer,
        TeamFeaturesSerializer $teamFeaturesSerializer,
        UserWithTeamsAppRolesSerializer $userWithTeamsAppRolesSerializer,
        UserRepository $userRepository,
    ) {
        $this->companySerializer = $companySerializer;
        $this->teamFeaturesSerializer = $teamFeaturesSerializer;
        $this->userWithTeamsAppRolesSerializer = $userWithTeamsAppRolesSerializer;
        $this->userRepository = $userRepository;
    }

    /**
     * @param Team[] $teams
     */
    public function serialize(Company $company, array $teams): array
    {
        $serializedCompany = $this->companySerializer->serialize($company);
        $serializedCompany['teams'] = $this->teamFeaturesSerializer->serialize($teams);
        $serializedCompany['users'] = $this->userWithTeamsAppRolesSerializer->serialize(
            $this->userRepository->getUsersInTeamsFromCompany($company)->all()
        );

        return $serializedCompany;
    }
}
