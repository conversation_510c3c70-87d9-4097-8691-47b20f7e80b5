<?php

declare(strict_types=1);

namespace App\Domain\Company;

use App\Domain\Company\Repositories\CompanyHistoryRepository;
use App\Models\Company;
use App\Models\CompanyHistory;
use DateTimeImmutable;
use Illuminate\Support\Collection;

final class CompanyHistoryService
{
    private CompanyHistoryRepository $repository;

    public function __construct(CompanyHistoryRepository $repository)
    {
        $this->repository = $repository;
    }

    public function bulkUpsert(Collection $companies): void
    {
        $lastCompaniesHistories =
            $this->repository->getLastFromMany($companies);

        $companiesWithoutHistory = [];
        $historiesIdsToMarkAsFinished = [];
        foreach ($companies as $company) {
            $companyHistory = $lastCompaniesHistories->firstWhere('company_id', '=', $company->id);

            if ($companyHistory === null) {
                $companiesWithoutHistory[] = $company;
                continue;
            }

            if ($this->hasTypeOrStatusChanged($company, $companyHistory)) {
                $historiesIdsToMarkAsFinished[] = $companyHistory->id;
            }
        }

        $companiesToUpdate = $companies->filter(
            fn(Company $company) => !in_array($company, $companiesWithoutHistory, true)
        )->all();
        $this->repository->createMany(array_merge($companiesWithoutHistory, $companiesToUpdate));
        $this->repository->endMany($historiesIdsToMarkAsFinished, new DateTimeImmutable());
    }

    public function upsert(Company $company): void
    {
        $currentHistory = $this->repository->getLast($company);

        if ($currentHistory === null) {
            $this->repository->create($company);

            return;
        }

        if ($this->hasTypeOrStatusChanged($company, $currentHistory)) {
            $this->repository->create($company);
            $this->repository->ends($currentHistory, $company->updated_at);
        }
    }

    private function hasTypeOrStatusChanged(Company $company, CompanyHistory $currentHistory): bool
    {
        return $company->type !== $currentHistory->type || $company->status !== $currentHistory->status;
    }
}
