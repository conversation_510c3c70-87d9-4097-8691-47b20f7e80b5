<?php

declare(strict_types=1);

namespace App\Application\Mail\UserNotification;

use App\Models\User;
use Illuminate\Mail\Mailable;

class SsoEnabledNotification extends Mailable
{
    protected $user;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    public function build(): Mailable
    {
        return $this->from('<EMAIL>', __('email.from'))
            ->subject(__('email.sso_enabled_notification_to_user.subject'))
            ->markdown('emails.sso-enabled-notification-to-user', [
                'name' => $this->user->first_name,
                'language' => $this->user->language,
                'loginUrl' => $this->getLoginUrl(),
            ]);
    }

    private function getLoginUrl(): string
    {
        return url('app/login?lang=' . $this->user->preferredLocale());
    }
}
