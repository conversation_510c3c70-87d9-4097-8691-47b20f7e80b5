<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\WaitingGif\WaitingGifRepository;
use App\Infrastructure\WaitingGif\LocalWaitingGifRepository;
use Illuminate\Support\ServiceProvider;

final class WaitingGifProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(WaitingGifRepository::class, function () {
            return new LocalWaitingGifRepository(public_path() . '/img/waiting-gif');
        });
    }
}
