<?php

namespace App\Application\Policies;

use App\Domain\Project\ProjectPolicy as DomainProjectPolicy;
use App\Models\ProcessedMedia;
use App\Models\Project;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class ProjectPolicy
{
    use HandlesAuthorization;

    private DomainProjectPolicy $domainProjectPolicy;

    public function __construct(DomainProjectPolicy $domainProjectPolicy)
    {
        $this->domainProjectPolicy = $domainProjectPolicy;
    }

    public function before($user, $ability)
    {
        return $user->is_admin ?: null;
    }

    public function canPrivatizeOrShare(User $user, Project $project): bool
    {
        return $this->domainProjectPolicy->canPrivatizeOrShare($user, $project);
    }

    public function createShareableLink(User $user, Project $project): bool
    {
        return $user->teams->pluck('id')->contains($project->team_id);
    }

    public function destroy(User $user, Project $project): bool
    {
        return $user->teams->pluck('id')->contains($project->team_id);
    }

    public function download(User $user, Project $project): bool
    {
        return $user->teams->pluck('id')->contains($project->team_id);
    }

    public function duplicate(User $user, Project $project): bool
    {
        return $this->view($user, $project);
    }

    public function list(User $user): bool
    {
        return $user->is_admin;
    }

    public function rename(User $user, Project $project): bool
    {
        return $user->teams->pluck('id')->contains($project->team_id);
    }

    public function restore(User $user): bool
    {
        return $user->is_admin;
    }

    public function update(User $user, Project $project, array $processedMediaIds = []): bool
    {
        return $this->view($user, $project) && $user->can('viewMany', [ProcessedMedia::class, $processedMediaIds]);
    }

    public function view(User $user, Project $project): bool
    {
        if ($project->is_template_model) {
            return true;
        }

        if ($project->is_private) {
            return $project->user_id === $user->id;
        }

        return $user->teams->contains($project->team_id);
    }

    public function reorder(User $user, Project $project): bool
    {
        return $this->view($user, $project);
    }
}
