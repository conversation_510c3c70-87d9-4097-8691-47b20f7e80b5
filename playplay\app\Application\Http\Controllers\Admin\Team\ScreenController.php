<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Team;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Domain\Screen\ScreenRepository;
use App\Models\Team;
use Illuminate\Contracts\View\View;

final class ScreenController extends BaseController
{
    /** @var ScreenRepository */
    private $screenRepository;

    public function __construct(ScreenRepository $screenRepository)
    {
        $this->screenRepository = $screenRepository;
    }

    public function index(Team $team): View
    {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('view', $team);

        return view('admin.teams.screens.index', [
            'tabActive' => 'screens',
            'genericScreens' => $this->screenRepository->getGenericScreensForTeam($team),
            'customScreens' => $this->screenRepository->getCustomScreensForTeam($team),
            'team' => $team,
        ]);
    }
}
