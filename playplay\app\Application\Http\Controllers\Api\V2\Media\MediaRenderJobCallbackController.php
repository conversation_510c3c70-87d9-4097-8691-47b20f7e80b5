<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Media;

use App\Application\Http\Requests\Api\Media\MediaRenderJobCallbackRequest;
use App\Domain\Workflow\Config\MediaWorkflow;
use App\Domain\Workflow\Dto\RenderErrorDto;
use App\Domain\RenderJob\RenderJobRepository;
use App\Models\RenderJob;
use Illuminate\Http\Response;

final class MediaRenderJobCallbackController
{
    private RenderJobRepository $renderJobRepository;
    private MediaWorkflow $mediaWorkflow;

    public function __construct(RenderJobRepository $renderJobRepository, MediaWorkflow $mediaWorkflow)
    {
        $this->renderJobRepository = $renderJobRepository;
        $this->mediaWorkflow = $mediaWorkflow;
    }

    public function __invoke(RenderJob $renderJob, MediaRenderJobCallbackRequest $request): Response
    {
        $metrics = [
            'config' => $request->input('config'),
            'metrics' => $request->input('metrics'),
        ];

        if ($request->has('error')) {
            $this->renderJobRepository->updateMetrics($renderJob, $metrics);
            $this->mediaWorkflow->error(
                $renderJob,
                new RenderErrorDto(
                    (string) $request->input('error')['type'],
                    $request->input('error')['message'],
                    $request->input('result') ?? [],
                    $request->input('config') ?? [],
                    $request->input('metrics') ?? []
                )
            );

            return new Response();
        }

        if ($request->has('result')) {
            $this->renderJobRepository->updateResultAndMetrics(
                $renderJob,
                $request->input('result'),
                $metrics
            );
            $this->mediaWorkflow->next($renderJob);

            return new Response();
        }

        return new Response(
            'Error: missing error or result',
            Response::HTTP_BAD_REQUEST,
        );
    }
}
