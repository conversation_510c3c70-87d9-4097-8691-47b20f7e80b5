<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Admin;

use App\Application\Http\Controllers\Admin\PermissionController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class PermissionRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::delete('permissions/delete', [PermissionController::class, 'destroyMany'])->name('permissions.delete');
    }
}
