<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Auth;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Auth\ResetPasswordEmailRequest;
use App\Application\Http\Requests\Api\Auth\ResetPasswordShowRequest;
use App\Application\Http\Requests\Api\Auth\ResetPasswordStoreRequest;
use App\Application\Mail\Welcome;
use App\Domain\TwoFactorAuth\CodeGenerator as TwoFactorAuthCodeGenerator;
use App\Domain\User\Action\UserAction;
use App\Domain\User\Action\UserActionService;
use App\Domain\User\PasswordResetService;
use App\Domain\User\UserRepository;
use App\Infrastructure\TwoFactorAuth\CookieService as TwoFactorAuthCookieService;
use App\Models\PasswordReset;
use App\Models\User;
use Illuminate\Config\Repository as ConfigRepository;
use Illuminate\Foundation\Auth\ResetsPasswords;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Password;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotAcceptableHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

final class ResetPasswordController extends BaseController
{
    use ResetsPasswords;

    public const TYPE_CREATE = 'create';
    public const TYPE_RESET = 'reset';

    private UserRepository $userRepository;

    private TwoFactorAuthCodeGenerator $twoFactorAuthCodeGenerator;

    private TwoFactorAuthCookieService $twoFactorAuthCookieService;

    private UserActionService $userActionService;

    private PasswordResetService $passwordResetService;

    private ConfigRepository $configRepository;

    public function __construct(
        UserRepository $userRepository,
        TwoFactorAuthCodeGenerator $codeGenerator,
        TwoFactorAuthCookieService $cookieService,
        UserActionService $userActionService,
        PasswordResetService $passwordResetService,
        ConfigRepository $configRepository
    ) {
        $this->userRepository = $userRepository;
        $this->twoFactorAuthCodeGenerator = $codeGenerator;
        $this->twoFactorAuthCookieService = $cookieService;
        $this->userActionService = $userActionService;
        $this->passwordResetService = $passwordResetService;
        $this->configRepository = $configRepository;
    }

    public function email(ResetPasswordEmailRequest $request): JsonResponse
    {
        $user = $this->userRepository->getUserFromEmail($request->input('email'));
        if ($user === null) {
            return $this->sendResetResponse(Password::INVALID_USER);
        }

        if ($this->userRepository->userMustLoginWithSSO($user->email)) {
            throw new NotAcceptableHttpException('email.sso_mandatory');
        }

        $resetResponse = $this->broker(self::TYPE_RESET)->sendResetLink(
            $request->only('email')
        );

        $this->userActionService->addUserAction(new UserAction('reset-password-email-sent'));

        return $this->sendResetResponse($resetResponse);
    }

    public function show($token, ResetPasswordShowRequest $request): JsonResponse
    {
        $passwordReset = PasswordReset::query()->where('email', $request->get('email'))->first();

        if ($passwordReset === null) {
            throw new NotFoundHttpException();
        }

        $isPasswordResetExpired = $this->passwordResetService->isExpired($passwordReset->created_at);
        if (!$isPasswordResetExpired && Hash::check($token, $passwordReset->token)) {
            return $this->sendJsonResponse(new Collection([]), Response::HTTP_OK);
        }

        return $this->sendJsonResponse(new Collection([]), Response::HTTP_GONE);
    }

    public function store(ResetPasswordStoreRequest $request): JsonResponse
    {
        $resetType = $request->get('type');
        $resetResponse = $this->broker($resetType)->reset(
            $this->credentials($request),
            function ($user, $password) use ($request) {
                $this->resetPassword($user, $password);
                $this->createTwoFactorAuthCodeIfNeeded($request);
            }
        );

        return $this->sendResetResponse($resetResponse, $resetType);
    }

    private function broker($type)
    {
        if ($type === self::TYPE_CREATE) {
            return app('auth.password.broker.new_users');
        }

        return app('auth.password.broker');
    }

    private function createTwoFactorAuthCodeIfNeeded(ResetPasswordStoreRequest $request): void
    {
        if (!$request->isCreation()) {
            return;
        }

        $email = $request->input('email');
        if (!$this->userRepository->userMustProvide2FA($email)) {
            return;
        }

        /**
         * We create a 2FA code that won't be sent to the user, but only stored in her cookies so that
         * she won't need to perform 2FA once her session expires on her current device
         */
        $user = $this->userRepository->getUserFromEmail($email);
        $code = $this->twoFactorAuthCodeGenerator->generate($user);
        $this->twoFactorAuthCookieService->setCookieWithCode($code);
    }

    private function sendResetResponse(string $resetResponse, $resetType = self::TYPE_RESET): JsonResponse
    {
        switch ($resetResponse) {
            case Password::PASSWORD_RESET:
                if ($resetType === self::TYPE_CREATE) {
                    /** @var User $user */
                    $user = auth()->user();
                    $user->update([
                        'terms' => $this->configRepository->get('app.terms.version'),
                    ]);
                    Mail::to($user)->send(new Welcome($user));
                }

                return $this->sendJsonResponse(new Collection([['redirect_to' => 'team-home']]), Response::HTTP_OK);
            case Password::RESET_LINK_SENT:
                return $this->sendJsonResponse(new Collection([[]]), Response::HTTP_OK);
            case Password::INVALID_TOKEN:
                return $this->sendJsonResponse(new Collection([]), Response::HTTP_GONE);
            case Password::INVALID_USER:
                return $this->sendJsonResponse(new Collection([]), Response::HTTP_FORBIDDEN);
            default:
                return $this->sendJsonResponse(new Collection([]), Response::HTTP_BAD_REQUEST);
        }
    }
}
