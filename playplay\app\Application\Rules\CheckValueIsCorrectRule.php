<?php

namespace App\Application\Rules;

use App\Models\ProcessedMedia;
use App\Models\ScreenParams\BaseParam;
use Illuminate\Contracts\Validation\Rule;

class CheckValueIsCorrectRule implements Rule
{
    public function passes($attribute, $value)
    {
        $paramID = $this->getParamIdFromAttribute($attribute);
        $param = BaseParam::find($paramID);
        if (!$param) {
            return false;
        }

        if (in_array($param->type, [BaseParam::TYPE_MEDIA, BaseParam::TYPE_CUTAWAY_SHOT], true)) {
            return ProcessedMedia::find($value) !== null;
        }

        return true;
    }

    public function message()
    {
        return 'CheckValueIsCorrectRule is not valid';
    }

    private function getParamIdFromAttribute($attribute)
    {
        $attributeIndexes = explode('.', $attribute);
        $requestParams = request()->get('params');

        return isset($requestParams[$attributeIndexes[1]]['param_id'])
            ? $requestParams[$attributeIndexes[1]]['param_id'] : 0;
    }
}
