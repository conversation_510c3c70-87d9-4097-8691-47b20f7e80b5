<?php

namespace App\Application\Http\Requests\Api\RawMedia;

use App\Domain\Project\RawMedia\RelationType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RawMediaDestroyRequest extends FormRequest
{
    public static function getRules(): array
    {
        return [
            'project_id' => ['required', 'exists:projects,id'],
            'relation_type_key' => [
                'nullable',
                Rule::in(RelationType::getAllKeys()),
            ],
        ];
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return static::getRules();
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'relation_type_key' => $this->route('relationTypeKey'),
        ]);
    }
}
