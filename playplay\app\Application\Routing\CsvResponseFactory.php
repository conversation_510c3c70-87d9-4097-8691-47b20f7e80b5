<?php

declare(strict_types=1);

namespace App\Application\Routing;

use App\Domain\Export\CsvSerializable;
use Illuminate\Http\Response;
use Illuminate\Routing\ResponseFactory as BaseResponseFactory;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

final class CsvResponseFactory extends BaseResponseFactory
{
    /**
     * @param Collection|CsvSerializable[] $data
     */
    public function csv(Collection $data): Response
    {
        if ($data->isEmpty()) {
            return $this->make('No Content', SymfonyResponse::HTTP_NO_CONTENT);
        }

        return $this->make(
            $this->formatCsv($data),
            SymfonyResponse::HTTP_OK,
            $this->createCsvHeaders()
        );
    }

    /**
     * @param Collection|CsvSerializable[] $data
     */
    private function addHeaderToCsvArray(Collection $data): string
    {
        $firstRowData = $data[0]->csvSerialize();
        $rowData = array_keys($firstRowData);

        return $this->rowDataToCsvString($rowData);
    }

    /**
     * @param Collection|CsvSerializable[] $data
     */
    private function addRowsToCsvArray(Collection $data): array
    {
        $csvArray = [];
        foreach ($data as $row) {
            $csvArray[] = $this->rowDataToCsvString($row->csvSerialize());
        }

        return $csvArray;
    }

    private function createCsvHeaders(): array
    {
        return [
            'Content-Disposition' => 'attachment; filename="export.csv"',
            'Content-Type' => 'text/csv; charset=WINDOWS-1252',
            'Content-Encoding' => 'WINDOWS-1252',
            'Content-Transfer-Encoding' => 'binary',
            'Content-Description' => 'File Transfer',
        ];
    }

    /**
     * @param Collection|CsvSerializable[] $data
     */
    private function formatCsv(Collection $data): string
    {
        $csvArray = array_merge(
            [$this->addHeaderToCsvArray($data)],
            $this->addRowsToCsvArray($data)
        );

        $csv = implode("\r\n", $csvArray);

        return mb_convert_encoding($csv, 'WINDOWS-1252');
    }

    private function rowDataToCsvString(array $row): string
    {
        array_walk($row, function (&$cell) {
            // todo : clean this mess
            if (is_array($cell) || is_string($cell)) {
                $cell = '"' . str_replace('"', '""', $cell) . '"';
            }
        });

        return implode(';', $row);
    }
}
