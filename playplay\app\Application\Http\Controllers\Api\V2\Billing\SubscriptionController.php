<?php

namespace App\Application\Http\Controllers\Api\V2\Billing;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Billing\SubscriptionStoreRequest;
use App\Domain\Billing\BillingException;
use App\Domain\Billing\IncompleteSubscriptionException;
use App\Infrastructure\Billing\StripeService;
use App\Infrastructure\Billing\StripeSubscriptionSerializer;
use App\Infrastructure\Billing\StripeSubscriptionService;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Psr\Log\LoggerInterface;
use Stripe\Exception\ApiErrorException;
use Stripe\Exception\CardException;
use Stripe\Exception\InvalidRequestException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class SubscriptionController extends BaseController
{
    use AuthorizesRequests;

    private Guard $auth;
    private StripeSubscriptionService $stripeSubscriptionService;
    private StripeService $stripeService;
    private StripeSubscriptionSerializer $stripeSubscriptionSerializer;
    private LoggerInterface $logger;

    public function __construct(
        StripeService $stripeService,
        StripeSubscriptionService $stripeSubscriptionService,
        StripeSubscriptionSerializer $stripeSubscriptionSerializer,
        Guard $auth,
        LoggerInterface $logger
    ) {
        $this->stripeService = $stripeService;
        $this->stripeSubscriptionSerializer = $stripeSubscriptionSerializer;
        $this->stripeSubscriptionService = $stripeSubscriptionService;
        $this->auth = $auth;
        $this->logger = $logger;
    }

    /** Used for 3D Secure payments */
    public function confirm(string $stripeSubscriptionId): JsonResponse
    {
        /** @var User $authenticatedUser */
        $authenticatedUser = $this->auth->user();

        try {
            $stripeSubscription = $this->stripeService->getSubscription($stripeSubscriptionId);
        } catch (InvalidRequestException) {
            throw new NotFoundHttpException('Subscription not found');
        } catch (ApiErrorException $e) {
            throw new BillingException($e->getMessage(), Response::HTTP_PAYMENT_REQUIRED);
        }

        $billingPlan = $this->stripeService->getBillingPlanFromSubscription($stripeSubscription);
        if ($billingPlan === null) {
            throw new NotFoundHttpException('BillingPlan not found');
        }

        try {
            $this->stripeSubscriptionService->validateSubscription(
                $authenticatedUser,
                $billingPlan,
                $stripeSubscription
            );
        } catch (IncompleteSubscriptionException $e) {
            throw new HttpException(
                Response::HTTP_PAYMENT_REQUIRED,
                $e->getMessage()
            );
        }

        return $this->sendJsonResponse(new Collection([]), Response::HTTP_CREATED);
    }

    /**
     * @throws AuthorizationException
     * @throws BillingException
     */
    public function store(SubscriptionStoreRequest $request): JsonResponse
    {
        $this->authorize('create', Subscription::class);

        try {
            /** @var User $authenticatedUser */
            $authenticatedUser = $this->auth->user();
            $this->stripeSubscriptionService->createOrUpdateCustomer(
                $authenticatedUser,
                $authenticatedUser->company,
                $request->input('stripe_token')
            );

            $stripeSubscription = $this->stripeSubscriptionService->createSubscription(
                $authenticatedUser,
                $request->input('billing_plan_id'),
                $request->input('country')
            );
        } catch (CardException $exception) {
            $this->logger->error('[STRIPE ERROR] Payment failed', [
                'message_exception' => $exception->getMessage(),
                'code' => $exception->getStripeCode(),
                'decline_code' => $exception->getDeclineCode(),
                'subscription_id' => $request->input('billing_plan_id'),
            ]);
            throw new BillingException($exception->getHttpBody(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (ApiErrorException $exception) {
            throw new BillingException($exception->getHttpBody(), Response::HTTP_PAYMENT_REQUIRED);
        } catch (IncompleteSubscriptionException $e) {
            throw new HttpException(
                Response::HTTP_PAYMENT_REQUIRED,
                $e->getMessage()
            );
        }

        return $this->sendJsonResponse(
            new Collection([$this->stripeSubscriptionSerializer->serialize($stripeSubscription)]),
            Response::HTTP_CREATED
        );
    }
}
