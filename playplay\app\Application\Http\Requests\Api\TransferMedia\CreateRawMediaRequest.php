<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\TransferMedia;

use App\Domain\RawMedia\RawMediaSource;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateRawMediaRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'uuid' => ['required', 'uuid', 'exists:projects,uuid,deleted_at,NULL'],
            'name' => ['required', 'string'],
            'source' => [
                'required',
                'string',
                Rule::in([RawMediaSource::TRANSFER_MEDIA_FROM_QR_CODE, RawMediaSource::TRANSFER_MEDIA_FROM_LINK]),
            ],
            'content_type' => ['required', 'string'],
            'hash' => ['required', 'string'],
            'cdn_url' => ['required', 'url', 'string'],
        ];
    }
}
