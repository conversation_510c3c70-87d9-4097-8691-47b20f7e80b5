<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Font;

use App\Application\Http\Controllers\Api\V3\Font\GetBasicFontsController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class FontRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'as' => 'fonts.',
            'prefix' => '/fonts'
        ], static function (Router $router) {
            $router->get('', GetBasicFontsController::class)->name('index');
        });
    }
}
