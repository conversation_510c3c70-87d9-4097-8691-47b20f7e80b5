<?php

declare(strict_types=1);

namespace App\Application\Console\Commands\Database;

use App\Domain\DataSeeding\DataSeedingException;
use App\Infrastructure\DataSeeding\DatabaseSeedsCleaner;
use Illuminate\Console\Command;
use Illuminate\Foundation\Application;
use Psr\Log\LoggerInterface;

final class CleanDatabaseSeeds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:clean:seeds';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to clean old database seeds';

    private Application $app;
    private LoggerInterface $logger;
    private DatabaseSeedsCleaner $databaseSeedsCleaner;

    public function __construct(
        Application $app,
        LoggerInterface $logger,
        DatabaseSeedsCleaner $databaseSeedsCleaner,
    ) {
        parent::__construct();

        $this->app = $app;
        $this->logger = $logger;
        $this->databaseSeedsCleaner = $databaseSeedsCleaner;
    }

    public function handle(): int
    {
        if ($this->app->environment() === 'production') {
            return 1;
        }

        try {
            $this->databaseSeedsCleaner->clean();
        } catch (DataSeedingException $ex) {
            $this->error('Cleanup failed! Something went wrong: ');
            $this->error($ex->getMessage());
            $this->logger->warning('The database seeds cleanup failed. The error was: ' . $ex->getMessage());

            return 1;
        }

        $this->info('The database seeds cleanup job succeeded!');

        return 0;
    }
}
