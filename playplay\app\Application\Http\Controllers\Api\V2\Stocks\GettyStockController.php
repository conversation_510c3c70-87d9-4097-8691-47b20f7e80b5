<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Stocks;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Stock\Getty\GettyStockImagesRequest;
use App\Application\Http\Requests\Api\Stock\Getty\GettyStockVideosRequest;
use App\Models\RawMedia;
use App\Services\Stock\AStock;
use App\Services\Stock\Getty\GettyImageStock;
use App\Services\Stock\Getty\GettyVideoStock;
use App\Services\Stock\Local\RawMediaStock;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

class GettyStockController extends BaseController
{
    use AuthorizesRequests;

    private GettyImageStock $gettyImageStockService;
    private GettyVideoStock $gettyVideoStockService;
    private RawMediaStock $rawMediaStock;

    public function __construct(
        GettyImageStock $gettyImageStockService,
        GettyVideoStock $gettyVideoStockService,
        RawMediaStock $rawMediaStock
    ) {
        $this->gettyImageStockService = $gettyImageStockService;
        $this->gettyVideoStockService = $gettyVideoStockService;
        $this->rawMediaStock = $rawMediaStock;
    }

    public function images(Guard $auth, GettyStockImagesRequest $request): JsonResponse
    {
        $this->authorize('viewGetty', [$auth->user(), $request->get('is_edito', false)]);
        $result = $this->handleByType('image', $request);

        return $this->sendJsonResponse(new Collection([$result]), Response::HTTP_OK);
    }

    public function videos(Guard $auth, GettyStockVideosRequest $request): JsonResponse
    {
        $this->authorize('viewGetty', [$auth->user(), $request->get('is_edito', false)]);
        $result = $this->handleByType('video', $request);

        return $this->sendJsonResponse(new Collection([$result]), Response::HTTP_OK);
    }

    private function handleByType(string $type, FormRequest $request): array
    {
        $page = (int) $request->get('page', 1);
        $perPage = (int) $request->get('per_page', AStock::SEARCH_PER_PAGE);
        $isEdito = (bool) $request->get('is_edito', false);
        $query = (string) $request->get('query', '');
        $keywordIds = $request->get('keyword_ids', '');
        $gettyService = $type === 'video' ? $this->gettyVideoStockService : $this->gettyImageStockService;

        $filters = array_filter($request->only($gettyService->getAdditionalFilters()));
        $filters['is_edito'] = $isEdito;
        $filters['keyword'] = $query;
        $filters['keyword_ids'] = $keywordIds;

        if ($query !== '') {
            return $gettyService->search($page, $perPage, $filters);
        }

        $recentlyUsed = $this->rawMediaStock->load($type, $page, RawMediaStock::ITEM_PER_PAGE, $next);
        if ($recentlyUsed->isNotEmpty()) {
            return [
                'type' => 'recent',
                'results' => $recentlyUsed->map(function (RawMedia $media) {
                    return $media->toVue();
                }),
                'next_page' => $next,
                'nb_results' => count($recentlyUsed),
            ];
        }

        return $gettyService->popular($page, $perPage, ['is_edito' => $isEdito]);
    }
}
