<?php

declare(strict_types=1);

namespace App\Application\Policies;

use App\Models\Project;
use App\Models\RawMedia;
use App\Models\User;

final class CutawayShotPolicy extends DefaultPolicy
{
    public const RESOURCE_NAME = 'CutawayShot';

    private ProcessedMediaPolicy $processedMediaPolicy;
    private ProjectPolicy $projectPolicy;

    public function __construct(ProcessedMediaPolicy $processedMediaPolicy, ProjectPolicy $projectPolicy)
    {
        $this->processedMediaPolicy = $processedMediaPolicy;
        $this->projectPolicy = $projectPolicy;
    }

    public function before($user, $ability): ?bool
    {
        return $user->is_admin ?: null;
    }

    public function create(User $user, RawMedia $rawMedia, Project $project, string $source): bool
    {
        return $this->processedMediaPolicy->create($user, $rawMedia, $project, $source);
    }

    public function view(User $user, Project $project): bool
    {
        return $this->projectPolicy->view($user, $project);
    }

    public function update(User $user, Project $project): bool
    {
        return $this->projectPolicy->view($user, $project);
    }
}
