<?php

declare(strict_types=1);

namespace App\Application\Http\Middleware;

use App\Domain\TwoFactorAuth\CodeValidator;
use App\Domain\User\UserRepository;
use App\Infrastructure\TwoFactorAuth\CookieService;
use Closure;
use Illuminate\Http\Request;

final class UnregisterDevice
{
    private CookieService $cookieService;
    private UserRepository $userRepository;
    private CodeValidator $codeValidator;

    public function __construct(
        CookieService $cookieService,
        CodeValidator $codeValidator,
        UserRepository $userRepository
    ) {
        $this->cookieService = $cookieService;
        $this->codeValidator = $codeValidator;
        $this->userRepository = $userRepository;
    }

    public function handle(Request $request, Closure $next)
    {
        if (!$request->hasCookie('device_token')) {
            return $next($request);
        }

        $email = $request->input('email');

        if ($email !== null) {
            $user = $this->userRepository->getUserFromEmail($email);

            if ($user !== null
                && !$this->codeValidator->isLatestCodeForUser($user, $request->cookie('device_token'))
            ) {
                $this->cookieService->clearCookie($request);
            }
        }

        return $next($request);
    }
}
