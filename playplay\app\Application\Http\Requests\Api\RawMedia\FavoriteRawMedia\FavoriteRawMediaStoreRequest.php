<?php

namespace App\Application\Http\Requests\Api\RawMedia\FavoriteRawMedia;

use Illuminate\Foundation\Http\FormRequest;

class FavoriteRawMediaStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function getRules()
    {
        return [
            'raw_media_id' => ['required', 'exists:raw_medias,id'],
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return $this->getRules();
    }
}
