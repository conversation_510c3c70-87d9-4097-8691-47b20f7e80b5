<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Admin;

use App\Application\Http\Controllers\Admin\RoleController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class RoleRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/roles',
            'as' => 'roles.'
        ], static function (Router $router) {
            $router->get('/', [RoleController::class, 'index'])->name('index');
            $router->post('/', [RoleController::class, 'store'])->name('store');
            $router->get('/create', [RoleController::class, 'create'])->name('create');
            $router->get('/{role}', [RoleController::class, 'show'])->name('show');
            $router->put('/{role}', [RoleController::class, 'update'])->name('update');
            $router->delete('/{role}', [RoleController::class, 'destroy'])->name('destroy');
            $router->get('/{role}/edit', [RoleController::class, 'edit'])->name('edit');
        });
    }
}
