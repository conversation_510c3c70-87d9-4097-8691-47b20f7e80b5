<?php

declare(strict_types=1);

namespace App\Domain\Music;

use App\Models\Music\MusicList;

interface MusicListRepository
{
    public function findOneById(int $musicListId): ?MusicList;

    public function getOrderedMusicLists(): array;

    public function isRawMediaInMusicLists(int $rawMediaId, array $musicListIds): bool;

    public function deleteMusic(MusicList $musicList, int $musicId): void;
}
