<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Permission;

use App\Application\Http\Controllers\Api\Permission\GetAppRolesController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class PermissionRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => 'app-roles/',
            'as' => 'app-roles.',
        ], static function (Router $router) {
            $router->get('/', GetAppRolesController::class)->name('get-all');
        });
    }
}
