<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Team;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Folder\FolderRequest;
use App\Application\Http\Requests\Api\Folder\ProjectMoveFolderRequest;
use App\Domain\User\Action\UserAction;
use App\Domain\User\Action\UserActionService;
use App\Models\Folder;
use App\Models\Team;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

final class TeamFolderController extends BaseController
{
    use AuthorizesRequests;

    private UserActionService $userActionService;

    public function __construct(UserActionService $userActionService)
    {
        $this->userActionService = $userActionService;
    }

    /**
     * @throws AuthorizationException
     */
    public function destroy(Team $team, Folder $folder): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('destroy', [Folder::class, $team, $folder]);

        $folder->delete();

        return $this->sendJsonResponse(new Collection(), Response::HTTP_NO_CONTENT);
    }

    /**
     * @throws AuthorizationException
     */
    public function index(Team $team): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('index', [Folder::class, $team]);

        $folders = $team->folders()->orderBy('title')->get();

        $this->userActionService->addUserAction(
            new UserAction(
                'my-playplays-page-visited',
                [],
                $team->id
            )
        );

        return $this->sendJsonResponse($folders, Response::HTTP_OK);
    }

    /**
     * @throws AuthorizationException
     */
    public function move(Team $team, ProjectMoveFolderRequest $request): JsonResponse
    {
        $folderId = $request->input('folder_id');
        $projectIds = $request->input('project_ids');

        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('move', [Folder::class, $team, $folderId]);

        DB::table('projects')->where('team_id', $team->id)
            ->whereIn('id', $projectIds)
            ->update([
                'folder_id' => $folderId,
            ]);

        return $this->sendJsonResponse(new Collection(), Response::HTTP_OK);
    }

    /**
     * @throws AuthorizationException
     */
    public function store(Team $team, FolderRequest $request, Guard $auth): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('create', [Folder::class, $team]);

        $title = $request->input('title');

        if ($team->folders->where('title', $title)->isNotEmpty()) {
            throw ValidationException::withMessages(['Title already exists for the specified team']);
        }

        $folder = $team->folders()->create([
            'title' => $title,
            'user_id' => $auth->id(),
        ]);

        return $this->sendJsonResponse(new Collection([$folder]), Response::HTTP_CREATED);
    }

    /**
     * @throws AuthorizationException
     */
    public function update(Team $team, Folder $folder, FolderRequest $request): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);
        $this->authorize('update', [Folder::class, $team, $folder]);

        $title = $request->input('title');

        if ($team->folders->where('title', $title)->isNotEmpty()) {
            throw ValidationException::withMessages(['Title already exists for the specified team']);
        }

        $folder->update([
            'title' => $title,
        ]);

        return $this->sendJsonResponse(new Collection([$folder]), Response::HTTP_OK);
    }
}
