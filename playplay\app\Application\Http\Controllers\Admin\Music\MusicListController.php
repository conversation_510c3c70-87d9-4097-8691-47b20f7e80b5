<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Music;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\Music\MusicListRequest;
use App\Models\Music\MusicList;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

final class MusicListController extends BaseController
{
    private Factory $viewFactory;

    public function __construct(Factory $viewFactory)
    {
        $this->authorizeResource(MusicList::class, MusicList::class);

        $this->viewFactory = $viewFactory;
    }

    public function create(): View
    {
        return $this->viewFactory->make(
            'admin.music-lists.create',
            ['musicList' => new MusicList()]
        );
    }

    public function destroy(MusicList $musicList)
    {
        return $this->ajaxDelete($musicList);
    }

    public function edit(MusicList $musicList): View
    {
        return view(
            'admin.music-lists.edit',
            ['musicList' => $musicList]
        );
    }

    public function index(Request $request): View
    {
        $search = $request->get('search');

        // TODO create a dedicated repository
        $musicLists = MusicList::orderBy('title')
            ->when($search !== null, function (QueryBuilder $qb) use ($search) {
                return $qb->where('title', 'LIKE', "%{$search}%");
            })
            ->simplePaginate($request->get('per_page', 50))
            ->appends($request->all());

        return view('admin.music-lists.index', [
            'musicLists' => $musicLists,
        ]);
    }

    public function reorderOptions(MusicList $musicList, Request $request): void
    {
        foreach ($request->get('options_ids', []) as $order => $id) {
            $musicList->musics()
                ->where('id', '=', $id)
                ->update(['order' => $order]);
        }
    }

    public function store(MusicListRequest $request): RedirectResponse
    {
        $optionList = MusicList::create([
            'title' => $request->get('title'),
        ]);

        return redirect()->route('admin.music-lists.edit', $optionList);
    }

    public function update(MusicList $musicList, MusicListRequest $request): RedirectResponse
    {
        $musicList->update([
            'title' => $request->get('title'),
        ]);

        return redirect()->back();
    }
}
