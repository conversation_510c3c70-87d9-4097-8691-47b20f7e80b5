<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\ProjectScreen;

use App\Application\Http\Controllers\Api\V2\ProjectScreen\GetScreenFamilyListController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class ScreenFamilyRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::get('screenFamily', GetScreenFamilyListController::class)
            ->name('screenFamily.index');
    }
}
