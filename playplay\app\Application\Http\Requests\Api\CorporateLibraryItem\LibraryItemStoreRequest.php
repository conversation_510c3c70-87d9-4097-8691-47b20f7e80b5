<?php

namespace App\Application\Http\Requests\Api\CorporateLibraryItem;

use Illuminate\Foundation\Http\FormRequest;

class LibraryItemStoreRequest extends FormRequest
{
    public static function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'is_folder' => ['required', 'boolean'],
            'raw_media_id' => ['sometimes', 'nullable', 'exists:raw_medias,id'],
            'folder_id' => ['sometimes', 'nullable', 'exists:corporate_library_items,id'],
        ];
    }

    public function authorize(): bool
    {
        return true;
    }
}
