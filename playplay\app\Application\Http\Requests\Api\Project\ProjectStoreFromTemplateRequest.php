<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\Project;

use App\Domain\Template\Repositories\TeamTemplateRepository;
use App\Models\Template;
use App\Models\User;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Request;
use Illuminate\Validation\Factory;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

final class ProjectStoreFromTemplateRequest extends AbstractProjectStoreFromRequest
{

    private Guard $guard;
    private TeamTemplateRepository $teamTemplateRepository;

    public function __construct(Guard $guard, TeamTemplateRepository $teamTemplateRepository)
    {
        parent::__construct();
        $this->registerRuleIsTemplateAvailableForUser($this->validationFactory);
        $this->guard = $guard;
        $this->teamTemplateRepository = $teamTemplateRepository;
    }

    protected function registerRuleIsTemplateAvailableForUser(Factory $validationFactory)
    {
        $validationFactory->extendImplicit(
            'teamTemplateAvailableForUser',
            function ($attribute, $value) {
                return $this->isTeamTemplateAvailableForUser($value);
            }
        );
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'template_id' => ['required', 'int', 'exists:templates,id,deleted_at,NULL', 'teamTemplateAvailableForUser'],
            'preset_id' => ['required', 'presetAvailableForUser'],
            'format' => ['required', 'formatAvailableForUser', 'isValidFormat'],
        ];
    }

    protected function isFormatAvailableForUser(string $format): bool
    {
        /** @var Request $request */
        $request = request();
        /** @var ?Template $template */
        $template = Template::query()->find($request->get('template_id'));
        if ($template === null) {
            return false;
        }

        return $template->isFormatAvailable($format);
    }

    /**
     * This overrides the response returned by the validator
     * The domain logic may be too linked with the application.
     * TODO : Remove the template id and format available check and pass it on service
     *
     * @throws ValidationException
     */
    protected function failedValidation(Validator $validator)
    {
        if ($validator->errors()->has('template_id')) {
            throw new NotFoundHttpException('Template not found');
        }

        parent::failedValidation($validator);
    }

    private function isTeamTemplateAvailableForUser(int $templateId): bool
    {
        /** @var ?Template $template */
        $template = Template::query()->find($templateId);
        if ($template?->is_team_template) {
            /** @var User $user */
            $user = $this->guard->user();
            return $this->teamTemplateRepository->isTeamTemplateAvailableForUser($templateId, $user->id);
        }

        return true;
    }
}
