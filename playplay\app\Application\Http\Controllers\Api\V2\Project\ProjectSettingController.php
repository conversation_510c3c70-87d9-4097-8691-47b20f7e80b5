<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Project;

use App\Application\Http\Controllers\Api\BaseController;
use App\Models\Project;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class ProjectSettingController extends BaseController
{
    use AuthorizesRequests;

    public function index(Project $project): JsonResponse
    {
        $this->authorize('view', $project);

        return $this->sendJsonResponse(
            new Collection([
                $this->formatToProjectSetting($project),
            ]),
            Response::HTTP_OK
        );
    }

    private function formatToProjectSetting(Project $project): array
    {
        return [
            'screen_duration' => $project->screen_duration,
            'loop_music' => $project->loop_music,
            'music_level' => $project->getMusicLevel(),
            'media_level' => $project->getMediaLevel(),
            'voiceover_level' => $project->getVoiceoverLevel(),
        ];
    }
}
