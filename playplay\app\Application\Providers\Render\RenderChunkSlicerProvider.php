<?php

declare(strict_types=1);

namespace App\Application\Providers\Render;

use App\Domain\Render\LegacyRenderChunksSlicer;
use App\Domain\Render\LegacyRenderChunksSlicerInterface;
use App\Domain\Render\RenderChunkSlicer;
use App\Domain\Render\RenderChunkSlicerInterface;
use Illuminate\Config\Repository as ConfigRepository;
use Illuminate\Foundation\Application;
use Illuminate\Support\ServiceProvider;

final class RenderChunkSlicerProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->bind(RenderChunkSlicerInterface::class, RenderChunkSlicer::class);
        $this->app->bind(LegacyRenderChunksSlicerInterface::class, LegacyRenderChunksSlicer::class);
        $this->app->bind(RenderChunkSlicer::class, function (Application $app) {
            $config = $app->make(ConfigRepository::class);

            return new RenderChunkSlicer(
                (int) $config->get('app.render_chunk.frame_count'),
                (int) $config->get('app.render_chunk.min_frame_count_threshold')
            );
        });
    }
}
