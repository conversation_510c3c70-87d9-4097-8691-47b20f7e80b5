<?php

declare(strict_types=1);

namespace App\Domain\Project\Duplicator;

use App\Models\Project;

final class ProjectVoiceoversDuplicator
{
    private ProjectAudioProcessedMediasDuplicator $projectAudioProcessedMediasDuplicator;

    public function __construct(ProjectAudioProcessedMediasDuplicator $projectAudioProcessedMediasDuplicator)
    {
        $this->projectAudioProcessedMediasDuplicator = $projectAudioProcessedMediasDuplicator;
    }

    public function duplicate(Project $sourceProject, Project $destinationProject): void
    {
        $sourceProject->load('voiceovers.rawMedia.lastRender');
        $this->projectAudioProcessedMediasDuplicator->duplicate(
            $destinationProject,
            $sourceProject->voiceovers,
        );
    }
}
