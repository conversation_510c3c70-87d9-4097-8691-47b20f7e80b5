<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Admin;

use App\Application\Http\Controllers\Admin\RenderProjectHtml\IndexRenderProjectHtmlController;
use App\Application\Http\Controllers\Admin\RenderProjectHtml\ShowRenderProjectHtmlController;
use App\Application\Http\Controllers\Admin\RenderProjectHtml\ShowTimelineController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class RenderProjectHtmlRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/renderProjectsHtml',
            'as' => 'renderProjectsHtml.'
        ], static function (Router $router) {
            $router->get('/', IndexRenderProjectHtmlController::class)->name('index');
            $router->get('/{render_project_html}', ShowRenderProjectHtmlController::class)->name('show');
            $router->get('/{renderProjectHtml}/timeline', ShowTimelineController::class)->name('showTimeline');
        });
    }
}
