<?php

declare(strict_types=1);

namespace App\Application\Listeners;

use App\Application\Events\ProjectScreenUpdated;
use App\Application\Events\ProjectSettingsUpdated;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Events\Dispatcher;

final class UpdateProjectScreenRender
{
    public function onProjectScreenUpdate(ProjectScreenUpdated $event): void
    {
        $projectScreen = $event->getProjectScreen();
        $shouldRefreshPreview = $event->getShouldRefreshPreview();

        if ($projectScreen) {
            $data = ['render_screen_up_to_date' => false];
            if ($shouldRefreshPreview) {
                $data['render_screen_preview_up_to_date'] = false;
            }

            $projectScreen->update($data);
        }
    }

    public function onProjectUpdate(ProjectSettingsUpdated $event): void
    {
        $project = $event->getProject();
        $projectScreensToUpdate = $project->projectScreens();

        $settings = $event->getSettings();
        foreach ($settings as $type => $values) {
            foreach ($values as $setting) {
                if ($setting === 'media_level') {
                    $projectScreensToUpdate->hasVideo($project->id)
                        ->whereRaw("JSON_EXTRACT({$type}, '$.{$setting}') = CAST('null' AS JSON)");
                } else {
                    $projectScreensToUpdate->whereRaw(
                        "JSON_EXTRACT({$type}, '$.{$setting}') = CAST('null' AS JSON)"
                    );
                }
            }
        }

        $this->updateProjectScreens(
            $projectScreensToUpdate,
            // no need to update previews if only music_level is updated
            $settings !== ['settings' => ['music_level']]
        );
    }

    public function subscribe(Dispatcher $dispatcher): void
    {
        $dispatcher->listen(
            ProjectScreenUpdated::class,
            __CLASS__ . '@onProjectScreenUpdate'
        );

        $dispatcher->listen(
            ProjectSettingsUpdated::class,
            __CLASS__ . '@onProjectUpdate'
        );
    }

    private function updateProjectScreens(Relation $projectScreens, bool $shouldUpdatePreview = true): void
    {
        $dataToUpdate = [
            'project_screens.render_screen_up_to_date' => false,
            'project_screens.updated_at' => now(),
        ];

        if ($shouldUpdatePreview) {
            $dataToUpdate['project_screens.render_screen_preview_up_to_date'] = false;
        }

        $projectScreens->toBase()->update($dataToUpdate);
    }
}
