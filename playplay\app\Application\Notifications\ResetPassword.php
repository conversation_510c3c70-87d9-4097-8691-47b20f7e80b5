<?php

declare(strict_types=1);

namespace App\Application\Notifications;

use App\Models\User;
use Illuminate\Auth\Notifications\ResetPassword as BaseResetPassword;
use Illuminate\Notifications\Messages\MailMessage;

final class ResetPassword extends BaseResetPassword
{
    private User $user;

    public function __construct(string $token, User $user)
    {
        parent::__construct($token);
        $this->user = $user;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function toMail($notifiable): MailMessage
    {
        $email = http_build_query(['email' => $this->user->email]);
        $url = url("/app/password/reset/{$this->token}?{$email}");

        return (new MailMessage())
            ->subject(__('email.reset_password.subject'))
            ->markdown('emails.passwords.reset', [
                'url' => $url,
                'name' => $this->user->first_name,
            ]);
    }
}
