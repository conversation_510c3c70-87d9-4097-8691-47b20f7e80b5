<?php

namespace App\Domain\FavoriteRawMedia;

use App\Models\RawMedia;

class FavoriteRawMediaService
{
    private FavoriteRawMediaSerializer $favoriteRawMediaSerializer;
    private FavoriteRawMediaRepository $favoriteRawMediaRepository;

    public function __construct(
        FavoriteRawMediaSerializer $favoriteRawMediaSerializer,
        FavoriteRawMediaRepository $favoriteRawMediaRepository
    ) {
        $this->favoriteRawMediaSerializer = $favoriteRawMediaSerializer;
        $this->favoriteRawMediaRepository = $favoriteRawMediaRepository;
    }

    public function retrieveFavorites(int $userId, int $page): array
    {
        $favorites = $this->favoriteRawMediaRepository
            ->getPaginatedAllByUserId($userId, $page);
        $favoriteCount = $favorites->count();
        $toVueFavorites = $favorites->map(function (RawMedia $rawMedia) {
            return $this->favoriteRawMediaSerializer->transform($rawMedia);
        });

        return [
            'results' => $toVueFavorites,
            'nb_results' => $favoriteCount,
            'next_page' => $this->getNextPage($favoriteCount, $page),
        ];
    }

    private function getNextPage(int $favoriteCount, int $page): ?int
    {
        return $favoriteCount === FavoriteRawMediaRepository::ITEMS_PER_PAGE ? $page + 1 : null;
    }
}
