<?php

namespace App\Application\Providers\Render;

use App\Domain\Render\RenderMedia\RenderMediaRepository;
use App\Infrastructure\Render\RenderMedia\EloquentRenderMediaRepository;
use Illuminate\Support\ServiceProvider;

class RenderMediaServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->bind(RenderMediaRepository::class, EloquentRenderMediaRepository::class);
    }
}
