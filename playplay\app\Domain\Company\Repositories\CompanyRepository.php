<?php

namespace App\Domain\Company\Repositories;

use App\Domain\Company\CompanyMetrics;
use App\Models\Company;
use DateTimeInterface;
use Illuminate\Support\Collection;

interface CompanyRepository
{
    public function update(
        Company $company,
        string $name,
        ?int $csmId,
        ?string $clientFrom,
        ?string $clientUntil,
        ?string $type,
        ?int $planId,
        bool $dataIsRestricted,
        string $status,
    ): void;

    public function getByTeamIds(array $teamIds): Collection;

    public function getByTeamId(int $teamId): ?Company;

    public function getCompanyMetrics(Company $company): CompanyMetrics;

    public function getById(int $companyId): Company;

    public function getManyByIds(array $companiesIds): Collection;

    public function updateCompanyFeatures(Company $company, array $featuresToUpdate): void;

    public function getCompanyByName(string $name): ?Company;

    public function getCompanyByTeamId(int $teamId): ?Company;

    public function getCompanyByPresetId(int $presetId): ?Company;

    public function updateClientUntilDate(Company $company, DateTimeInterface $subscriptionEnd): void;

    public function getActiveButterflyCompaniesBeingClientForThePast3Months(): Collection;

    public function updateTypeAsClient(array $companiesIds): void;
}
