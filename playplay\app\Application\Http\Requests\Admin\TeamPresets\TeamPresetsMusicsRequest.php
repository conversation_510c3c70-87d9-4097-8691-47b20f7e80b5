<?php

namespace App\Application\Http\Requests\Admin\TeamPresets;

use Illuminate\Foundation\Http\FormRequest;

class TeamPresetsMusicsRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'musics.canUpload' => ['required', 'boolean'],
            'optionListIds' => [
                'sometimes',
                'required',
            ],
            'optionListIds.*' => ['sometimes', 'exists:music_lists,id'],
        ];
    }
}
