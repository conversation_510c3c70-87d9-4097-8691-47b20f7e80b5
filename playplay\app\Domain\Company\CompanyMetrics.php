<?php

namespace App\Domain\Company;

use JsonSerializable;

final class CompanyMetrics implements JsonSerializable
{
    private int $monthlyDownloads;

    public function __construct(int $monthlyDownloads)
    {
        $this->monthlyDownloads = $monthlyDownloads;
    }

    public function jsonSerialize(): array
    {
        return [
            'monthly_downloads' => $this->monthlyDownloads
        ];
    }
}
