<?php

namespace App\Application\Policies;

use App\Models\TeamPreset;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class TeamPresetPolicy extends DefaultPolicy
{
    use HandlesAuthorization;

    public function before(User $authUser, $ability, $team = null)
    {
        if ($authUser->can('manage-team_presets')) {
            return true;
        }
    }

    public function update(User $user, TeamPreset $preset)
    {
        return $user->teams->pluck('id')->contains($preset->team_id);
    }
}
