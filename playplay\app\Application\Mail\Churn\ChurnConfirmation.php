<?php

declare(strict_types=1);

namespace App\Application\Mail\Churn;

use Carbon\Carbon;
use DateTimeInterface;
use Illuminate\Mail\Mailable;

final class ChurnConfirmation extends Mailable
{
    private string $userFullName;

    private string $userLanguage;

    private DateTimeInterface $subscriptionEndDate;

    private bool $hasCommitment;

    public function __construct(
        string $userFullName,
        string $userLanguage,
        DateTimeInterface $subscriptionEndDate,
        bool $hasCommitment
    ) {
        $this->userFullName = $userFullName;
        $this->userLanguage = $userLanguage;
        $this->subscriptionEndDate = $subscriptionEndDate;
        $this->hasCommitment = $hasCommitment;
    }

    public function build(): self
    {
        // TODO: remove carbon once we install the ext-intl PHP extension
        /** @var Carbon $carbonSubscriptionEndDate */
        $carbonSubscriptionEndDate = Carbon::createFromInterface($this->subscriptionEndDate);

        $subject = $this->hasCommitment
            ? __('email.churn_confirmation_commitment.subject')
            : __('email.churn_confirmation_nocommitment.subject');

        $mail = $this->hasCommitment
            ? 'emails.churn-confirmation-commitment'
            : 'emails.churn-confirmation-no-commitment';

        return $this->from('<EMAIL>')
            ->subject($subject)
            ->markdown($mail, [
                'name' => $this->userFullName,
                'has_commitment' => $this->hasCommitment,
                'day' => $this->getFormattedDayNumber($this->userLanguage, $carbonSubscriptionEndDate),
                'month' => $carbonSubscriptionEndDate->locale($this->userLanguage)->getTranslatedMonthName(),
                'year' => $carbonSubscriptionEndDate->format('Y'),
            ]);
    }

    private function getFormattedDayNumber(string $language, DateTimeInterface $date): string
    {
        if ($language === 'en') {
            return $date->format('jS');
        }

        return $date->format('j');
    }
}
