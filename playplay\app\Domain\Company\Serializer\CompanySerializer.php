<?php

namespace App\Domain\Company\Serializer;

use App\Domain\Subscription\Serializer\SubscriptionSerializer;
use App\Models\Company;

final class CompanySerializer
{
    private SubscriptionSerializer $subscriptionSerializer;

    public function __construct(SubscriptionSerializer $subscriptionSerializer)
    {
        $this->subscriptionSerializer = $subscriptionSerializer;
    }

    public function serialize(Company $company): array
    {
        $serializedSubscription = $company->getCurrentSubscription()
            ? $this->subscriptionSerializer->serialize($company->lastSubscription)
            : null;

        return [
            'is_connected_to_getty' => $company->is_connected_to_getty,
            'name' => $company->name,
            'sso_enabled' => $company->sso_enabled,
            'subscription' => $serializedSubscription,
        ];
    }
}
