<?php

namespace App\Application\Console\Commands\RawMedia;

use App\Domain\RawMedia\RawMediaRepository;
use Illuminate\Console\Command;

class CleanExpiredGettyRawMedia extends Command
{
    protected $signature = 'raw-media:clean-expired-getty';

    private RawMediaRepository $rawMediaRepository;

    public function __construct(RawMediaRepository $rawMediaRepository)
    {
        parent::__construct();
        $this->rawMediaRepository = $rawMediaRepository;
    }

    public function handle(): int
    {
        $expiredMedia = $this->rawMediaRepository->getExpiredGettyMedia();
        $nbUpdated = $this->rawMediaRepository->markAsErrored($expiredMedia->pluck('id')->toArray());

        if ($nbUpdated === 0) {
            $this->info("No expired getty media was cleaned up.");
        } else {
            $this->info("$nbUpdated expired getty medias " . (($nbUpdated === 1) ? 'was' : 'were') . " cleaned up.");
        }

        return 0;
    }
}
