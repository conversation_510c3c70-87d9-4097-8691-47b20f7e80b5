<?php

declare(strict_types=1);

namespace App\Application\Listeners;

use App\Application\Events\UserGenerateVideoOrRender;
use App\Application\Mail\FirstGeneratedProjectEmail;
use App\Domain\Config\ConfigRepository;
use App\Domain\User\UserRendersRepository;
use Illuminate\Contracts\Mail\Mailer;

final class SendFirstRenderEmail
{
    private ConfigRepository $configRepository;
    private Mailer $mailer;
    private UserRendersRepository $repository;

    public function __construct(
        ConfigRepository $configRepository,
        Mailer $mailer,
        UserRendersRepository $userRendersRepository
    ) {
        $this->configRepository = $configRepository;
        $this->mailer = $mailer;
        $this->repository = $userRendersRepository;
    }

    public function handle(UserGenerateVideoOrRender $event): void
    {
        $user = $event->getRenderProject()->project->user;

        if ($this->repository->isFirstGeneratedVideoOfUser($user)) {
            $emailsToNotify = $this->configRepository->getArray('first_generated_video_notification_emails');
            $this->mailer
                ->to($emailsToNotify)
                ->send(new FirstGeneratedProjectEmail($event->getRenderProject()));
        }
    }
}
