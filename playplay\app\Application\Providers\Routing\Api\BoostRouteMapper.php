<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\Cluster\BoostController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class BoostRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'as' => 'boost.',
        ], static function (Router $router) {
            $router->post('/boost', [BoostController::class, 'store'])->name('store');
        });
    }
}
