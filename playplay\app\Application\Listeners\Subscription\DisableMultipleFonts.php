<?php

declare(strict_types=1);

namespace App\Application\Listeners\Subscription;

use App\Application\Events\SubscriptionCreated;
use App\Domain\TeamPreset\TeamPresetRepository;

final class DisableMultipleFonts
{
    private TeamPresetRepository $teamPresetRepository;

    public function __construct(TeamPresetRepository $teamPresetRepository)
    {
        $this->teamPresetRepository = $teamPresetRepository;
    }

    public function handle(SubscriptionCreated $event): void
    {
        $subscription = $event->getSubscription();
        $plan = $subscription->company->plan;

        if (!$plan->getValueOfFeature('has_multiple_fonts')) {
            $this->teamPresetRepository->removeNonDefaultFonts($subscription->company);
        }
    }
}
