<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\Media;

use Illuminate\Foundation\Http\FormRequest;

final class MediaRenderJobCallbackRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'result' => ['required_without:error', 'array'],
            'error' => ['required_without:result', 'array'],
            'error.type' => ['required_with:error'],
            'error.message' => ['required_with:error', 'string'],
        ];
    }
}
