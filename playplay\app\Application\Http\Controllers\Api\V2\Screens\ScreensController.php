<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Screens;

use App\Application\Http\Controllers\Api\BaseController;
use App\Domain\Screen\ScreenService;
use App\Models\Project;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class ScreensController extends BaseController
{
    use AuthorizesRequests;

    private ScreenService $screenService;

    public function __construct(ScreenService $screenService)
    {
        $this->screenService = $screenService;
    }

    public function index(Project $project): JsonResponse
    {
        $this->authorize('view', $project);

        $response = $this->buildJsonResponse(
            new Collection([$this->screenService->getScreensByProject($project)])
        );

        return new JsonResponse($response, Response::HTTP_OK);
    }
}
