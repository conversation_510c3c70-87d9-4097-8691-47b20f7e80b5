<?php

namespace App\Application\Rules;

use App\Domain\CutawayShot\Validators\CutawayShotItemDurationRuleValidator;
use Illuminate\Contracts\Validation\Rule;

class CutawayShotItemDurationRule implements Rule
{
    private CutawayShotItemDurationRuleValidator $cutawayShotItemDurationRuleValidator;

    public function __construct(
        CutawayShotItemDurationRuleValidator $cutawayShotItemDurationRuleValidator
    ) {
        $this->cutawayShotItemDurationRuleValidator = $cutawayShotItemDurationRuleValidator;
    }

    public function message(): string
    {
        return trans('validation.cutaway_shot_item_duration');
    }

    public function passes($attribute, $value, $parameters = [], $validator = null): bool
    {
        return $this->cutawayShotItemDurationRuleValidator->isValid($validator->getData());
    }
}
