<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Template;

use App\Application\Http\Controllers\Api\BaseController;
use App\Domain\Template\Services\HomepageTemplateService;
use App\Models\Team;
use App\Models\User;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

final class GetHomepageTeamTemplatesController extends BaseController
{
    use AuthorizesRequests;

    private HomepageTemplateService $homepageTemplateService;
    private Guard $guard;

    public function __construct(
        HomepageTemplateService $homepageTemplateService,
        Guard $guard,
    ) {
        $this->homepageTemplateService = $homepageTemplateService;
        $this->guard = $guard;
    }

    public function __invoke(Team $team): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $team->company);

        /** @var User $user */
        $user = $this->guard->user();
        if (!$user->teams->contains($team)) {
            throw new NotFoundHttpException();
        }

        return $this->sendJsonResponse(
            $this->homepageTemplateService->getHomepageTemplates($team),
            Response::HTTP_OK
        );
    }
}
