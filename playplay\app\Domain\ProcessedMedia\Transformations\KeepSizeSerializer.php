<?php

declare(strict_types=1);

namespace App\Domain\ProcessedMedia\Transformations;

use App\Domain\Render\RenderMedia\Transformation\KeepSizeValue;
use App\Models\ProcessedMedia;

final class KeepSizeSerializer
{
    public function serialize(ProcessedMedia $processedMedia): ?string
    {
        $processedMediaData = $processedMedia->lastRender?->data;
        if ($processedMediaData === null) {
            return null;
        }

        if ($processedMediaData->getKeepSize() !== null) {
            return $processedMediaData->getKeepSize();
        }

        // Legacy keep size is only used for images with blur keep size
        if ($processedMediaData->getLegacyKeepSize() === true) {
            return KeepSizeValue::BLUR;
        }

        return null;
    }
}
