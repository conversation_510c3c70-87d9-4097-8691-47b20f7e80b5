<?php

declare(strict_types=1);

namespace App\Domain\Permissions;

use App\Domain\Common\Exceptions\EntityNotFoundException;
use App\Models\Permissions\AppRole;
use Illuminate\Support\Collection;

interface AppRoleRepository
{
    /** @throws EntityNotFoundException */
    public function getById(int $id): AppRole;

    /** @throws EntityNotFoundException */
    public function getByName(string $name): AppRole;

    public function getAllAvailableAppRolesWithAppPermissions(): Collection;

    public function getAllAvailableAppRolesHavingAppPermissions(string $appPermissionName): Collection;

    public function getAvailableAppRolesWithEnabledByDefaultAppPermissions(): Collection;
}
