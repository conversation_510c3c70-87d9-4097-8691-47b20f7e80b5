<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\Team\TimecodedElement;

use Illuminate\Foundation\Http\FormRequest;

final class ToggleTimecodedElementsFamilyAutoAddNewPresetsRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'is_enabled' => ['boolean', 'required'],
        ];
    }
}
