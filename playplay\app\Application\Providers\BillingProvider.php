<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Billing\BillingPlanRepository;
use App\Domain\Billing\ChargeRepository;
use App\Domain\Billing\CompanyRepository;
use App\Domain\Billing\InvoiceRepository;
use App\Infrastructure\Billing\PlanhatCompanyRepository;
use App\Infrastructure\Billing\StripeBillingPlanRepository;
use App\Infrastructure\Billing\StripeChargeRepository;
use App\Infrastructure\Billing\StripeInvoiceRepository;
use Illuminate\Support\ServiceProvider;

final class BillingProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(InvoiceRepository::class, StripeInvoiceRepository::class);
        $this->app->bind(ChargeRepository::class, StripeChargeRepository::class);
        $this->app->bind(BillingPlanRepository::class, StripeBillingPlanRepository::class);
        $this->app->bind(CompanyRepository::class, PlanhatCompanyRepository::class);
    }
}
