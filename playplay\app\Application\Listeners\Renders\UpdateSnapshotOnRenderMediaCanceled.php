<?php

declare(strict_types=1);

namespace App\Application\Listeners\Renders;

use App\Application\Events\Renders\RenderMediaCanceled;
use App\Domain\Preview\SnapshotStatus;

final class UpdateSnapshotOnRenderMediaCanceled
{
    public function handle(RenderMediaCanceled $event): void
    {
        $renderMedia = $event->getRenderMedia();
        $renderMedia->snapshots()->isNotFinished()->update(['status' => SnapshotStatus::CANCELED]);
    }
}
