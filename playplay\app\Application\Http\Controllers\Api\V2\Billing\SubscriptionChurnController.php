<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Billing;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Mail\Churn\ChurnConfirmation;
use App\Domain\Billing\BillingException;
use App\Domain\Billing\SubscriptionService;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Mail\Mailer;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

final class SubscriptionChurnController extends BaseController
{
    use AuthorizesRequests;

    private Guard $auth;

    private Mailer $mailer;

    private SubscriptionService $subscriptionService;

    public function __construct(Guard $auth, Mailer $mailer, SubscriptionService $subscriptionService)
    {
        $this->auth = $auth;
        $this->mailer = $mailer;
        $this->subscriptionService = $subscriptionService;
    }

    /**
     * @throws AuthorizationException|BillingException
     */
    public function __invoke(Subscription $subscription): JsonResponse
    {
        $this->authorize('churn', $subscription);

        /** @var User $user */
        $user = $this->auth->user();
        $this->subscriptionService->churn($subscription, $user->id);
        $this->mailer->to($user)->send(
            new ChurnConfirmation(
                $user->name,
                $user->language,
                $subscription->ended_at,
                $subscription->billingPlan->isAnnual()
            )
        );

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }
}
