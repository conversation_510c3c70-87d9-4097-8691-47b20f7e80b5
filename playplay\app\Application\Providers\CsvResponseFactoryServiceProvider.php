<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Application\Routing\CsvResponseFactory;
use Illuminate\Foundation\Application;
use Illuminate\Support\ServiceProvider;

final class CsvResponseFactoryServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->singleton('Illuminate\Contracts\Routing\ResponseFactory', function (Application $app) {
            return new CsvResponseFactory($app['Illuminate\Contracts\View\Factory'], $app['redirect']);
        });
    }
}
