<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Auth;

use App\Application\Http\Controllers\Api\V2\Auth\SsoCallbackLoginController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class SsoCallbackLoginRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::get('login-sso-callback', [SsoCallbackLoginController::class, 'callbackLogin'])
            ->name('login-sso-callback');
        Route::get('login-sso-mobile-token', [SsoCallbackLoginController::class, 'getLoginSsoMobileToken'])
            ->name('login-sso-mobile-token');
    }
}
