<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Option;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\Option\StoreOptionListCategoryRequest;
use App\Application\Http\Requests\Admin\Option\UpdateOptionListCategoryRequest;
use App\Domain\Screen\Parameters\OptionListCategoryRepository;
use App\Models\Screen\Parameters\OptionListCategory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

final class OptionListCategoryController extends BaseController
{
    private OptionListCategoryRepository $optionListCategoryRepository;

    public function __construct(OptionListCategoryRepository $optionListCategoryRepository)
    {
        $this->authorizeResource(OptionListCategory::class, OptionListCategory::class);
        $this->optionListCategoryRepository = $optionListCategoryRepository;
    }

    public function create(): View
    {
        return view(
            'admin.options.option-list-categories.create',
            ['optionListCategory' => new OptionListCategory()]
        );
    }

    public function edit(OptionListCategory $optionListCategory): View
    {
        return view('admin.options.option-list-categories.edit', ['optionListCategory' => $optionListCategory]);
    }

    public function index(): View
    {
        $optionListCategories = $this->optionListCategoryRepository->getAll();

        return view(
            'admin.options.option-list-categories.index',
            ['optionListCategories' => $optionListCategories]
        );
    }

    public function reorder(Request $request): void
    {
        $categoryIds = $request->get('category_ids', []);
        $this->optionListCategoryRepository->reorderByIds($categoryIds);
    }

    public function store(StoreOptionListCategoryRequest $request): RedirectResponse
    {
        OptionListCategory::create([
            'name' => $request->get('name'),
            'backoffice_name' => $request->get('backoffice_name'),
            'is_option_required' => (bool) $request->get('is_option_required'),
            'is_thumbnail_padded' => (bool) $request->get('is_thumbnail_padded'),
        ]);

        return redirect()->route('admin.option-list-categories.index');
    }

    public function update(
        OptionListCategory $optionListCategory,
        UpdateOptionListCategoryRequest $request
    ): RedirectResponse {
        $optionListCategory->update(
            $request->only(
                ['name', 'backoffice_name', 'is_option_required', 'is_thumbnail_padded']
            )
        );

        return redirect()->route('admin.option-list-categories.edit', $optionListCategory->id);
    }
}
