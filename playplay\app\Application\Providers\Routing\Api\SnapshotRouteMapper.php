<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\V2\Snapshot\SnapshotController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class SnapshotRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => '/snapshots',
            'as' => 'snapshots.',
        ], static function (Router $router) {
            $router->get('/{snapshot}', [SnapshotController::class, 'show'])->name('show');
            $router->post('', [SnapshotController::class, 'store'])->name('store');
        });
    }
}
