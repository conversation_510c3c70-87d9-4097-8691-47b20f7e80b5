<?php

declare(strict_types=1);

namespace App\Domain\Outro;

use App\Domain\RawMedia\RawMediaType;
use App\Models\Project;

class OutroSerializer
{
    private OutroService $outroService;

    public function __construct(OutroService $outroService)
    {
        $this->outroService = $outroService;
    }

    public function serialize(Project $project): array
    {
        $outros = $this->outroService->getOutrosFor($project);

        $hasMusicOnOutro = $project->hasMusicOnOutro();
        $musicLevel = $hasMusicOnOutro ? $project->getMusicLevel() : 0;
        $voiceoverLevel = $hasMusicOnOutro ? $project->getVoiceoverLevel() : 0;

        $outroScreens = [];
        foreach ($outros as $outro) {
            $outroScreens[] = $this->convertOutroToAScreen($outro, $musicLevel, $voiceoverLevel);
        }

        return $outroScreens;
    }

    private function convertOutroToAScreen(array $outro, int $musicLevel, int $voiceoverLevel): array
    {
        return [
            'type' => 'ProjectOutro',
            'params' => [
                'media' => [
                    'type' => RawMediaType::VIDEO,
                    'url' => $outro['url'],
                    'duration' => $outro['duration'],
                ],
            ],
            'options' => [
                'colors' => [
                    "main" => "rgb(0, 0, 0)",
                    "text" => "rgb(0, 0, 0)",
                    "word" => "rgb(0, 0, 0)",
                ],
                'logos' => [],
                'settings' => [
                    'show_logos' => false,
                    'music_level' => $musicLevel,
                    'media_level' => Project::DEFAULT_AUDIO_LEVEL,
                    'voiceover_level' => $voiceoverLevel,
                    'screen_duration' => 'regular',
                ],
            ],
        ];
    }
}
