<?php

declare(strict_types=1);

namespace App\Application\Http\Mappers\UserTeam;

use App\Application\Http\Requests\Admin\User\UserStoreRequest as AdminUserStoreRequest;
use App\Application\Http\Requests\Admin\User\UserTeamsUpdateRequest;
use App\Application\Http\Requests\Api\User\UserStoreRequest as ApiUserStoreRequest;
use App\Domain\Permissions\AppRoleRepository;
use App\Domain\UserTeam\TeamAppRoleDTO;
use App\Domain\UserTeam\TeamAppRoleDTOCollection;
use Illuminate\Support\Collection;

final class TeamAppRoleMapper
{
    private AppRoleRepository $appRoleRepository;

    public function __construct(AppRoleRepository $appRoleRepository)
    {
        $this->appRoleRepository = $appRoleRepository;
    }

    /**
     * @param AdminUserStoreRequest|ApiUserStoreRequest|UserTeamsUpdateRequest $formRequest
     *
     * @return Collection<TeamAppRoleDTO>
     */
    public function fromRequest(
        AdminUserStoreRequest|ApiUserStoreRequest|UserTeamsUpdateRequest $formRequest
    ): Collection {
        return new TeamAppRoleDTOCollection(
            array_filter(
                array_map(function (array $value) {
                    if (!isset($value['id'], $value['app_role_id'])) {
                        return null;
                    }

                    // TODO: remove when we use the new system of roles
                    $roleName = $this->appRoleRepository->getById((int) $value['app_role_id'])->name;

                    return new TeamAppRoleDTO((int) $value['id'], (int) $value['app_role_id'], $roleName);
                }, array_values($formRequest->get('teams', [])))
            )
        );
    }
}
