<?php

declare(strict_types=1);

namespace App\Application\Rules;

use App\Domain\TimecodedElement\Validators\TimecodedElementsFamilyTypeRuleValidator;
use Illuminate\Contracts\Validation\Rule;

final class IsValidTimecodedElementsFamilyTypeRule implements Rule
{
    private TimecodedElementsFamilyTypeRuleValidator $timecodedElementsFamilyTypeRuleValidator;

    public function __construct(TimecodedElementsFamilyTypeRuleValidator $timecodedElementsFamilyTypeRuleValidator)
    {
        $this->timecodedElementsFamilyTypeRuleValidator = $timecodedElementsFamilyTypeRuleValidator;
    }

    public function passes($attribute, $value, $parameters = [], $validator = null): bool
    {
        return $this->timecodedElementsFamilyTypeRuleValidator->isValid($value);
    }

    public function message(): string
    {
        return trans('validation.timecoded_element_family_type');
    }
}
