<?php

namespace App\Application\Console\Commands;

use App\Domain\Metrics\PublishMetricsService;
use Illuminate\Console\Command;

class PublishMetrics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'publish-metrics';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Push metrics to Stackdriver';

    private PublishMetricsService $service;

    public function __construct(PublishMetricsService $service)
    {
        parent::__construct();

        $this->service = $service;
    }

    public function handle(): int
    {
        $this->service->publish();

        return 0;
    }
}
