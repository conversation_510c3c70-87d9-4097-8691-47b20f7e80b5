<?php

namespace App\Application\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class MusicListPolicy extends DefaultPolicy
{
    use HandlesAuthorization;

    public function before(User $authUser, $ability, $param = null)
    {
        // TODO create a dedicated manage-music-lists
        if ($authUser->can('manage-option-lists')) {
            return true;
        }
    }
}
