<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Application\Html\AdminForm;
use App\Models\Company;
use App\Models\Team;
use App\Models\User;
use Collective\Html\FormBuilder;
use Collective\Html\HtmlBuilder;
use Illuminate\Support\Collection;
use Illuminate\Support\ServiceProvider;

final class AdminMacroServiceProvider extends ServiceProvider
{
    public function boot(HtmlBuilder $html, AdminForm $form): void
    {
        $this->bootUppyFileMacro($html);
        $this->bootSelectCompany($form);
        $this->bootSelectTeam($form);
        $this->bootSelectUser($form);
        $this->bootSelectTeamOnEditTeamPage($form);
    }

    private function bootSelectCompany(AdminForm $builder): void
    {
        FormBuilder::macro(
            'selectCompany',
            function (
                string $name,
                string|bool $label = null,
                array|Collection $list = [],
                string $selected = null,
                array $options = []
            ) use ($builder) {
                $requestCompanyId = $selected ?? request()->get('company_id');
                $class = data_get($options, 'class', '');
                $companiesOptions = [
                    'rel' => null,
                    'class' => "selectPaginated  {$class}",
                    'data-route' => route('admin.companies.filters'),
                    'data-id' => $requestCompanyId,
                    'data-text' => optional(Company::find($requestCompanyId))->name,
                ];
                $options = array_merge($options, $companiesOptions);

                return $builder->select($name, $label, $list, $selected, $options);
            }
        );
    }

    private function bootSelectTeam(AdminForm $builder): void
    {
        FormBuilder::macro(
            'selectTeam',
            function (
                string $name,
                string|bool $label = null,
                array|Collection $list = [],
                string $selected = null,
                array $options = []
            ) use ($builder) {
                $requestCompanyId = request()->get('company_id');
                $requestTeamId = $selected ?? request()->get('team_id');
                $class = data_get($options, 'class', '');
                $teamsOptions = [
                    'rel' => null,
                    'class' => "selectPaginated  {$class}",
                    'data-route' => route('admin.teams.filters', ['company_id' => $requestCompanyId]),
                    'data-id' => $requestTeamId,
                    'data-text' => optional(Team::query()->find($requestTeamId))->name,
                ];
                $options = array_merge($options, $teamsOptions);

                return $builder->select($name, $label, $list, $selected, $options);
            }
        );
    }

    private function bootSelectTeamOnEditTeamPage(AdminForm $builder): void
    {
        FormBuilder::macro(
            'selectTeamOnEditTeamPage',
            function (
                string $name,
                string|bool $label = null,
                array|Collection $list = [],
                string $selected = null,
                array $options = []
            ) use ($builder) {
                return $builder->select($name, $label, $list, $selected, $options);
            }
        );
    }

    private function bootSelectUser(AdminForm $builder): void
    {
        FormBuilder::macro(
            'selectUser',
            function (
                string $name,
                string|bool $label = null,
                array|Collection $list = [],
                string $selected = null,
                array $options = []
            ) use ($builder) {
                $requestCompanyId = request()->get('company_id');
                $requestUserId = $selected ?? request()->get('user_id');
                $class = data_get($options, 'class', '');
                $teamsOptions = [
                    'rel' => null,
                    'class' => "selectPaginated  {$class}",
                    'data-route' => route('admin.users.filters', ['company_id' => $requestCompanyId]),
                    'data-id' => $requestUserId,
                    'data-text' => optional(User::find($requestUserId))->name,
                ];
                $options = array_merge($options, $teamsOptions);

                return $builder->select($name, $label, $list, $selected, $options);
            }
        );
    }

    private function bootUppyFileMacro(HtmlBuilder $html): void
    {
        FormBuilder::macro(
            'uppyFile', function (string $name, array $options = [], bool $init = true) use ($html) {
                return '
<div class="' . ($init ? "UppyContainer" : "UppySkeleton") . '">
    <input class="form-control" type="text" id="' . $name . '" name="' . $name . '" ' . $html->attributes($options) . '>
    <div class="UppyInput"></div>
    <div class="UppyInput-Progress"></div>
</div>
            ';
            }
        );
    }
}
