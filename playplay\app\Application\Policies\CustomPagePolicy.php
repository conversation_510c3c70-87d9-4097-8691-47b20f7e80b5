<?php

declare(strict_types=1);

namespace App\Application\Policies;

use App\Models\CustomPage;
use App\Models\Project;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

final class CustomPagePolicy extends DefaultPolicy
{
    use HandlesAuthorization;

    private ProjectPolicy $projectPolicy;

    public function __construct(ProjectPolicy $policy)
    {
        $this->projectPolicy = $policy;
    }

    public function create(User $user, Project $project): bool
    {
        return $project->company->hasCustomPageFeature() === true && $this->projectPolicy->view($user, $project);
    }

    public function view(User $user, CustomPage $customPage): bool
    {
        return $customPage->project->company->hasCustomPageFeature() === true &&
            $this->projectPolicy->view($user, $customPage->project);
    }

    public function update(User $user, CustomPage $customPage): bool
    {
        return $customPage->project->company->hasCustomPageFeature() === true &&
            $this->projectPolicy->view($user, $customPage->project);
    }
}
