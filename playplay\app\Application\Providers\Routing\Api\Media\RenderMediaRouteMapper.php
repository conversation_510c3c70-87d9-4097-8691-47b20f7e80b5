<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Media;

use App\Application\Http\Controllers\Api\V2\Media\RenderMediaController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class RenderMediaRouteMapper implements RouteMapper
{
    public function map(): void
    {
        $this->mapRenderMediaCallbackRoutes();
    }

    private function mapRenderMediaCallbackRoutes(): void
    {
        Route::group([
            'prefix' => 'v2/renders/{renderMedia}',
            'as' => 'v2.renders.callback.',
        ], static function (Router $router) {
            $router
                ->post('/callback-raw-media', [RenderMediaController::class, 'callbackRawMedia'])
                ->name('raw-media');
            $router
                ->post('/callback-project-media', [RenderMediaController::class, 'callbackProcessedMedia'])
                ->name('project-media');
        });
    }
}
