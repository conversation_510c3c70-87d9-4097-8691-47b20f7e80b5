<?php

namespace App\Application\Http\Requests\Admin\User;

use Illuminate\Foundation\Http\FormRequest;

final class UserTeamsUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'company_id' => ['required', 'exists:companies,id'],
            'teams.*.id' => ['sometimes', 'exists:teams,id'],
            'teams.*.app_role_id' => ['sometimes', 'integer', 'exists:app_roles,id'],
        ];
    }
}
