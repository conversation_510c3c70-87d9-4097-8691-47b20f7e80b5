<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Auth;

use App\Application\Http\Controllers\Api\V3\Auth\LoginWithCaptchaController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class LoginWithCaptchaRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::post('login', LoginWithCaptchaController::class)
            ->middleware('user-from-date-not-reached')
            ->name('login');
    }
}
