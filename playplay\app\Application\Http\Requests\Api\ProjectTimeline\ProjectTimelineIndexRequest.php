<?php

namespace App\Application\Http\Requests\Api\ProjectTimeline;

use App\Domain\Project\ProcessedMedia\RelationType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProjectTimelineIndexRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules()
    {
        return [
            'relation_type_key' => [
                'required',
                Rule::in(
                    array_keys(RelationType::TYPES_AS_STRING)
                ),
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'relation_type_key' => $this->route('relationTypeKey'),
            ]
        );
    }
}
