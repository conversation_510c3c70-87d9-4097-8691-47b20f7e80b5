<?php

declare(strict_types=1);

namespace App\Application\Observers;

use App\Domain\User\Action\UserAction;
use App\Domain\User\Action\UserActionService;
use App\Models\CustomPage;

final class CustomPageObserver
{
    private UserActionService $userActionService;

    public function __construct(UserActionService $userActionService)
    {
        $this->userActionService = $userActionService;
    }

    public function created(CustomPage $customPage): void
    {
        $this->userActionService->addUserAction(
            new UserAction(
                'custom-page-created',
                [],
                $customPage->project->team_id,
                $customPage->project_id,
            )
        );
    }
}
