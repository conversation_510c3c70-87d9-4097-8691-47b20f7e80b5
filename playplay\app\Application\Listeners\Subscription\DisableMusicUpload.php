<?php

declare(strict_types=1);

namespace App\Application\Listeners\Subscription;

use App\Application\Events\SubscriptionCreated;
use App\Domain\TeamPreset\TeamPresetRepository;

final class DisableMusicUpload
{
    private TeamPresetRepository $teamPresetRepository;

    public function __construct(TeamPresetRepository $teamPresetRepository)
    {
        $this->teamPresetRepository = $teamPresetRepository;
    }

    public function handle(SubscriptionCreated $event): void
    {
        $subscription = $event->getSubscription();

        $this->teamPresetRepository->disableMusicUploadForAllTeamsOfCompany($subscription->company);
    }
}
