<?php

declare(strict_types=1);

namespace App\Application\Events;

use App\Models\Category as TemplateCategory;

final class NewGenericTemplateCategoryIsCreated
{
    private TemplateCategory $genericTemplateCategory;

    public function __construct(TemplateCategory $templateCategory)
    {
        $this->genericTemplateCategory = $templateCategory;
    }

    public function getGenericTemplateCategory(): TemplateCategory
    {
        return $this->genericTemplateCategory;
    }
}
