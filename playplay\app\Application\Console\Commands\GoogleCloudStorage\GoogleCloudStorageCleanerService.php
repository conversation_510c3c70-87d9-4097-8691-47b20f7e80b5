<?php

declare(strict_types=1);

namespace App\Application\Console\Commands\GoogleCloudStorage;

use Google\Cloud\Storage\StorageClient;
use Google\Cloud\Storage\StorageObject;
use Illuminate\Config\Repository;

class GoogleCloudStorageCleanerService implements StorageCleanerInterface
{
    /** @var StorageClient */
    private $storageClient;
    /** @var string */
    private $bucket;

    public function __construct(StorageClient $storageClient, Repository $configRepository)
    {
        $this->storageClient = $storageClient;
        $this->bucket = $configRepository->get('filesystems.disks.gcs.bucket');
    }

    /**
     * @throws DeleteFilesException
     */
    public function deleteFilesIn(string $path): bool
    {
        try {
            $objectFiles = $this->objectFilesIn($path);

            /** @var StorageObject $objectFile */
            foreach ($objectFiles as $objectFile) {
                $objectFile->delete();
            }

            return true;
        } catch (\Throwable $e) { // I don't know which error or exception is thrown, so I catched the univers
            throw new DeleteFilesException("Cannot delete file in path ({$path}). {$e->getMessage()}", 0, $e);
        }
    }

    private function objectFilesIn(string $path): \Iterator
    {
        return $this->storageClient->bucket($this->bucket)->objects(['prefix' => $path]);
    }
}
