<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api;

use App\Application\Http\Controllers\Api\V2\SignUpController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Support\Facades\Route;

final class SignupRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::post('/sign-up', [SignUpController::class, 'store'])->name('sign-up.store');
        Route::get('/sign-up/email-valid', [SignUpController::class, 'emailValid'])->name('sign-up.email-valid');
    }
}
