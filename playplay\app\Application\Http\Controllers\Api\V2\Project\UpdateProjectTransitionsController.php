<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\Project;

use App\Application\Events\ProjectUpdated;
use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\Project\ProjectTransitionsRequest;
use App\Models\Project;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

final class UpdateProjectTransitionsController extends BaseController
{
    use AuthorizesRequests;

    private Dispatcher $eventDispatcher;

    public function __construct(Dispatcher $eventDispatcher)
    {
        $this->eventDispatcher = $eventDispatcher;
    }

    public function __invoke(Project $project, ProjectTransitionsRequest $request): JsonResponse
    {
        $this->authorize('update', [$project]);
        $project->projectScreens()->update(['has_transition_after' => $request->input('activate_transitions')]);
        $this->eventDispatcher->dispatch(new ProjectUpdated($project));

        return $this->sendJsonResponse(new Collection([]), Response::HTTP_OK);
    }
}
