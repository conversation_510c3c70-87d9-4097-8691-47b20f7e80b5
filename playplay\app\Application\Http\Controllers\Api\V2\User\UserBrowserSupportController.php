<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Api\V2\User;

use App\Application\Http\Controllers\Api\BaseController;
use App\Application\Http\Requests\Api\User\UserBrowserSupportStoreRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;

class UserBrowserSupportController extends BaseController
{
    public function store(UserBrowserSupportStoreRequest $request): JsonResponse
    {
        return $this->sendJsonResponse(new Collection([]), Response::HTTP_CREATED);
    }
}
