<?php

namespace App\Application\Rules;

use App\Domain\CutawayShot\Validators\CutawayShotItemDurationRuleValidator;
use Illuminate\Contracts\Validation\Rule;

final class CutawayShotTimelineItemsDurationRule implements Rule
{
    private CutawayShotItemDurationRuleValidator $cutawayShotItemDurationRuleValidator;

    public function __construct(
        CutawayShotItemDurationRuleValidator $cutawayShotItemDurationRuleValidator
    ) {
        $this->cutawayShotItemDurationRuleValidator = $cutawayShotItemDurationRuleValidator;
    }

    public function message(): string
    {
        return trans('validation.cutaway_shot_item_duration');
    }

    public function passes($attribute, $value, $parameters = [], $validator = null): bool
    {
        $items = $validator->getData()['items'] ?? null;

        if (!is_array($items)) {
            return false;
        }

        foreach ($validator->getData()['items'] as $itemData) {
            if (!$this->cutawayShotItemDurationRuleValidator->isValid($itemData)) {
                return false;
            }
        }

        return true;
    }
}
