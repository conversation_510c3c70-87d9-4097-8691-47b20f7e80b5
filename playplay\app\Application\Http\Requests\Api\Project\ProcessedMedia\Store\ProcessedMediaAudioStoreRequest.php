<?php

namespace App\Application\Http\Requests\Api\Project\ProcessedMedia\Store;

use App\Domain\Music\MusicListRepository;
use App\Domain\Project\RawMedia\RelationType;
use App\Domain\RawMedia\RawMediaSource;
use App\Models\ProcessedMedia;
use App\Models\Project;
use App\Models\ProjectRawMedia;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;
use Illuminate\Routing\Route;
use Illuminate\Validation\Factory;
use Illuminate\Validation\Rule;

class ProcessedMediaAudioStoreRequest extends FormRequest
{
    private MusicListRepository $musicListRepository;

    public function __construct(MusicListRepository $musicListRepository)
    {
        $this->musicListRepository = $musicListRepository;
        $validationFactory = app(Factory::class);
        $this->addIsValidRawMediaRelationTypeRule($validationFactory);
        parent::__construct();
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'project_id' => ['required', 'exists:projects,id'],
            'raw_media_id' => ['required', 'isValidRawMediaRelationType'],
            'source' => [
                'required',
                Rule::in([
                    RawMediaSource::UPLOAD,
                    ProcessedMedia::SOURCE_OPTION_LIST,
                ]),
            ],
            'start' => ['required', 'numeric', 'min:0'],
            'trim_start' => ['required', 'numeric', 'min:0'],
            'trim_end' => ['required', 'numeric', 'greater_than:trim_start'],
        ];
    }

    private function addIsValidRawMediaRelationTypeRule(Factory $validationFactory): void
    {
        $localOptionMusicRepository = $this->musicListRepository;

        $validationFactory->extendImplicit(
            'isValidRawMediaRelationType',
            static function () use ($localOptionMusicRepository) {
                /** @var Request $request */
                $request = request();
                $projectId = $request->get('project_id');
                $rawMediaId = $request->get('raw_media_id');
                if ($request->get('source') === RawMediaSource::UPLOAD) {
                    /** @var Route $route */
                    $route = $request->route();
                    $relationType = RelationType::fromKey(
                        $route->parameter('relationTypeKey')
                    );

                    return ProjectRawMedia::query()->where('raw_media_id', $rawMediaId)
                        ->where('project_id', '=', $projectId)
                        ->where('relation_type', '=', $relationType)
                        ->exists();
                }

                /** @var Project $project */
                $project = Project::query()->find($projectId);

                return $localOptionMusicRepository->isRawMediaInMusicLists(
                    $rawMediaId,
                    $project->preset->musicListsMusics->pluck('id')->all()
                );
            }
        );
    }
}
