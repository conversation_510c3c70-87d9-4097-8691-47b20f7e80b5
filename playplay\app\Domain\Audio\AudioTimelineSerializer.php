<?php

namespace App\Domain\Audio;

use App\Domain\Project\ProcessedMedia\RelationType;
use App\Models\ProcessedMedia;

class AudioTimelineSerializer
{
    public function serialize(RelationType $relationType, array $processedMedias): array
    {
        $filteredProcessedMedias = array_values(
            array_filter($processedMedias, static function (ProcessedMedia $processedMedia) use ($relationType): bool {
                return $processedMedia->relation_type === $relationType;
            })
        );

        return array_map(
            static function (ProcessedMedia $processedMedia): array {
                $data = $processedMedia->data ?? [];
                $rawMedia = $processedMedia->rawMedia;

                return [
                    'processed_media_id' => $processedMedia->id,
                    'source' => $processedMedia->source,
                    'name' => $rawMedia->name,
                    'start' => $data['start'] ?? 0,
                    'trim_start' => $data['trimStart'] ?? 0,
                    'trim_end' => $data['trimEnd'] ?? $processedMedia->getDuration(),
                    'raw_media' => [
                        'id' => $rawMedia->id,
                        'duration' => $rawMedia->duration,
                        'peaks_url' => $rawMedia->audio_peaks_data_url,
                        'url' => $rawMedia->browser_url,
                    ],
                ];
            },
            $filteredProcessedMedias
        );
    }
}
