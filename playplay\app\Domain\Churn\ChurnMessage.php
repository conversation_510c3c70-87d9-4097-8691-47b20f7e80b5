<?php

declare(strict_types=1);

namespace App\Domain\Churn;

use App\Domain\Billing\SubscriptionDuration;
use App\Domain\Billing\SubscriptionRevenue;
use App\Models\Company;

final class ChurnMessage
{
    public readonly string $content;

    private Company $company;

    public function __construct(
        Company $company,
        SubscriptionRevenue $subscriptionRevenue,
        SubscriptionDuration $subscriptionDuration,
    ) {
        $this->company = $company;
        $churnAuthorEmail = $company->lastChurn->user->email;
        $comment = $company->lastChurn->comment ?? 'N/A';

        $this->content = <<<EOD
*ID Card Churn*
- Company ID: {$company->id}
- Email: {$churnAuthorEmail}
- ARR: {$subscriptionRevenue->getFormattedAnnualRecurringRevenue()}
- MRR: {$subscriptionRevenue->getFormattedMonthlyRecurringRevenue()}
- Duration of subscription: {$subscriptionDuration->duration}
- End date: {$subscriptionDuration->endDate}
- Why they left: "{$comment}"
EOD;
    }

    public function isButterflyChurn(): bool
    {
        return $this->company->type === Company::TYPE_BUTTERFLY;
    }
}
