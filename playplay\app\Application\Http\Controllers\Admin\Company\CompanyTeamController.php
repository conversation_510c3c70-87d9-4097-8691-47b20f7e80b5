<?php

declare(strict_types=1);

namespace App\Application\Http\Controllers\Admin\Company;

use App\Application\Http\Controllers\Admin\BaseController;
use App\Application\Http\Requests\Admin\Company\CompanyTeamOrderUpdateRequest;
use App\Domain\Company\CompanyService;
use App\Models\Company;
use Illuminate\Http\JsonResponse;

final class CompanyTeamController extends BaseController
{
    private CompanyService $companyService;

    public function __construct(CompanyService $companyService)
    {
        $this->companyService = $companyService;
    }

    public function updateOrder(CompanyTeamOrderUpdateRequest $request, Company $company): JsonResponse
    {
        $this->authorize('canAccessRestrictedData', $company);
        $this->authorize('update', $company);
        $this->companyService->updateCompanyTeamsOrder($company, $request->input('team_orders'));

        return new JsonResponse();
    }
}
