<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Admin\TimecodedElement;

use Illuminate\Foundation\Http\FormRequest;

final class TimecodedElementsFamilyStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'backoffice_name' => ['required', 'string'],
            'type' => ['bail', 'required', 'string', 'isValidTimecodedElementsFamilyType'],
            'is_generic' => ['required', 'boolean'],
        ];
    }
}
