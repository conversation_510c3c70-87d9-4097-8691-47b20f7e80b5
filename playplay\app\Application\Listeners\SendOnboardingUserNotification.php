<?php

declare(strict_types=1);

namespace App\Application\Listeners;

use App\Application\Events\AdminCreatedUser;
use App\Domain\User\Onboarding\NotificationService;

final class SendOnboardingUserNotification
{
    private NotificationService $userOnboardingNotificationService;

    public function __construct(NotificationService $userOnboardingNotificationService)
    {
        $this->userOnboardingNotificationService = $userOnboardingNotificationService;
    }

    public function handle(AdminCreatedUser $event): void
    {
        $this->userOnboardingNotificationService->sendOnboardingNotification($event->getUser());
    }
}
