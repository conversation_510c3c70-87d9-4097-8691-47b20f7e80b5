<?php

declare(strict_types=1);

namespace App\Domain\CustomPage;

use App\Domain\CustomPage\Exception\UnableToGetDefaultFontException;
use App\Domain\ProcessedMedia\Repository\ProcessedMediaRepository;
use App\Models\CustomPage;
use App\Models\Project;

final class CustomPageFactory
{
    private CustomPageRepository $customPageRepository;
    private CustomPageTokenGeneratorInterface $tokenGenerator;
    private DefaultColorsRepository $defaultColorsRepository;
    private DefaultFontRepository $defaultFontRepository;
    private ProcessedMediaRepository $processedMediaRepository;

    public function __construct(
        CustomPageRepository $customPageRepository,
        CustomPageTokenGeneratorInterface $tokenGenerator,
        DefaultColorsRepository $defaultColorsRepository,
        DefaultFontRepository $defaultFontRepository,
        ProcessedMediaRepository $processedMediaRepository,
    ) {
        $this->customPageRepository = $customPageRepository;
        $this->tokenGenerator = $tokenGenerator;
        $this->defaultColorsRepository = $defaultColorsRepository;
        $this->defaultFontRepository = $defaultFontRepository;
        $this->processedMediaRepository = $processedMediaRepository;
    }

    /**
     * @throws ProjectDoesNotHaveRenderHtmlUrlException
     * @throws UnableToGetDefaultFontException
     */
    public function createFromProject(Project $project): CustomPage
    {
        if ($project->last_rendered_url === null) {
            throw new ProjectDoesNotHaveRenderHtmlUrlException();
        }

        $preset = $project->team->presets->first();
        $primaryColor = $preset->getDefaultMainColor() ?: $this->defaultColorsRepository->getDefaultMainColor();
        $fontId = $preset->getFirstDefaultFontId() ?: $this->defaultFontRepository->getDefaultFont()->id;
        $logoProcessedMediaId = $preset->getFirstPrimarySquareLogoProcessedMediaId();

        if ($logoProcessedMediaId !== null
            && $this->processedMediaRepository->getById($logoProcessedMediaId) === null
        ) {
            $logoProcessedMediaId = null;
        }

        $createDTO = new CreateCustomPageDTO(
            $this->tokenGenerator->generate(),
            $project->id,
            $project->title,
            $primaryColor,
            $fontId,
            $logoProcessedMediaId
        );

        return $this->customPageRepository->create($createDTO);
    }
}
