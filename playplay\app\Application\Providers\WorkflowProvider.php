<?php

declare(strict_types=1);

namespace App\Application\Providers;

use App\Domain\Workflow\Config\MediaImageWorkflow;
use App\Domain\Workflow\WorkflowInterface;
use App\Domain\Queueing\QueueMetadataService;
use App\Domain\Render\RenderScreen\RenderScreenService;
use App\Domain\RenderJob\RenderJobRepository;
use App\Services\Processing\ProcessingQueue;
use App\Services\Rendering\RenderingQueue;
use App\Services\Rendering\WorkflowManager;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Support\ServiceProvider;

class WorkflowProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(WorkflowInterface::class, MediaImageWorkflow::class);
        $this->app->bind(
            WorkflowManager::class,
            static fn(Application $app) => new WorkflowManager(
                $app->make(RenderingQueue::class),
                $app->make(ProcessingQueue::class),
                $app->make(RenderJobRepository::class),
                $app->make(RenderScreenService::class),
                $app->makeWith(QueueMetadataService::class, ['queueName' => 'queue_rendering']),
            )
        );
    }
}
