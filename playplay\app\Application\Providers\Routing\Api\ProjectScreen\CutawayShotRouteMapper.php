<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\ProjectScreen;

use App\Application\Http\Controllers\Api\V2\ProjectScreen\Param\CutawayShot\CreateCutawayShotImageController;
use App\Application\Http\Controllers\Api\V2\ProjectScreen\Param\CutawayShot\CreateCutawayShotVideoController;
use App\Application\Http\Controllers\Api\V2\ProjectScreen\Param\CutawayShot\CutawayShotTimelineController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class CutawayShotRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'prefix' => 'project-screens/{projectScreen}/params',
            'as' => 'project-screens.params.cutaway-shot-',
        ], static function (Router $router) {
            $router->get('{layoutParam}/cutaway-shot-timeline', [CutawayShotTimelineController::class, 'index'])
                ->name('timeline.index');
            $router->put('{param}/cutaway-shot-timeline', [CutawayShotTimelineController::class, 'update'])
                ->name('timeline.update');
        });

        Route::group([
            'prefix' => 'project-screens/{project_screen}/params',
            'as' => 'project-screens.params.cutaway-shot-',
        ], static function (Router $router) {
            $router->post('{param}/cutaway-shot-images', CreateCutawayShotImageController::class)->name('images.store');
            $router->post('{param}/cutaway-shot-videos', CreateCutawayShotVideoController::class)->name('videos.store');
        });
    }
}
