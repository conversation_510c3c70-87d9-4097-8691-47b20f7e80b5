<?php

declare(strict_types=1);

namespace App\Application\Providers\Routing\Api\Team;

use App\Application\Http\Controllers\Api\V2\Team\TeamFolderController;
use App\Application\Providers\Routing\RouteMapper;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

final class TeamFolderRouteMapper implements RouteMapper
{
    public function map(): void
    {
        Route::group([
            'as' => 'teams.folders.',
            'prefix' => '/teams/{team}/folders'
        ], static function (Router $router) {
            $router->get('/', [TeamFolderController::class, 'index'])->name('index');
            $router->post('/', [TeamFolderController::class, 'store'])->name('store');
            $router->put('/{folder}', [TeamFolderController::class, 'update'])->name('update');
            $router->delete('/{folder}', [TeamFolderController::class, 'destroy'])->name('destroy');
        });
        Route::put('/teams/{team}/move-folders', [TeamFolderController::class, 'move'])->name('teams.folders.move');
    }
}
