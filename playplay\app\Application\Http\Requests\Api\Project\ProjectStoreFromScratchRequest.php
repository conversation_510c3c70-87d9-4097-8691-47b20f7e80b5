<?php

declare(strict_types=1);

namespace App\Application\Http\Requests\Api\Project;

final class ProjectStoreFromScratchRequest extends AbstractProjectStoreFromRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'preset_id' => ['required', 'presetAvailableForUser'],
            'format' => ['required', 'isValidFormat'],
            'outro_id' => ['sometimes', 'int'],
        ];
    }

    protected function isFormatAvailableForUser(string $format): bool
    {
        return true;
    }
}
